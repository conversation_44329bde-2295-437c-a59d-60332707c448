package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 应用类型(字典0124)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-26
 */
@Getter
@AllArgsConstructor
public enum MessageChatEnum implements IEnumType<String> {

    MEDIA_ASSETS("0124-1", "媒资工作台"),
    ABNORMAL_CONTRACT("0124-2", "异常合同"),
    ABNORMAL_NOTICE("0124-3", "异常通知"),
    CS_APPROVAL_CENTER("0124-4", "创视审批中心"),
    SALE_WORKBENCH("0124-5", "销售工作台");

    private final String code;
    private final String desc;

    private static final Map<String, MessageChatEnum> BY_CODE_MAP =
            Arrays.stream(MessageChatEnum.values())
                    .collect(Collectors.toMap(MessageChatEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static MessageChatEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static MessageChatEnum parse(String code, MessageChatEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(MessageChatEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 