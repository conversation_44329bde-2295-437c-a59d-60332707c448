package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Accessors(chain = true)
@Schema(name = "InnerProductApproveDetailVO", description = "站内审批任务产品线变更列表详情返回参数")
public class InnerProductApproveDetailVO {

    @Schema(description = "产品线变更id")
    private Long id;

    @Schema(description = "审批任务名称")
    private String approvalName;

    @Schema(description = "申请人")
    @TransField(type = TransTypes.USER)
    private Integer instanceUserId;
    private String instanceUserName;

    @Schema(description = "申请部门")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime instanceCreateTime;

    @Schema(description = "完成时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime endTime;

    @Schema(description = "审批结果，字典(0138)")
    @TransField(type = TransTypes.DICT)
    private String approvalResult;
    private String approvalResultName;

    @Schema(description = "品牌")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY, target = "signSubjectName")
    private Integer companyId;
    private String signSubjectName;

    @Schema(description = "当前商机进度(字典0073)")
    @TransField(type = TransTypes.DICT)
    private String progress;
    private String progressName;

    @Schema(description = "变更前产品线")
    private String beforeProductLine;

    @Schema(description = "变更后产品线")
    private String afterProductLine;

    @Schema(description = "情况说明")
    private String remark;
}
