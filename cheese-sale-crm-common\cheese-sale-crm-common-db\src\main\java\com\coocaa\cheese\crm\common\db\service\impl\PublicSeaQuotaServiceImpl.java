package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaQuotaEntity;
import com.coocaa.cheese.crm.common.db.mapper.PublicSeaQuotaMapper;
import com.coocaa.cheese.crm.common.db.service.IPublicSeaQuotaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 公海配额服务实现类
 *
 * <AUTHOR>
 * @since 2025/4/30
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PublicSeaQuotaServiceImpl
        extends ServiceImpl<PublicSeaQuotaMapper, PublicSeaQuotaEntity>
        implements IPublicSeaQuotaService {
}
