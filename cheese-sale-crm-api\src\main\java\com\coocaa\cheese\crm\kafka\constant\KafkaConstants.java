package com.coocaa.cheese.crm.kafka.constant;

/**
 * Kafka 常量定义类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-05
 */
public class KafkaConstants {

    /**
     * ContainerFactory:单条消费
     */
    public static final String KAFKA_LISTENER_CONTAINER_FACTORY_ONE = "kafkaListenerContainerFactoryOne";
    /**
     * ContainerFactory:批量消费
     */
    public static final String KAFKA_LISTENER_CONTAINER_FACTORY_BATCH = "kafkaListenerContainerFactoryBatch";
    /**
     * ID: 合同状态变更ID
     */
    public static final String ID_CONTRACT_STATUS_CHANGE = "id_cheese_sale_contract_status_change";
    /**
     * TOPIC: 合同状态变更
     */
    public static final String TOPIC_CONTRACT_STATUS_CHANGE = "cheese_sale_contract_status_change";
    /**
     * TOPIC: 归属人变更
     */
    public static final String TOPIC_BUSINESS_OWNER_CHANGE = "cheese_sale_business_owner_change";
    /**
     * TOPIC: 主体变更
     */
    public static final String TOPIC_ADVERTISING_SUBJECT_CHANGE = "cheese_sale_advertising_subject_change";

    /**
     * ID: 方案转扣费topic id
     */
    public static final String ID_PLAN_TRANSFER_DEDUCTION = "id_plan_transfer_deduction";
    /**
     * TOPIC: 方案转扣费topic
     */
    public static final String TOPIC_PLAN_TRANSFER_DEDUCTION = "plan-end-inform-sale-crm-topic";

}
