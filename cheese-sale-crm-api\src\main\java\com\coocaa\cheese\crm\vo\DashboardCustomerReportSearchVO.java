package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 客户报备搜索
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-14
 */
@Data
public class DashboardCustomerReportSearchVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "品牌ID", type = "Integer", example = "1")
    private Integer brandId;

    @Schema(description = "品牌名称", type = "String")
    private String brandName;

    @Schema(description = "所属公司ID", type = "Integer", example = "1")
    private Integer companyId;

    @Schema(description = "所属公司名称", type = "String")
    private String companyName;

    @Schema(description = "管理部门ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "创建人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer creator;
    private String creatorName;

    @Schema(description = "创建时间", type = "String")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "生效状态(1:生效,0:不生效)", type = "Integer", example = "1")
    private Integer effectiveStatus;

    @Schema(description = "产品线")
    private String productLine;

    @Schema(description = "是否删除", type = "Integer", example = "0")
    private Integer deleteFlag;

    @Schema(description = "是否创建商机(1是，0否)", type = "Integer", example = "1")
    private Integer isCreateBusiness;
}
