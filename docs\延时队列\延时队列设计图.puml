@startuml RedissonDelayQueue

skinparam backgroundColor white
skinparam handwritten false

title Redisson延时队列设计图

participant "业务层" as Business
participant "消息生产者\nMessageProducer" as Producer
participant "Redis\n延时队列" as RedisQueue
participant "定时任务\nDelayQueueConsumerTask" as Consumer
participant "消息处理器工厂\nMessageHandlerFactory" as Factory
participant "具体消息处理器\nTaskMessageHandler" as Handler

== 消息发送流程 ==

Business -> Producer : 调用sendMessage(queueEnum, content)
note right
可通过Controller接口或直接调用
传入队列类型和消息内容
end note

activate Producer
Producer -> Producer : 创建DelayedMessage对象
note right
- 生成消息ID (UUID)
- 设置消息类型
- 设置消息内容
- 设置创建时间
- 设置计划执行时间
end note

Producer -> RedisQueue : 发送消息到延时队列
note right
offer(message, delay, timeUnit)
- message: 消息对象
- delay: 延时时间
- timeUnit: 时间单位
end note
deactivate Producer

RedisQueue -> RedisQueue : 将消息存入Redis
note right
- 使用Redisson的RDelayedQueue
- 按指定延时存储消息
end note

== 消息消费流程 ==

Consumer -> Consumer : 定时执行processQueues方法
note left
- 通过XXL-Job调度执行
- 可指定消息类型或处理所有类型
end note

activate Consumer
Consumer -> RedisQueue : 获取已到期消息
note right
poll()方法获取队头消息
如果队列为空则返回null
end note

RedisQueue --> Consumer : 返回DelayedMessage对象

Consumer -> Factory : 获取消息处理器
note right
getHandler(messageType)
根据消息类型获取对应处理器
end note

Factory --> Consumer : 返回对应的MessageHandler实现

Consumer -> Handler : 调用handle(message)方法处理消息
note right
具体业务逻辑在各处理器中实现
例如TaskMessageHandler处理主体公海消息
end note

activate Handler
Handler -> Handler : 处理业务逻辑
deactivate Handler

deactivate Consumer

== 系统组件说明 ==

note right of Business
业务层可通过多种方式发送延时消息:
1. Controller接口调用
2. 直接注入MessageProducer调用
end note

note right of RedisQueue
延时队列特点:
- 基于Redis的可靠消息队列
- 支持消息的延时投递
- 支持多种业务类型消息
end note

note right of Factory
消息处理器工厂特点:
- 自动发现并注册所有MessageHandler实现
- 使用Map缓存处理器实例提高性能
- 支持根据消息类型获取对应处理器
end note

note right of Handler
消息处理器特点:
- 遵循接口设计便于扩展
- 每个处理器负责一种消息类型
- 通过Spring自动注入相关依赖
end note

@enduml 