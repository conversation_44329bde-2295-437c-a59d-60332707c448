package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 站内审批任务申请列表详情返回参数
 * <AUTHOR>
 * @since 2025-06-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Schema(name = "InnerApprovePublicSeaAuthDetailVO", description = "站内审批任务申请列表详情返回参数")
public class InnerApprovePublicSeaAuthDetailVO extends BaseInnerApproveDetailVO {

    @Schema(description = "实际业务id")
    private Integer bizId;

    @Schema(description = "签约主体类型(与品牌的关系，字典0068)")
    @TransField(type = TransTypes.DICT)
    private String type;
    private String typeName;

    @Schema(description = "品牌")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;

    @Schema(description = "所属行业")
    @TransField(type = TransTypes.INDUSTRY)
    private String industryCode;
    private String industryName;

    @Schema(description = "品牌别名")
    private String brandTag;

    @Schema(description = "是否TOP客户 [0:否, 1:是]")
    private Integer topFlag;

    @Schema(description = "备注")
    private String description;

    @Schema(description = "情况说明", maxLength = 100)
    private String remark;

    @Schema(description = "产品线", type = "String", example = "1")
    private String productLineNames;

}
