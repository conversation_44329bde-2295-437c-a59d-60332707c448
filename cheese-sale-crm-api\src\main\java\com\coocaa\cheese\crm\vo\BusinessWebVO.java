package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 商机信息web页面VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessWebVO", description = "商机信息web页面VO")
public class BusinessWebVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "商机编码", example = "BIZ001")
    private String code;

    @Schema(description = "品牌ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;

    @Schema(description = "品牌名称", type = "String", example = "创维")
    private String brandName;

    @Schema(description = "渠道ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.CHANNEL)
    private Integer channelId;

    @Schema(description = "渠道名称", type = "String", example = "直营渠道")
    private String channelName;

    @Schema(description = "渠道别名", type = "String", example = "渠道1")
    private String channelAlias;

    @Schema(description = "归属人ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer ownerId;
    private String ownerName;

    @Schema(description = "管理部门ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "商机进度(字典0073)", type = "String", example = "0073-1")
    @TransField(type = TransTypes.DICT)
    private String progress;
    private String progressName;

    @Schema(description = "商机状态(字典0074)", type = "String", example = "0074-1")
    @TransField(type = TransTypes.DICT)
    private String status;
    private String statusName;

    @Schema(description = "签约主体ID", type = "Integer", example = "1")
    private Integer advertisingSubjectId;

    @Schema(description = "是否TOP客户 [0:否, 1:是]", type = "Integer", example = "1")
    private Integer topFlag;

    @Schema(description = "签约主体公司ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;

    @Schema(description = "签约主体名称-关联查询的公司名称", type = "String", example = "创维集团")
    private String companyName;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "创建时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "签约主体状态集合", type = "String", example = "1,2,3")
    private String advertisingStatusSet;

} 