package com.coocaa.cheese.crm.common.tools.config.api;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 内部审批配置
 *
 * <AUTHOR>
 * @since 2025/5/7
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "inner.approve.rule.code")
public class InnerApproveConfig {
    /**
     * 签约主体报备
     */
    private Integer report;

    /**
     * 延期申请
     */
    private Integer release;

    /**
     * 打捞授权
     */
    private Integer sea;

    /**
     * 产品线变更
     */
    private Integer product;

    /**
     * pk挑战
     */
    private Integer pk;
}
