package com.coocaa.cheese.crm.rpc.vo;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
public class InnerApproveTaskVO {

    @Schema(description = "任务ID")
    private Integer id;

    @Schema(description = "规则编号")
    private Integer ruleCode;

    @Schema(description = "审批实例code")
    private String instanceCode;

    @Schema(description = "审批节点表ID")
    private Integer nodeId;

    @Schema(description = "审批人员ID")
    private Integer userId;

    @Schema(description = "审批单提交人部门ID")
    private String departId;

    @Schema(description = "审批结果，字典0138")
    private String approvalResult;

    @Schema(description = "审批意见")
    private String comment;

    @Schema(description = "任务状态，字典0139")
    private String nodeStatus;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "任务开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "任务结束时间")
    private LocalDateTime endTime;

    @Schema(description = "审批任务名称")
    private String approvalName;

    @Schema(description = "审批实例创建时间")
    private LocalDateTime instanceCreateTime;

    @Schema(description = "审批实例创建人")
    private Integer instanceUserId;
}
