package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售控制目标类型枚举
 * 按部门, 按品牌, 按渠道
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Getter
@AllArgsConstructor
public enum ControlTargetTypeEnum implements IEnumType<String> {
    BY_DEPARTMENT("0069-1", "按部门"),
    BY_BRAND("0069-2", "按品牌"),
    BY_CHANNEL("0069-3", "按渠道");

    private final String code;
    private final String desc;

    private static final Map<String, ControlTargetTypeEnum> BY_CODE_MAP =
            Arrays.stream(ControlTargetTypeEnum.values())
                    .collect(Collectors.toMap(ControlTargetTypeEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static ControlTargetTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static ControlTargetTypeEnum parse(String code, ControlTargetTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ControlTargetTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 