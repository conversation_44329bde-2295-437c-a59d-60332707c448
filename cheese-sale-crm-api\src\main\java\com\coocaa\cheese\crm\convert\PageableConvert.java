package com.coocaa.cheese.crm.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.common.result.PageResponseVO;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 分页转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02
 */
public interface PageableConvert<T, R> {
    /**
     * 转换成分页结果
     */
    @Mappings({
            @Mapping(source = "current", target = "currentPage"),
            @Mapping(source = "total", target = "totalRows"),
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "pages", target = "totalPages"),
            @Mapping(source = "records", target = "rows")
    })
    PageResponseVO<R> toPageResponse(IPage<T> pagedRows);
}
