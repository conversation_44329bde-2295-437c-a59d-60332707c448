package com.coocaa.cheese.crm.controller.approve;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.InnerApproveApplyQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.service.SubjectApproveService;
import com.coocaa.cheese.crm.vo.InnerApproveApplyPageVO;
import com.coocaa.cheese.crm.vo.InnerApproveDetailVO;
import com.coocaa.cheese.crm.vo.InnerApproveTaskPageVO;
import com.coocaa.cheese.crm.vo.ProductLineUnionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 签约主体站内审批业务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Slf4j
@RestController
@RequestMapping("/subject/examine/approve")
@Tag(name = "签约主体站内审批业务", description = "签约主体站内审批业务")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SubjectApproveController {

    private final SubjectApproveService subjectApproveService;

    @Operation(summary = "查询审批任务列表(分页)")
    @PostMapping("/task/page")
    public ResultTemplate<PageResponseVO<InnerApproveTaskPageVO>> pageApproveTask(@Validated @RequestBody PageRequestVO<InnerApproveTaskQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(InnerApproveTaskQueryParam::new));
        return ResultTemplate.success(subjectApproveService.pageApproveTask(pageRequest));
    }

    @Operation(summary = "查询审批信息详情")
    @Parameter(name = "id", description = "签约主体id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<InnerApproveDetailVO> queryApproveDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(subjectApproveService.queryApproveDetail(id));
    }

    @Operation(summary = "关联检查查询")
    @Parameter(name = "id", description = "签约主体id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}/union")
    public ResultTemplate<List<ProductLineUnionVO>> queryBrandUnionProductLine(@PathVariable("id") Integer id) {

        return ResultTemplate.success(subjectApproveService.queryBrandUnionProductLine(id));
    }

    @Operation(summary = "查询审批申请列表(分页)")
    @PostMapping("/apply/page")
    public ResultTemplate<PageResponseVO<InnerApproveApplyPageVO>> pageApplyApprove(@Validated @RequestBody PageRequestVO<InnerApproveApplyQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(InnerApproveApplyQueryParam::new));
        return ResultTemplate.success(subjectApproveService.pageApplyApprove(pageRequest));
    }
}
