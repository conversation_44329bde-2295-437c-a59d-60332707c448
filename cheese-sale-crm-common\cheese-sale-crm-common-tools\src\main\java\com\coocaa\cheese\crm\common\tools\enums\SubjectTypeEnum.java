package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售主体类型枚举
 * 品牌持有人, 品牌持有人分支机构, 授权代理商, 广告服务商, 授权经销商, 其它
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Getter
@AllArgsConstructor
public enum SubjectTypeEnum implements IEnumType<String> {
    BRAND_OWNER("0068-1", "品牌持有人"),
    BRAND_BRANCH("0068-2", "品牌持有人分支机构"),
    AUTHORIZED_AGENT("0068-3", "授权代理商"),
    AD_SERVICE_PROVIDER("0068-4", "广告服务商"),
    AUTHORIZED_DEALER("0068-5", "授权经销商"),
    OTHER("0068-6", "其它");

    private final String code;
    private final String desc;

    private static final Map<String, SubjectTypeEnum> BY_CODE_MAP =
            Arrays.stream(SubjectTypeEnum.values())
                    .collect(Collectors.toMap(SubjectTypeEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static SubjectTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static SubjectTypeEnum parse(String code, SubjectTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(SubjectTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 