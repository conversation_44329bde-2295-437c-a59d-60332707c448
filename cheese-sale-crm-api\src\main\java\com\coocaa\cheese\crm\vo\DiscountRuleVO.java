package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 折扣规则视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "折扣规则视图对象")
public class DiscountRuleVO {
    @Schema(description = "规则ID")
    private Integer id;

    @Schema(description = "折扣策略名称")
    private String name;

    @Schema(description = "折扣策略编码")
    private String code;

    @TransField(type = TransTypes.DICT)
    @Schema(description = "策略状态(字典0081)")
    private String status;
    private String statusName;

    @Schema(description = "适用城市")
    @TransField(type = TransTypes.CITY, target = "cityNames")
    private String cityIds;
    private String cityNames;

    @Schema(description = "创建人ID")
    @TransField(type = TransTypes.USER)
    private Integer creator;
    private String creatorName;

    @Schema(description = "生效时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime effectiveTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 