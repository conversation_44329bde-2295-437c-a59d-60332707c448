package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 机构资质查询参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构资质查询参数")
public class InstitutionQualificationQueryParam {
    @Schema(description = "机构账户ID", type = "Integer", example = "1")
    private Integer institutionId;

    @Schema(description = "资质名称", type = "String", example = "营业执照")
    private String qualificationName;
} 