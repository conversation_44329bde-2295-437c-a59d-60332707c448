
package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 产品线表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
public class ProductLineUnionDTO implements Serializable {

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 签约主体id
     */
    private Integer signSubjectId;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 产品线
     */
    private String productLine;
}