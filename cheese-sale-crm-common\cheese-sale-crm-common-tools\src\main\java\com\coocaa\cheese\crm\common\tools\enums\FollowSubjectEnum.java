package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 跟进主题枚举
 * 首次建联, 需求挖掘, 方案确认, 商务推动, 履约服务, 日常维护
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Getter
@AllArgsConstructor
public enum FollowSubjectEnum implements IEnumType<String> {
    FIRST_CONTACT("0072-1", "首次建联"),
    DEMAND_MINING("0072-2", "需求挖掘"),
    SOLUTION_CONFIRM("0072-3", "方案确认"),
    BUSINESS_PROMOTION("0072-4", "商务推动"),
    PERFORMANCE_SERVICE("0072-5", "履约服务"),
    DAILY_MAINTENANCE("0072-6", "日常维护");

    private final String code;
    private final String desc;

    private static final Map<String, FollowSubjectEnum> BY_CODE_MAP =
            Arrays.stream(FollowSubjectEnum.values())
                    .collect(Collectors.toMap(FollowSubjectEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static FollowSubjectEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static FollowSubjectEnum parse(String code, FollowSubjectEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(FollowSubjectEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 