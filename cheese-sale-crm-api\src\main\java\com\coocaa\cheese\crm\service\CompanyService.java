package com.coocaa.cheese.crm.service;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.util.AesUtils;
import com.coocaa.cheese.crm.bean.CompanyParam;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.entity.CompanyEntity;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBrandService;
import com.coocaa.cheese.crm.common.db.service.ICompanyService;
import com.coocaa.cheese.crm.common.db.service.IInstitutionAccountService;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.convert.CompanyConvert;
import com.coocaa.cheese.crm.vo.CompanyVO;
import com.coocaa.cheese.crm.vo.TianyanchaCompanyVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 公司/企业基础数据管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CompanyService {
    private final ICompanyService companyService;
    private final IBrandService brandService;
    private final IAdvertisingSubjectService advertisingSubjectService;
    private final IInstitutionAccountService institutionAccountService;

    @Value("${tianyancha.api.endpoint:http://open.api.tianyancha.com}")
    private String tianYanChaApiEndpoint;

    @Value("${tianyancha.api.token:0ff1a6293924934354ea16764dec8c342c2fdb9f51f6c42423c16078540b29c7523c61b666dd877c40ecc5de00136b36}")
    private String tianYanChaApiToken;


    /**
     * 企业列表(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的企业列表
     */
    public PageResponseVO<CompanyVO> pageList(@RequestBody PageRequestVO<CompanyParam> pageRequest) {
        String code = pageRequest.getQuery().getCode(), name = pageRequest.getQuery().getName();
        if (StringUtils.isNotBlank(code)) {
            code = AesUtils.encryptHex(code);
        }

        LambdaQueryWrapper<CompanyEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByDesc(CompanyEntity::getUpdateTime);
        queryWrapper.eq(CompanyEntity::getDeleteFlag, Optional.ofNullable(pageRequest.getQuery().getDeleteFlag()).orElse(BooleFlagEnum.NO.getCode()));
        queryWrapper.eq(StringUtils.isNotBlank(code), CompanyEntity::getCode, code);
        queryWrapper.eq(StringUtils.isNotBlank(pageRequest.getQuery().getSearchName()), CompanyEntity::getName, pageRequest.getQuery().getSearchName());
        queryWrapper.like(StringUtils.isNotBlank(name), CompanyEntity::getName, name);
        return CompanyConvert.INSTANCE.toPageResponse(companyService.page(getPage(pageRequest), queryWrapper));
    }

    /**
     * 企业列表(不分页)
     *
     * @param name 企业名称
     * @return 企业列表
     */
    public List<CodeNameVO> listCompanies(String name) {
        return companyService.lambdaQuery()
                .select(CompanyEntity::getId, CompanyEntity::getCode, CompanyEntity::getName)
                .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .like(StringUtils.isNotBlank(name), CompanyEntity::getName, name)
                .orderByDesc(CompanyEntity::getUpdateTime)
                .list().stream()
                .map(company -> CodeNameVO.builder()
                        .id(company.getId())
                        .code(company.getCode())
                        .name(company.getName())
                        .build())
                .toList();
    }

    /**
     * 根据公司ID查询列表
     *
     * @param ids 银行ID列表
     * @return 银行列表
     */
    public List<CodeNameVO> listByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        Set<Integer> uniqueIds = ids.stream().filter(Objects::nonNull).filter(id -> id > 0).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(uniqueIds)) {
            return Collections.emptyList();
        }

        return companyService.lambdaQuery()
                .select(CompanyEntity::getId, CompanyEntity::getName)
                .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(CompanyEntity::getId, uniqueIds)
                .list().stream()
                .map(item -> CodeNameVO.builder().id(item.getId()).name(item.getName()).build())
                .toList();
    }

    /**
     * 查询天眼查公司信息
     *
     * @param name     名字
     * @param pageNum  页码
     * @param pageSize 每页显示数量
     * @return 天眼查公司信息
     */
    public List<CodeNameVO> listTianyanchaCompanies(String name, Integer pageNum, Integer pageSize) {
        String apiUrl = String.format("%s/services/open/search/2.0?word=%s&pageSize=%s&pageNum=%s",
                tianYanChaApiEndpoint, URLEncoder.encode(name, Charset.defaultCharset()), pageSize, pageNum);
        HttpRequest request = HttpRequest.get(apiUrl).timeout((int) TimeUnit.SECONDS.toMillis(30));
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("Connection", "keep-alive");
        request.header("Authorization", AesUtils.decryptStr(tianYanChaApiToken));

        // 调用接口查询公司信息
        String response = request.execute().body();
        log.info("调用天眼查接口: {} \n\t返回: {}", apiUrl, response);
        JSONObject json = JSON.parseObject(response);
        if (Objects.isNull(json.get("result"))) {
            throw new BusinessException("天眼查返回结果为空");
        }
        if (!Objects.equals(json.get("reason"), "ok")) {
            throw new BusinessException("天眼查查询失败");
        }
        List<CodeNameVO> codeNames = new ArrayList<>();
        // 解析公司信息
        JSONObject data = json.getJSONObject("result");
        List<TianyanchaCompanyVO> companies = JSON.parseArray(JSON.toJSONString(data.get("items")), TianyanchaCompanyVO.class);
        if (CollectionUtils.isEmpty(companies)) {
            return Collections.emptyList();
        }
        for (TianyanchaCompanyVO company : companies) {
            if (!tianyanchaCheck(company)) {
                continue;
            }
            CodeNameVO codeNameVO = new CodeNameVO();
            // 检查数据库里面是否有同名公司信息,有就填充，没有就新增公司信息
            CompanyEntity queryCompanyEntity = companyService.lambdaQuery()
                    .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .eq(CompanyEntity::getName, company.getName())
                    .eq(CompanyEntity::getCode, AesUtils.encryptHex(company.getCreditCode()))
                    .one();
            if (Objects.nonNull(queryCompanyEntity)) {
                codeNameVO.setId(queryCompanyEntity.getId());
                codeNameVO.setName(queryCompanyEntity.getName());
                codeNameVO.setCode(queryCompanyEntity.getCode());
            } else {
                // 新增公司信息
                CompanyEntity companyEntity = new CompanyEntity();
                companyEntity.setName(company.getName());
                companyEntity.setNickName(company.getName());
                companyEntity.setCode(company.getCreditCode());
                companyService.save(companyEntity);
                codeNameVO.setCode(company.getCreditCode());
                codeNameVO.setName(company.getName());
                codeNameVO.setId(companyEntity.getId());
            }
            codeNames.add(codeNameVO);
        }

        return codeNames;
    }

    /**
     * 校验天眼查公司信息是否合法
     *
     * @param company
     */

    private boolean tianyanchaCheck(TianyanchaCompanyVO company) {
        if (StringUtils.isBlank(company.getName()) || StringUtils.isBlank(company.getCreditCode())) {
            return false;
        }
        return true;
    }

    /**
     * 企业详情
     *
     * @param id 企业ID
     * @return 企业详情
     */
    public CompanyVO getDetail(Integer id) {
        return Optional.ofNullable(companyService.getById(id)).map(CompanyConvert.INSTANCE::toVo).orElse(null);
    }

    /**
     * 企业新增或修改
     *
     * @param id    企业ID (为空表示新增)
     * @param param 企业信息
     * @return true: 析增或修改成功
     */
    public boolean createOrUpdate(Integer id, CompanyParam param) {
        // 检查企业名称重复
        if (StringUtils.isNotBlank(param.getName())) {
            boolean exists = companyService.lambdaQuery()
                    .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .ne(Objects.nonNull(id), CompanyEntity::getId, id)
                    .eq(CompanyEntity::getName, param.getName())
                    .exists();
            if (exists) {
                throw new BusinessException(param.getName() + "与已有数据重复");
            }
        }

        // 检查社会信用代码重复
        if (StringUtils.isNotBlank(param.getCode())) {
            boolean exists = companyService.lambdaQuery()
                    .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .ne(Objects.nonNull(id), CompanyEntity::getId, id)
                    .eq(CompanyEntity::getCode, AesUtils.encryptHex(param.getCode()))
                    .exists();
            if (exists) {
                throw new BusinessException(param.getCode() + "与已有数据重复");
            }
        }

        // 转换实体
        CompanyEntity entity = CompanyConvert.INSTANCE.toEntity(param);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());

        // 新增或修改
        if (Objects.isNull(id)) {
            return companyService.save(entity);
        } else {
            entity.setId(id);
            return companyService.updateById(entity);
        }
    }

    /**
     * 企业删除
     *
     * @param id 企业ID
     * @return true: 删除成功
     */
    public boolean delete(Integer id) {
        // 检查企业是否存在
        if (!companyService.lambdaQuery().eq(CompanyEntity::getId, id).exists()) {
            throw new BusinessException("企业不存在，删除失败");
        }

        // 品牌有使用
        boolean reference = brandService.lambdaQuery()
                .eq(BrandEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BrandEntity::getCompanyId, id).exists();
        if (reference) {
            throw new BusinessException("该企业已关联品牌，禁止删除");
        }

        // 签约主体有使用
        reference = advertisingSubjectService.lambdaQuery()
                .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(AdvertisingSubjectEntity::getCompanyId, id).exists();
        if (reference) {
            throw new BusinessException("该企业已关联签约主体，禁止删除");
        }

        // 机构账户有使用
        reference = institutionAccountService.lambdaQuery()
                .eq(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(InstitutionAccountEntity::getCompanyId, id).exists();
        if (reference) {
            throw new BusinessException("该企业已关联机构账户，禁止删除");
        }

        return companyService.lambdaUpdate()
                .set(CompanyEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(CompanyEntity::getId, id)
                .update();
    }

    /**
     * 获取分页对象
     */
    private Page<CompanyEntity> getPage(PageRequestVO<?> pageRequest) {
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE)
        );
    }
}
