package com.coocaa.cheese.crm.message;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.common.tools.config.api.ApiConfig;
import com.coocaa.cheese.crm.common.tools.enums.MessageChatEnum;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import com.coocaa.cheese.crm.rpc.bean.ChatMessageParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/14
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class FeishuNotificationService {

    private final ApiConfig apiConfig;
    private final Executor threadPoolExecutor;
    private final FeignAuthorityRpc feignAuthorityRpc;

    /**
     * 异步发送飞书消息
     * @param title, content
     * @return void
     **/
    public void sendAsyncNotification(String title, String content) {
        threadPoolExecutor.execute(() -> {
            try {
                sendNotification(title, content);
            } catch (Exception e) {
                log.error("异步发送飞书通知失败", e);
            }
        });
    }

    /**
     * 发送飞书通知消息
     * @param title 消息标题
     * @param content 消息内容
     * @return 是否发送成功
     */
    public boolean sendNotification(String title, String content) {
        try {
            ChatMessageParam param = new ChatMessageParam();
            param.setAppCode(MessageChatEnum.ABNORMAL_NOTICE.getCode());
            param.setTitle(title != null ? title : "系统异常通知");
            param.setContent(content);
            param.setChatId(apiConfig.messageChatId);
            param.setRecordFlag(Boolean.FALSE);

            ResultTemplate<String> result = feignAuthorityRpc.sendChatMessage(param);

            if (result != null && result.getSuccess()) {
                return true;
            } else {
                log.error("飞书通知发送失败: {}", result != null ? result.getMsg() : "null response");
                return false;
            }
        } catch (Exception e) {
            log.error("调用飞书通知接口异常", e);
            return false;
        }
    }
}
