package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.enums.ProgressTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 商机进度参数
 * <AUTHOR>
 * @since 2025/5/13
 */
@Data
public class BusinessProgressParam {

    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "进度类型(INTENTION:意向,BUDGET:预算,REQUIREMENT:需求)", type = "String", example = "INTENTION")
    @NotBlank(message = "进度类型不能为空")
    private ProgressTypeEnum progressType;

    @NotNull(message = "商机id不能为空")
    @Schema(description = "商机ID", type = "Integer", example = "1")
    private Integer businessId;

    @Schema(description = "进度状态(0：无，1：有)", type = "Integer", example = "1")
    private Integer progressStatus;

    @Schema(description = "细节描述", type = "String", example = "客户有意向")
    private String detailDesc;

    @Schema(description = "预算金额", type = "Integer", example = "10000")
    private Integer budgetAmount;
}
