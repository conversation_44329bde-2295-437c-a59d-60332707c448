# Redisson延时队列设计方案

## 1. 概述

延时队列是一种能够在指定时间后处理消息的消息队列，用于实现定时任务、延迟处理等场景。本框架基于Redisson实现了高可靠、易扩展的延时队列，支持多种消息类型处理，可无缝集成到现有业务系统中。

## 2. 核心功能

✅ **六大核心特性**

- ✨ **消息延时**：支持灵活配置延时时间，精确到秒级
- 🎯 **多种消息类型**：支持多种业务类型消息处理
- ⚙️ **灵活扩展**：易于新增消息类型和处理器
- 🧩 **处理器工厂**：自动注册并管理各类消息处理器
- 🚀 **定时调度**：基于XXL-Job实现定时消费
- 🔍 **无忧监控**：支持消息处理状态监控和手动触发

## 3. 架构设计

### 3.1 核心组件

![延时队列类图](./延时队列类图.puml)

| 组件名                    | 职责                    |
|------------------------|-----------------------|
| DelayQueueEnum         | 定义队列类型、消息类型、延时时间配置    |
| DelayedMessage         | 消息数据模型，包含ID、类型、内容、时间等 |
| MessageProducer        | 消息生产者，负责消息创建和发送       |
| DelayQueueConsumerTask | 消息消费者，定时从队列获取到期消息     |
| MessageHandler         | 消息处理器接口，定义消息处理规范      |
| MessageHandlerFactory  | 消息处理器工厂，管理各类处理器       |
| TaskMessageHandler     | 具体消息处理器实现，处理特定业务逻辑    |

### 3.2 工作流程

![延时队列设计图](./延时队列设计图.puml)

## 4. 延时队列处理流程

### 4.1 消息发送流程

**适用场景**

- 📅 需要在指定时间后执行的任务
- 🔄 需要定期执行的操作
- 🔔 到期提醒和通知
- 🔒 资源到期释放（如主体公海保护期）

**逻辑流程图**

![消息处理流程](./消息处理流程.puml)

## 5. 使用指南

### 5.1 基础用法

**1. 发送简单延时消息**

```java

@Autowired
private MessageProducer messageProducer;

// 方法一：直接指定队列类型和消息内容
messageProducer.

sendMessage(DelayQueueEnum.ADVERTISING_SUBJECT_QUEUE, "业务ID:12345");

// 方法二：通过API接口发送
@PostMapping("/simple")
public ResultTemplate<Void> sendSimpleMessage(
        @RequestParam String content,
        @RequestParam String messageType) {

    DelayQueueEnum queueEnum = DelayQueueEnum.getByMessageType(messageType);
    messageProducer.sendMessage(queueEnum, content);
    return ResultTemplate.success();
}
```

**2. 自定义消息处理器**

```java

@Slf4j
@Component
public class MyCustomMessageHandler implements MessageHandler {

    @Override
    public DelayQueueEnum getBizType() {
        return DelayQueueEnum.MY_CUSTOM_QUEUE;
    }

    @Override
    public void handle(DelayedMessage message) {
        log.info("处理自定义消息: {}", message.getContent());
        // 实现自定义业务逻辑
    }
}
```

**3. 定义新队列类型**

```java
public enum DelayQueueEnum {
    // 主体公海保护期延迟队列
    ADVERTISING_SUBJECT_QUEUE("sale_comm_advertising_subject_queue", "SUBJECT", 10, TimeUnit.SECONDS),

    // 新增自定义队列（示例）
    MY_CUSTOM_QUEUE("my_custom_queue", "CUSTOM", 60, TimeUnit.MINUTES);

    // 其余代码省略...
}
```

### 5.2 高级配置

**1. 配置消息附加数据**

```java
DelayedMessage message = new DelayedMessage();
message.

setMessageId(UUID.randomUUID().

toString());
        message.

setMessageType("CUSTOM");
message.

setContent("主要内容");

// 设置附加数据
Map<String, Object> extraData = new HashMap<>();
extraData.

put("businessId",12345);
extraData.

put("userId",currentUser.getId());
        extraData.

put("priority","HIGH");
message.

setExtraData(extraData);

messageProducer.

sendMessage(DelayQueueEnum.MY_CUSTOM_QUEUE, message);
```

**2. 定制消息延时时间**

```java
// 创建自定义延时的消息
DelayedMessage message = new DelayedMessage();
// 设置消息属性...

// 使用自定义延时发送
RQueue<DelayedMessage> queue = redisson.getQueue(queueEnum.getQueueName());
RDelayedQueue<DelayedMessage> delayedQueue = redisson.getDelayedQueue(queue);
delayedQueue.

offer(message, 30,TimeUnit.MINUTES); // 自定义延时30分钟
```

### 5.3 特别注意！！！

1. **消息幂等性**
    - 消息处理应保证幂等，同一消息可能会被多次处理
    - 建议在处理器中实现幂等检查逻辑

2. **消息持久性**
    - Redis故障可能导致消息丢失
    - 关键业务可考虑双写数据库或使用RocketMQ等消息队列

3. **处理超时**
    - 长时间运行的处理器可能阻塞消费线程
    - 建议复杂任务异步处理并设置超时机制

4. **消息大小**
    - 消息内容不宜过大，建议仅存储业务ID等关键信息
    - 大量数据建议通过ID关联到数据库获取

## 6. 系统集成

### 6.1 与现有系统集成

**依赖条件**

- Spring Boot 环境
- Redisson客户端配置
- XXL-Job调度中心（可选）

**配置示例**

```yaml
# Redisson配置
spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0

# XXL-Job配置
xxl:
  job:
    admin:
      addresses: http://xxl-job-admin:8080/xxl-job-admin
    executor:
      appname: cheese-sale-crm-api
      ip:
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
```

### 6.2 定时任务配置

**XXL-Job调度配置**

- 任务名称：delayQueueConsumerJob
- Cron表达式：*/10 * * * * ? （每10秒执行一次）
- 任务参数：留空（处理所有类型）或指定messageType（如SUBJECT）

## 7. 扩展开发

### 7.1 自定义消息处理器

实现 MessageHandler 接口

```java

@Slf4j
@Component
public class ContractExpirationHandler implements MessageHandler {

    @Autowired
    private ContractService contractService;

    @Override
    public DelayQueueEnum getBizType() {
        return DelayQueueEnum.CONTRACT_EXPIRATION_QUEUE;
    }

    @Override
    public void handle(DelayedMessage message) {
        try {
            log.info("处理合同到期消息: {}", message.getContent());

            // 解析消息内容
            String contractId = message.getContent();

            // 执行业务逻辑
            contractService.handleExpiration(contractId);

            log.info("合同到期处理完成: {}", contractId);
        } catch (Exception e) {
            log.error("合同到期处理异常: {}", message.getContent(), e);
        }
    }
}
```

### 7.2 消息处理监控与重试

```java

@Slf4j
@Component
public class RetryableMessageHandler implements MessageHandler {

    @Autowired
    private MessageProducer messageProducer;

    @Override
    public DelayQueueEnum getBizType() {
        return DelayQueueEnum.RETRY_QUEUE;
    }

    @Override
    public void handle(DelayedMessage message) {
        try {
            // 尝试处理消息
            doHandle(message);
        } catch (Exception e) {
            // 获取重试次数
            Map<String, Object> extraData = message.getExtraData();
            int retryCount = extraData != null && extraData.containsKey("retryCount")
                    ? (int) extraData.get("retryCount") : 0;

            // 检查是否超过最大重试次数
            if (retryCount < 3) {
                // 增加重试次数
                if (extraData == null) {
                    extraData = new HashMap<>();
                    message.setExtraData(extraData);
                }
                extraData.put("retryCount", retryCount + 1);

                // 延迟递增重试
                int delaySeconds = (int) Math.pow(2, retryCount);
                log.info("消息处理失败，{}秒后重试，当前重试次数: {}", delaySeconds, retryCount + 1);

                // 重新发送到队列
                RQueue<DelayedMessage> queue = redisson.getQueue(getBizType().getQueueName());
                RDelayedQueue<DelayedMessage> delayedQueue = redisson.getDelayedQueue(queue);
                delayedQueue.offer(message, delaySeconds, TimeUnit.SECONDS);
            } else {
                log.error("消息处理失败，已达到最大重试次数: {}", message, e);
                // 可以将失败消息记录到数据库或发送告警
            }
        }
    }

    private void doHandle(DelayedMessage message) {
        // 实际的业务处理逻辑
    }
}
```

## 8. 最佳实践

### 🔧 配置建议

- 合理设置延时时间，避免过短造成资源浪费
- 消息内容保持简洁，尽量只包含关键业务ID
- 关键业务考虑增加持久化和重试机制
- 处理器设计遵循单一职责原则

### ⚠️ 重要限制

- Redis宕机会导致消息丢失，关键业务需考虑双重保障
- 消息不保证严格按时间顺序处理
- 消费线程数受XXL-Job配置影响，注意并发控制
- 消息处理超时会影响后续消息处理，应避免长时间阻塞 