package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 机构资金变动记录视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构资金变动记录视图对象")
public class InstitutionFundChangeRecordVO {
    @Schema(description = "记录ID")
    private Integer id;

    @Schema(description = "资金账户ID")
    private Integer fundAccountId;

    @Schema(description = "机构账户ID")
    private Integer institutionId;

    @Schema(description = "机构名称")
    private String institutionName;

    @Schema(description = "变动金额")
    private BigDecimal changeAmount;

    @Schema(description = "变动类型 (字典0111)")
    @TransField(type = TransTypes.DICT)
    private String changeType;
    private String changeTypeName;

    @Schema(description = "变动原因 (字典0112)")
    @TransField(type = TransTypes.DICT)
    private String changeReason;
    private String changeReasonName;

    @Schema(description = "变动备注")
    private String description;

    @Schema(description = "变动凭据URL")
    private String voucherUrl;

    @Schema(description = "变动调整凭据URL")
    private String adjustmentVoucherUrl;

    @Schema(description = "投放方案ID")
    private Integer planId;

    @Schema(description = "创建方式 [0:系统, 1:人工]")
    private Integer creationMethod;

    @Schema(description = "变动时间")
    private LocalDateTime updateTime;

    @Schema(description = "操作人ID")
    @TransField(type = TransTypes.USER)
    private Integer operator;
    private String operatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
} 