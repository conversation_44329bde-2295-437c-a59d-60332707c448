<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.InnerApproveMapper">

    <select id="queryInstanceCodeById" resultType="java.lang.String">
        SELECT
            s1.instance_code
        FROM sale_crm_inner_approval s1
       INNER JOIN (
            SELECT
                MAX(id) AS max_id
            FROM
                sale_crm_inner_approval
            WHERE biz_id = #{id}
              AND type = #{bizType}
            GROUP BY biz_id
        ) s2
          ON s1.id = s2.max_id
    </select>

    <select id="queryBizId" resultType="com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity">
        SELECT
          s1.biz_id,
          s1.instance_code
         FROM sale_crm_inner_approval s1
        INNER JOIN (
          SELECT
            MAX(id) AS max_id
            FROM
            sale_crm_inner_approval
        WHERE type = #{bizType}
          AND instance_code in
        <foreach collection="codes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        GROUP BY biz_id
            ) s2
        ON s1.id = s2.max_id
    </select>
</mapper>
