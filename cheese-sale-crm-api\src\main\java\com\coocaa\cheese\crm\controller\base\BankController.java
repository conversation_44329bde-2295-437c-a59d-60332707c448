package com.coocaa.cheese.crm.controller.base;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.BankParam;
import com.coocaa.cheese.crm.bean.BankQueryParam;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.service.BankService;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.vo.BankVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 银行帐户管理
 *
 * <AUTHOR>
 * @since 2025-02-25
 */
@Slf4j
@RestController
@RequestMapping("/banks")
@Tag(name = "银行帐户管理", description = "银行帐户管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BankController extends BaseController {
    private final BankService bankService;

    /**
     * 银行帐户下拉列表
     */
    @Operation(summary = "银行帐户下拉列表")
    @GetMapping("/dropdown/{companyId}")
    public ResultTemplate<List<CodeNameVO>> listBanks(@PathVariable("companyId") Integer companyId,
                                                      @RequestParam(name = "name", required = false) String bankName) {
        return ResultTemplate.success(bankService.listByCompanyId(companyId, bankName));
    }

    /**
     * 根据ID批量查询列表
     */
    @Operation(summary = "根据ID批量查询列表")
    @PostMapping("/list/ids")
    public ResultTemplate<List<CodeNameVO>> listByIds(@RequestBody List<Integer> ids) {
        return ResultTemplate.success(bankService.listByIds(ids));
    }

    /**
     * 银行帐户列表(分页)
     */
    @Operation(summary = "银行帐户列表(分页)")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<BankVO>> pageList(@RequestBody PageRequestVO<BankQueryParam> pageRequest) {
        return ResultTemplate.success(bankService.pageList(pageRequest));
    }

    /**
     * 银行帐户详情
     */
    @Operation(summary = "银行帐户详情")
    @Parameter(name = "id", description = "银行帐户ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<BankVO> getDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(bankService.getDetail(id));
    }

    /**
     * 银行帐户创建
     */
    @Operation(summary = "创建银行帐户")
    @PostMapping
    public ResultTemplate<Boolean> create(@RequestBody @Validated BankParam param) {
        return ResultTemplate.success(bankService.createOrUpdate(null, param));
    }

    /**
     * 银行帐户修改
     */
    @Operation(summary = "银行帐户修改")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> update(@PathVariable("id") Integer id, @RequestBody BankParam param) {
        return ResultTemplate.success(bankService.createOrUpdate(id, param));
    }

    /**
     * 银行帐户删除
     */
    @Operation(summary = "银行帐户删除")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> deleteCompany(@PathVariable("id") Integer id) {
        return ResultTemplate.success(bankService.delete(id));
    }
}
