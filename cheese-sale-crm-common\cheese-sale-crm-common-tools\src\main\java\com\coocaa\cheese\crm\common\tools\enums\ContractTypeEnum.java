package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 合同类型枚举(字典0082)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum ContractTypeEnum implements IEnumType<String> {
    NOT_GIFT_PLAY("0082-1", "非赠播"),
    ZERO_GIFT_PLAY("0082-2", "0元赠播");

    private final String code;
    private final String desc;

    private static final Map<String, ContractTypeEnum> BY_CODE_MAP =
            Arrays.stream(ContractTypeEnum.values())
                    .collect(Collectors.toMap(ContractTypeEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static ContractTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static ContractTypeEnum parse(String code, ContractTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ContractTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 