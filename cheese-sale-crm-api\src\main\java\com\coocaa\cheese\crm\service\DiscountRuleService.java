package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.util.BigDecimalUtils;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.DiscountCostParam;
import com.coocaa.cheese.crm.bean.DiscountRuleParam;
import com.coocaa.cheese.crm.bean.DiscountRuleQueryParam;
import com.coocaa.cheese.crm.common.db.entity.DiscountCostEntity;
import com.coocaa.cheese.crm.common.db.entity.DiscountRuleEntity;
import com.coocaa.cheese.crm.common.db.service.IDiscountCostService;
import com.coocaa.cheese.crm.common.db.service.IDiscountRuleService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.DiscountRuleStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.IntervalFlagEnum;
import com.coocaa.cheese.crm.convert.DiscountCostConvert;
import com.coocaa.cheese.crm.convert.DiscountRuleConvert;
import com.coocaa.cheese.crm.util.CodeGenerator;
import com.coocaa.cheese.crm.vo.DiscountCostVO;
import com.coocaa.cheese.crm.vo.DiscountRuleDetailVO;
import com.coocaa.cheese.crm.vo.DiscountRuleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 折扣规则服务
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DiscountRuleService {
    private final IDiscountRuleService discountRuleService;
    private final IDiscountCostService discountCostService;
    private final CodeGenerator codeGenerator;

    /**
     * 分页查询折扣规则列表
     *
     * @param pageRequest 分页查询参数
     * @return 折扣规则分页数据
     */
    @AutoTranslate
    public PageResponseVO<DiscountRuleVO> pageListDiscountRule(PageRequestVO<DiscountRuleQueryParam> pageRequest) {
        // 构建分页对象
        Page<DiscountRuleEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));

        // 获取查询参数
        DiscountRuleQueryParam queryParam = Optional.ofNullable(pageRequest.getQuery())
                .orElseGet(DiscountRuleQueryParam::new);

        // 构建查询条件
        LambdaQueryWrapper<DiscountRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DiscountRuleEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .like(StringUtils.isNotBlank(queryParam.getName()),
                        DiscountRuleEntity::getName, queryParam.getName())
                .eq(Objects.nonNull(queryParam.getCityId()),
                        DiscountRuleEntity::getCityIds, queryParam.getCityId())
                .orderByDesc(DiscountRuleEntity::getCreateTime);

        // 执行查询
        IPage<DiscountRuleEntity> pageResult = discountRuleService.page(page, queryWrapper);

        // 转换为VO
        return DiscountRuleConvert.INSTANCE.toPageResponse(pageResult);
    }

    /**
     * 获取折扣规则详情
     *
     * @param id 折扣规则ID
     * @return 折扣规则详情
     */
    @AutoTranslate
    public DiscountRuleDetailVO getDiscountRuleDetail(Integer id) {
        // 查询折扣规则
        DiscountRuleEntity entity = discountRuleService.getById(id);
        if (entity == null || BooleFlagEnum.YES.getCode().equals(entity.getDeleteFlag())) {
            throw new BusinessException("折扣规则不存在");
        }

        // 转换为VO
        DiscountRuleDetailVO detailVO = DiscountRuleConvert.INSTANCE.toDetailVo(entity);

        // 查询折扣区间
        List<DiscountCostEntity> costEntities = discountCostService.lambdaQuery()
                .eq(DiscountCostEntity::getRuleId, id)
                .list();

        // 转换为VO
        List<DiscountCostVO> costs = DiscountCostConvert.INSTANCE.entityToVO(costEntities);
        detailVO.setDiscountCosts(costs);

        return detailVO;
    }

    /**
     * 创建折扣规则
     *
     * @param param 折扣规则参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createDiscountRule(DiscountRuleParam param) {

        // 构建折扣规则实体
        DiscountRuleEntity entity = new DiscountRuleEntity();
        entity.setName(param.getName());
        entity.setCode(codeGenerator.generateDiscountRuleCode());
        entity.setCityIds(param.getCityIds());
        entity.setStatus(DiscountRuleStatusEnum.EFFECTIVE.getCode());
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());
        if (BooleFlagEnum.isYes(param.getImmediateEffectiveFlag())) {
            // 立即生效
            entity.setStatus(DiscountRuleStatusEnum.EFFECTIVE.getCode());
            entity.setEffectiveTime(LocalDateTime.now());
        } else {
            // 未来生效
            entity.setStatus(DiscountRuleStatusEnum.NOT_EFFECTIVE.getCode());
            entity.setEffectiveTime(param.getEffectiveTime());
        }
        // 保存折扣规则
        boolean success = discountRuleService.save(entity);

        // 保存折扣区间
        if (success) {
            saveDiscountCosts(entity.getId(), param.getDiscountCosts());
        }

        return success;
    }

    /**
     * 保存折扣区间
     */
    private void saveDiscountCosts(Integer ruleId, List<DiscountCostParam> costParams) {
        List<DiscountCostEntity> costEntities = new ArrayList<>();

        for (DiscountCostParam costParam : costParams) {
            DiscountCostEntity costEntity = DiscountCostConvert.INSTANCE.toEntity(costParam);
            costEntity.setRuleId(ruleId);
            costEntities.add(costEntity);
        }

        discountCostService.saveBatch(costEntities);
    }

    /**
     * 作废折扣策略
     *
     * @param id 策略ID
     * @return 是否作废成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean invalidDiscountRule(Integer id) {
        // 1. 检查策略是否存在且未删除
        DiscountRuleEntity existRule = discountRuleService.getById(id);
        if (Objects.isNull(existRule) || BooleFlagEnum.isYes(existRule.getDeleteFlag())) {
            throw new BusinessException("策略不存在");
        }

        // 2. 检查状态是否允许作废
        if (!DiscountRuleStatusEnum.NOT_EFFECTIVE.getCode().equals(existRule.getStatus())) {
            throw new BusinessException("只有未生效的策略才能作废");
        }

        // 3. 更新策略状态为作废
        existRule.setStatus(DiscountRuleStatusEnum.INVALID.getCode());
        return discountRuleService.updateById(existRule);
    }

    /**
     * 折扣试算
     */
    public BigDecimal calculateDiscount(String cityId, BigDecimal cost) {
        if (StringUtils.isBlank(cityId) || cost == null || BigDecimalUtils.lt(cost, BigDecimal.ZERO)) {
            throw new BusinessException("参数错误");
        }

        // 查询适用于该城市的有效折扣规则
        List<DiscountRuleEntity> rules = discountRuleService.lambdaQuery()
                .apply(String.format("FIND_IN_SET('%s', city_ids)", cityId))
                .eq(DiscountRuleEntity::getStatus, DiscountRuleStatusEnum.EFFECTIVE.getCode())
                .eq(DiscountRuleEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(DiscountRuleEntity::getCreateTime)
                .list();

        if (rules.isEmpty()) {
            // 没有适用的折扣规则，返回1.4折
            return new BigDecimal("1.4");
        }

        // 取第一个规则（如果有多个规则，可以根据业务需求选择最优规则）
        DiscountRuleEntity rule = rules.get(0);

        // 查询折扣区间
        List<DiscountCostEntity> costEntities = discountCostService.lambdaQuery()
                .eq(DiscountCostEntity::getRuleId, rule.getId())
                .orderByAsc(DiscountCostEntity::getCost)
                .list();

        if (costEntities.isEmpty()) {
            // 没有折扣区间，返回1.4折
            return new BigDecimal("1.4");
        }

        // 计算折扣
        return BigDecimalUtils.multiply(calculateCostDiscount(costEntities, cost), BigDecimal.TEN);
    }

    /**
     * 折扣试算
     *
     * @param cityId    城市ID
     * @param cost      累计消耗金额
     * @param ruleParam 折扣规则参数
     * @return 折扣系数
     */
    public BigDecimal trialDiscount(Integer cityId, BigDecimal cost, DiscountRuleParam ruleParam) {
        // 检查城市是否在规则的适用城市范围内
        if (StringUtils.isNotBlank(ruleParam.getCityIds())) {
            // 将逗号分隔的城市ID字符串转换为整数列表
            List<Integer> cityIds = Arrays.stream(ruleParam.getCityIds().split(Constants.COMMA))
                    .map(Integer::parseInt)
                    .toList();
            if (!cityIds.contains(cityId)) {
                // 城市不匹配，返回1.4折
                return new BigDecimal("1.4");
            }
        }

        // 将DiscountCostParam转换为DiscountCostEntity
        List<DiscountCostEntity> costEntities = ruleParam.getDiscountCosts().stream()
                .map(DiscountCostConvert.INSTANCE::toEntity)
                .toList();

        // 计算折扣
        return BigDecimalUtils.multiply(calculateCostDiscount(costEntities, cost), BigDecimal.TEN);
    }

    /**
     * 计算消费金额折扣结果
     *
     * @param costList    消费金额配置列表
     * @param currentCost 当前消费金额
     * @return 计算结果
     */
    private BigDecimal calculateCostDiscount(List<DiscountCostEntity> costList, BigDecimal currentCost) {
        BigDecimal costDiscount = new BigDecimal("0.14");

        if (!costList.isEmpty()) {
            // 记录下标，需要获取上一个值
            int index = 0;
            // 查找匹配的消费区间
            for (DiscountCostEntity cost : costList) {
                boolean matched = false;
                if (Objects.isNull(cost.getCost())) {
                    cost.setCost(BigDecimal.ZERO);
                }
                // 使用 IntervalFlagEnum 判断区间类型
                if (IntervalFlagEnum.isClosed(cost.getClosedFlag())) {
                    // 闭区间: currentCost <= cost
                    matched = BigDecimalUtils.le(currentCost, cost.getCost());
                } else if (IntervalFlagEnum.isOpen(cost.getClosedFlag())) {
                    // 开区间: currentCost < cost
                    matched = BigDecimalUtils.lt(currentCost, cost.getCost());
                } else if (IntervalFlagEnum.isPositiveInfinity(cost.getClosedFlag())) {
                    // 正无穷: 直接返回
                    matched = true;
                }

                if (matched) {
                    BigDecimal extraValue;
                    if (index == 0) {
                        extraValue = BigDecimalUtils.sub(currentCost, BigDecimal.ZERO);
                    } else {
                        extraValue = BigDecimalUtils.sub(currentCost, costList.get(index - 1).getCost());
                    }
                    costDiscount = calculateStepDiscount(
                            cost.getDiscount(),
                            extraValue,
                            cost.getCostStep(),
                            cost.getDiscountDecrease(),
                            cost.getDiscountLimit()
                    );
                    break;
                }
                index++;
            }
        }
        return costDiscount;
    }

    /**
     * 计算阶梯折扣
     *
     * @param baseDiscount  基础折扣
     * @param extraValue    超出值
     * @param stepValue     阶梯值
     * @param decreaseValue 递减值
     * @param limitValue    限制值
     * @return 计算后的折扣
     */
    private BigDecimal calculateStepDiscount(
            BigDecimal baseDiscount,
            BigDecimal extraValue,
            BigDecimal stepValue,
            BigDecimal decreaseValue,
            BigDecimal limitValue
    ) {
        // 如果没有阶梯值或递减值，直接返回基础折扣
        if (stepValue == null
                || BigDecimalUtils.le(stepValue, BigDecimal.ZERO)
                || decreaseValue == null
                || BigDecimalUtils.le(decreaseValue, BigDecimal.ZERO)) {
            return baseDiscount;
        }

        // 计算超出阶梯值的次数
        BigDecimal steps = BigDecimalUtils.divide(extraValue, stepValue, 0, RoundingMode.HALF_UP);

        // 计算折扣递减值
        BigDecimal decrease = BigDecimalUtils.multiply(steps, decreaseValue);

        // 计算最终折扣
        BigDecimal finalDiscount = BigDecimalUtils.sub(baseDiscount, decrease);

        // 如果有限制值，确保折扣不低于限制值
        if (limitValue != null && BigDecimalUtils.gt(limitValue, BigDecimal.ZERO)) {
            finalDiscount = BigDecimalUtils.max(finalDiscount, limitValue);
        }

        // 确保折扣在0-1之间
        finalDiscount = BigDecimalUtils.max(finalDiscount, BigDecimal.ZERO);
        finalDiscount = BigDecimalUtils.min(finalDiscount, BigDecimal.ONE);

        return finalDiscount;
    }
} 