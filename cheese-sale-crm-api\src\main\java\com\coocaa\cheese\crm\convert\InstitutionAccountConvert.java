package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.InstitutionAccountParam;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;
import com.coocaa.cheese.crm.vo.InstitutionAccountDetailVO;
import com.coocaa.cheese.crm.vo.InstitutionAccountVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 机构账户信息转换
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Mapper
public interface InstitutionAccountConvert extends PageableConvert<InstitutionAccountEntity, InstitutionAccountVO> {
    InstitutionAccountConvert INSTANCE = Mappers.getMapper(InstitutionAccountConvert.class);
    /**
     * Entity 转 DetailVO
     */
    InstitutionAccountDetailVO toDetailVo(InstitutionAccountEntity entity);

    /**
     * Entity列表 转 VO列表
     */
    List<InstitutionAccountVO> entityToVO(List<InstitutionAccountEntity> entityList);

    /**
     * Param 转 Entity
     */
    InstitutionAccountEntity toEntity(InstitutionAccountParam param);
} 