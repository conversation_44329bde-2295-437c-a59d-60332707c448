<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <appender name="STDOUT"
              class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned by default the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
        <encoder>
<!--            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} -%kvp- %msg%n</pattern>-->
            <pattern>%clr([%level]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] %clr([${PID:-}]){faint} %clr([%thread]){magenta} %clr([%-40.40logger{80}:%line]){cyan} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE"
              class="ch.qos.logback.core.FileAppender">
        <!-- encoders are assigned by default the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
        <file>/data/log/data-import.log</file>
        <encoder>
<!--            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} -%kvp- %msg%n</pattern>-->
            <pattern>%clr([%level]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] %clr([${PID:-}]){faint} %clr([%thread]){magenta} %clr([%-40.40logger{80}:%line]){cyan} %msg%n</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>