package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 商机状态枚举
 * 活跃, 终止
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Getter
@AllArgsConstructor
public enum BusinessStatusEnum implements IEnumType<String> {
    ACTIVE("0074-1", "活跃"),
    TERMINATED("0074-2", "终止"),
    DORMANT("0074-3", "休眠");;

    private final String code;
    private final String desc;

    private static final Map<String, BusinessStatusEnum> BY_CODE_MAP =
            Arrays.stream(BusinessStatusEnum.values())
                    .collect(Collectors.toMap(BusinessStatusEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static BusinessStatusEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static BusinessStatusEnum parse(String code, BusinessStatusEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(BusinessStatusEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 