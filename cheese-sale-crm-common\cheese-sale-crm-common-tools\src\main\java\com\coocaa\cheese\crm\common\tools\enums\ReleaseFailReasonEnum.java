package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 	销售-释放改期失败原因(字典0176)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum ReleaseFailReasonEnum implements IEnumType<String> {

    BUSINESS_OWNER_CHANGE("0176-1", "商机所有人已变更"),
    SIGN_SUBJECT_FOLLOW("0176-2", "签约主体正被他人跟进"),
    RELEASE_DATE_GREATER_THAN_DELAY_DATE("0176-3", "释放日期已大于延期日期");

    private final String code;
    private final String desc;

    private static final Map<String, ReleaseFailReasonEnum> BY_CODE_MAP =
            Arrays.stream(ReleaseFailReasonEnum.values())
                    .collect(Collectors.toMap(ReleaseFailReasonEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static ReleaseFailReasonEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static ReleaseFailReasonEnum parse(String code, ReleaseFailReasonEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ReleaseFailReasonEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 