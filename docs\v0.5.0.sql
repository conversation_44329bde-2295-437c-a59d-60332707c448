USE `cheese_sale_crm`;

CREATE TABLE `sale_comm_advertising_subject`
(
    `id`           INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`         VARCHAR(16)               DEFAULT '0068-6' COMMENT '签约主体类型(字典0068)',
    `brand_id`     INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '品牌ID',
    `company_id`   INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '所属公司ID',
    `top_flag`     TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '是否TOP客户 [0:否, 1:是]',
    `evidence_url` VARCHAR(200)              DEFAULT '' COMMENT '证明材料',
    `description`  VARCHAR(100)              DEFAULT '' COMMENT '备注说明',
    `delete_flag`  TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`      INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`  DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`     INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time`  DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='签约主体';


CREATE TABLE `sale_comm_bank`
(
    `id`          INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`        VARCHAR(30)      NOT NULL DEFAULT '' COMMENT '开户行',
    `account`     VARCHAR(100)     NOT NULL DEFAULT '' COMMENT '银行帐号(加密存储)',
    `company_id`  INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '所属公司ID',
    `type`        VARCHAR(16)      NOT NULL DEFAULT '0078-1' COMMENT '帐户类型(字典0078)',
    `delete_flag` TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '删除标记 [0:否, 1:是]',
    `creator`     INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time` DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`    INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time` DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_parent_code` (`company_id`)
) ENGINE = InnoDB COMMENT ='银行帐户';


CREATE TABLE `sale_comm_brand`
(
    `id`            INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`          VARCHAR(50)      NOT NULL DEFAULT '1' COMMENT '品牌名称: 2-50个字符',
    `industry_code` VARCHAR(16)      NOT NULL DEFAULT '021-109' COMMENT '所属行业',
    `company_id`    INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '所属公司ID',
    `logo_url`      VARCHAR(200)     NOT NULL DEFAULT '' COMMENT '品牌Logo',
    `delete_flag`   TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`       INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`   DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`      INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='主品牌信息';


CREATE TABLE `sale_comm_brand_tag`
(
    `id`          INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `brand_id`    INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '品牌ID',
    `name`        VARCHAR(50)      NOT NULL DEFAULT '1' COMMENT '标签名称: 2-50个字符',
    `creator`     INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time` DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='副品牌信息';


CREATE TABLE `sale_comm_channel`
(
    `id`          INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `parent_id`   INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '父渠道ID',
    `name`        VARCHAR(50)      NOT NULL DEFAULT '' COMMENT '渠道名称: 2-50个字符',
    `nick_name`   VARCHAR(50)      NOT NULL DEFAULT '' COMMENT '渠道别名',
    `description` VARCHAR(100)              DEFAULT '' COMMENT '备注说明',
    `lock_flag`   TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '保护标记 [0:否, 1:是]',
    `delete_flag` TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '删除标记 [0:否, 1:是]',
    `creator`     INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time` DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`    INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time` DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='渠道信息';


CREATE TABLE `sale_comm_company`
(
    `id`          INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`        VARCHAR(50)      NOT NULL DEFAULT '1' COMMENT '公司名称: 2-50个字符',
    `nick_name`   VARCHAR(50)      NOT NULL DEFAULT '' COMMENT '公司简称',
    `code`        VARCHAR(100)              DEFAULT '' COMMENT '统一社会信用代码, 需要加密存储',
    `delete_flag` TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`     INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time` DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`    INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time` DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='公司信息';


CREATE TABLE `sale_comm_config`
(
    `id`          INT(11) UNSIGNED    NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`        VARCHAR(50)         NOT NULL DEFAULT '' COMMENT '名称',
    `parent_code` VARCHAR(50)         NOT NULL DEFAULT '' COMMENT '父级编码',
    `code`        VARCHAR(50)         NOT NULL DEFAULT '' COMMENT '自定义编码',
    `value`       VARCHAR(100)        NOT NULL DEFAULT '' COMMENT '数据',
    `ext1`        VARCHAR(100)                 DEFAULT '' COMMENT '扩展数据1',
    `ext2`        VARCHAR(100)                 DEFAULT '' COMMENT '扩展数据2',
    `ext3`        VARCHAR(100)                 DEFAULT '' COMMENT '扩展数据3',
    `description` VARCHAR(200)                 DEFAULT '' COMMENT '备注',
    `status`      TINYINT(2) UNSIGNED NOT NULL DEFAULT '0' COMMENT '状态 [0:禁用, 1:启用]',
    `creator`     INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time` DATETIME                     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`    INT(11) UNSIGNED             DEFAULT '0' COMMENT '操作人',
    `update_time` DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_parent_code` (`parent_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='销售侧统一配置';


CREATE TABLE `sale_crm_attachment`
(
    `id`          INT(11) UNSIGNED    NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`        TINYINT(2) UNSIGNED NOT NULL DEFAULT '0' COMMENT '附件归属类型',
    `sub_type`    TINYINT(2) UNSIGNED          DEFAULT '0' COMMENT '附件归属子类型',
    `biz_id`      INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '业务ID (合同,协议,...)',
    `name`        VARCHAR(100)                 DEFAULT '' COMMENT '附件名称',
    `url`         VARCHAR(200)                 DEFAULT '' COMMENT '附件全路径',
    `size`        BIGINT(20) UNSIGNED          DEFAULT '0' COMMENT '大小(字节)',
    `file_type`   VARCHAR(16)                  DEFAULT '' COMMENT '文件类型 (pdf, doc,...)',
    `delete_flag` TINYINT(2) UNSIGNED          DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`     INT(11) UNSIGNED             DEFAULT '0' COMMENT '创建人',
    `create_time` DATETIME                     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`    INT(11) UNSIGNED             DEFAULT '0' COMMENT '操作人',
    `update_time` DATETIME                     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='CRM附件';


CREATE TABLE `sale_crm_business`
(
    `id`                       INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code`                     VARCHAR(16)      NOT NULL DEFAULT '' COMMENT '商机编码',
    `channel_id`               INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '渠道ID',
    `brand_id`                 INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '品牌ID',
    `owner_name`               VARCHAR(20)      NOT NULL DEFAULT '' COMMENT '归属人姓名',
    `owner_id`                 INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '归属人ID',
    `department_id`            VARCHAR(64)               DEFAULT '' COMMENT '部门ID',
    `department_name`          VARCHAR(20)               DEFAULT '' COMMENT '部门名称',
    `description`              VARCHAR(50)               DEFAULT '' COMMENT '备注说明',
    `advertising_subject_id`   INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '签约主体ID',
    `advertising_bind_date`    DATE                      DEFAULT NULL COMMENT '主体绑定日期',
    `advertising_release_date` DATE                      DEFAULT NULL COMMENT '主体释放日期',
    `advertising_bind_flag`    TINYINT(1)                DEFAULT '0' COMMENT '商机绑定状态 [0:未绑定, 1:已绑定]',
    `advertising_release_flag` TINYINT(1)                DEFAULT '0' COMMENT '商机释放状态 [0:保护中, 1:已释放]',
    `assign_time`              DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    `progress`                 VARCHAR(16)      NOT NULL DEFAULT '0073-1' COMMENT '商机进度(字典0073)',
    `status`                   VARCHAR(16)      NOT NULL DEFAULT '0074-1' COMMENT '商机状态(字典0074)',
    `delete_flag`              TINYINT(1)                DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`                  INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`              DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`                 INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time`              DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='商机数据表';


CREATE TABLE `sale_crm_business_contact`
(
    `id`                 INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `business_id`        INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '商机ID',
    `name`               VARCHAR(20)      NOT NULL DEFAULT '' COMMENT '姓名',
    `mobile`             VARCHAR(100)     NOT NULL DEFAULT '' COMMENT '电话(加密存储)',
    `gender`             TINYINT(1)       NOT NULL DEFAULT '1' COMMENT '性别：[1-男;2-女]',
    `department`         VARCHAR(20)               DEFAULT '' COMMENT '部门',
    `position`           VARCHAR(20)      NOT NULL DEFAULT '' COMMENT '职务',
    `decision_attitude`  VARCHAR(16)      NOT NULL DEFAULT '0070-1' COMMENT '决策态度(字典0070)',
    `decision_influence` TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '决策影响力',
    `birthday_month_day` VARCHAR(4)                DEFAULT '' COMMENT '生日(月日)(用户输入)',
    `birthday_year`      VARCHAR(4)                DEFAULT '' COMMENT '生日(年)(用户输入)',
    `age_group`          VARCHAR(16)               DEFAULT '' COMMENT '年龄段(字典0071)',
    `address`            VARCHAR(300)              DEFAULT '' COMMENT '收件地址(加密存储)',
    `hobbies`            VARCHAR(40)               DEFAULT '' COMMENT '兴趣爱好',
    `education`          VARCHAR(40)               DEFAULT '' COMMENT '教育信息',
    `family_info`        VARCHAR(40)               DEFAULT '' COMMENT '家庭信息',
    `social_info`        VARCHAR(40)               DEFAULT '' COMMENT '人际信息',
    `career_info`        VARCHAR(40)               DEFAULT '' COMMENT '事业信息',
    `life_info`          VARCHAR(40)               DEFAULT '' COMMENT '生活信息',
    `credit_values`      VARCHAR(40)               DEFAULT '' COMMENT '信用价值观信息',
    `business_card_url`  VARCHAR(200)              DEFAULT '' COMMENT '名片文件地址',
    `delete_flag`        TINYINT(1)                DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`            INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`        DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`           INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time`        DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='联系人数据表';


CREATE TABLE `sale_crm_business_follow`
(
    `id`                           INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `business_id`                  INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '商机ID',
    `follow_time`                  DATETIME                  DEFAULT '1970-01-01 00:00:00' COMMENT '跟进时间',
    `follow_subject`               VARCHAR(16)      NOT NULL DEFAULT '' COMMENT '跟进主题(字典0072)',
    `effective_communication_flag` TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '是否有效沟通 [0:否, 1:是]',
    `communication_progress`       VARCHAR(200)              DEFAULT '' COMMENT '沟通进展',
    `delete_flag`                  TINYINT(1)                DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`                      INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`                  DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`                     INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time`                  DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='商机跟进任务表';


CREATE TABLE `sale_crm_business_protection`
(
    `id`                       INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `target_type`              VARCHAR(16)               DEFAULT '0076-1' COMMENT '控制目标类型(字典0076)',
    `target_id`                VARCHAR(64)      NOT NULL DEFAULT '' COMMENT '控制目标ID(存飞书部门编码)',
    `target_name`              VARCHAR(50)               DEFAULT '' COMMENT '控制目标名称',
    `bind_advertising_subject` SMALLINT(3)      NOT NULL DEFAULT '0' COMMENT '绑定签约主体',
    `establish_connection`     SMALLINT(3)      NOT NULL DEFAULT '0' COMMENT '建立联系',
    `apply_contract`           SMALLINT(3)      NOT NULL DEFAULT '0' COMMENT '合同申请',
    `sign_contract`            SMALLINT(3)      NOT NULL DEFAULT '0' COMMENT '合同签约',
    `delete_flag`              TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`                  INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`              DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`                 INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time`              DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='商机保护期';


CREATE TABLE `sale_crm_business_status_change_log`
(
    `id`            INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `type`          VARCHAR(16)      NOT NULL DEFAULT '0042-5' COMMENT '数据类型 (字典0077)',
    `biz_id`        INT(11) UNSIGNED          DEFAULT '0' COMMENT '业务ID (商机,...)',
    `biz_code`      VARCHAR(32)               DEFAULT '' COMMENT '业务编码 (商机,...)',
    `status`        VARCHAR(16)               DEFAULT '' COMMENT '业务状态(字典0072)',
    `change_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '状态变更时间',
    `operator`      INT(11) UNSIGNED          DEFAULT '0' COMMENT '状态变更操作人',
    `operator_wno`  VARCHAR(16)               DEFAULT '' COMMENT '状态变更操作人工号',
    `operator_name` VARCHAR(20)               DEFAULT '' COMMENT '状态变更操作人姓名',
    `content`       TEXT COMMENT '补充内容',
    `delete_flag`   TINYINT(2) UNSIGNED       DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `create_time`   DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`, `biz_id`) COMMENT '变更类型索引'
) ENGINE = InnoDB COMMENT ='商机状态变更记录';


CREATE TABLE `sale_crm_business_transfer_record`
(
    `id`                   INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `business_id`          INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '商机ID',
    `source_owner_id`      INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '来源归属人',
    `source_department_id` VARCHAR(64)               DEFAULT '' COMMENT '来源管理部门',
    `target_owner_id`      INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '目标归属人',
    `target_department_id` VARCHAR(64)               DEFAULT '' COMMENT '目标管理部门',
    `reason`               VARCHAR(16)      NOT NULL DEFAULT '' COMMENT '转移原因(字典0075)',
    `description`          VARCHAR(200)              DEFAULT '' COMMENT '备注说明',
    `delete_flag`          TINYINT(1)                DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`              INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`          DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`             INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time`          DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='数据转移记录表';



-- DML 初始默认数据
INSERT INTO sale_comm_channel (id, parent_id, name, nick_name, description, lock_flag, delete_flag, creator, create_time, operator, update_time)
VALUES (1, 0, '系统', '系统', '系统描述', 1, 0, 0, '2099-12-31 23:59:59', 0, '2099-12-31 23:59:59'),
       (2, 1, '个人开发', '个人开发', '个人开发', 1, 0, 0, '2099-12-31 23:59:59', 0, '2099-12-31 23:59:59');