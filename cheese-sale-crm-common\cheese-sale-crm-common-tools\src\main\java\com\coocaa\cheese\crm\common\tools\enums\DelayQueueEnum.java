package com.coocaa.cheese.crm.common.tools.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 延迟队列枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-28
 */
@Getter
@AllArgsConstructor
public enum DelayQueueEnum {
    // 主体公海保护期延迟队列
    ADVERTISING_SUBJECT_QUEUE("sale_comm_advertising_subject_queue", "SUBJECT", "subject_time", "subject_time_unit");

    private static final Map<String, DelayQueueEnum> BY_MESSAGE_TYPE_MAP =
            Arrays.stream(DelayQueueEnum.values())
                    .collect(Collectors.toMap(DelayQueueEnum::getMessageType, item -> item));

    private final String queueName;
    private final String messageType;
    private final String delay;
    private final String timeUnit;

    /**
     * 根据消息类型获取队列枚举
     */
    public static DelayQueueEnum getByMessageType(String messageType) {
        return BY_MESSAGE_TYPE_MAP.get(messageType);
    }
} 