package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.InnerApproveApplyQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTemplateParam;
import com.coocaa.cheese.crm.bean.ReleaseRescheduleInitiateParam;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveTaskPageDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseBusinessDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseHistoryDTO;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.ReleaseRescheduleEntity;
import com.coocaa.cheese.crm.common.db.entity.TransferRecordEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import com.coocaa.cheese.crm.common.db.service.IReleaseRescheduleService;
import com.coocaa.cheese.crm.common.db.service.ITransferRecordService;
import com.coocaa.cheese.crm.common.tools.config.api.InnerApproveConfig;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.ApproveFieldTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.ReleaseRescheduleAssignWayEnum;
import com.coocaa.cheese.crm.common.tools.enums.ReleaseRescheduleExecuteStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.common.tools.enums.TransferReasonEnum;
import com.coocaa.cheese.crm.convert.ReleaseRescheduleConvert;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveApplyPageQueryParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveTaskPageQueryParam;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveApplyVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveTaskVO;
import com.coocaa.cheese.crm.vo.InnerApproveReleaseApplyPageVO;
import com.coocaa.cheese.crm.vo.InnerReleaseApproveDetailVO;
import com.coocaa.cheese.crm.vo.InnerReleaseApproveTaskPageVO;
import com.coocaa.cheese.crm.vo.InnerReleaseHistoryVO;
import com.coocaa.cheese.crm.vo.ReleaseRescheduleInitiateVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 释放改期记录表 业务实现类
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ReleaseRescheduleService {

    private static final int APPROVE_ZERO = 0;
    private static final int APPROVE_ONE = 1;
    private static final int APPROVE_THREE = 3;
    private static final long APPROVE_ZERO_L = 0L;

    private final IBusinessService businessService;
    private final InnerApproveService approveService;
    private final InnerApproveConfig innerApproveConfig;
    private final IProductLineService productLineService;
    private final IInnerApproveService innerApproveService;
    private final ITransferRecordService transferRecordService;
    private final IReleaseRescheduleService releaseRescheduleService;

    @AutoTranslate
    public ReleaseRescheduleInitiateVO queryRescheduleInitiate(Integer id) {

        InnerReleaseBusinessDetailDTO dto = Optional.ofNullable(businessService.queryBusinessDetailForInner(id))
                .orElseThrow(() -> new BusinessException("查询商机详情失败!"));
        log.info("发起延期申请查询商机详情id:{}, dto:{}", id, JSONUtil.toJsonStr(dto));
        ReleaseRescheduleInitiateVO vo = ReleaseRescheduleConvert.INSTANCE.toVo(dto);
        //查询该签约主体对应的产品线
        List<String> productLineName = productLineService.queryProductLineBySignSubjectId(dto.getSignSubjectId());
        if (CollectionUtil.isNotEmpty(productLineName)) {
            vo.setProductLine(String.join(Constants.COMMA, productLineName));
        }
        //查询商机分配方式
        TransferRecordEntity recordEntity = transferRecordService.lambdaQuery()
                .select(TransferRecordEntity::getTransferReason)
                .eq(TransferRecordEntity::getBizId, id)
                .orderByDesc(TransferRecordEntity::getCreateTime)
                .last("limit 1")
                .one();
        log.info("发起延期申请查询查询商机分配方式:{}", JSONUtil.toJsonStr(recordEntity));
        if (Objects.isNull(recordEntity)) {
            vo.setAssignWay(ReleaseRescheduleAssignWayEnum.BUILD_BY_ONESELF.getCode());
        } else {
            if (Objects.equals(TransferReasonEnum.ON_JOB.getCode(), recordEntity.getTransferReason())) {
                vo.setAssignWay(ReleaseRescheduleAssignWayEnum.ON_JOB_CHANGE.getCode());
            } else if (Objects.equals(TransferReasonEnum.RESIGNATION.getCode(), recordEntity.getTransferReason())) {
                vo.setAssignWay(ReleaseRescheduleAssignWayEnum.OUT_JOB_HAND_OVER.getCode());
            }
        }
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer initiate(ReleaseRescheduleInitiateParam param) {

        //校验数据
        checkInitiate(param.getBusinessId());
        //保存商机释放改期记录表
        ReleaseRescheduleEntity entity = ReleaseRescheduleConvert.INSTANCE.toEntity(param);
        boolean save = releaseRescheduleService.save(entity);
        //发起申请
        Boolean approval = approveService.initiateApproval(entity.getId().longValue(), StationTargetEnum.BUSINESS_DELAY,
                innerApproveConfig.getRelease(), buildApproveList(param));
        if (!save || !approval) {
            throw new BusinessException("释放改期记录表创建失败!");
        }

        return entity.getId();
    }

    @AutoTranslate
    public PageResponseVO<InnerReleaseApproveTaskPageVO> pageApproveTask(PageRequestVO<InnerApproveTaskQueryParam> pageRequest) {

        InnerApproveTaskPageQueryParam param = new InnerApproveTaskPageQueryParam();
        param.setRuleCode(innerApproveConfig.getRelease());
        param.setFlag(pageRequest.getQuery().getExamine() ? APPROVE_ONE : APPROVE_ZERO);
        param.setApplicant(pageRequest.getQuery().getInstanceUserName());
        PageResponseVO<?> pageResponseVO = approveService.queryTaskPage(param, pageRequest.getCurrentPage(), pageRequest.getPageSize());
        if (CollectionUtil.isEmpty(pageResponseVO.getRows())) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        List<InnerApproveTaskVO> rowList = JSONUtil.toList(JSONUtil.toJsonStr(pageResponseVO.getRows()), InnerApproveTaskVO.class);
        //查询审批code信息
        Set<String> codes = rowList.stream().map(InnerApproveTaskVO::getInstanceCode).collect(Collectors.toSet());
        List<InnerApproveEntity> innerApproveEntities = innerApproveService.queryBizId(codes, StationTargetEnum.BUSINESS_DELAY.getCode());
        log.info("延期申请查询审批任务列表pageRequest:{}, data:{}", JSONUtil.toJsonStr(pageRequest), JSONUtil.toJsonStr(innerApproveEntities));
        if (CollectionUtil.isEmpty(innerApproveEntities)) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        //因为InnerApproveEntity表逻辑没办法联查
        List<Integer> bizIds = innerApproveEntities.stream().map(entity -> entity.getBizId().intValue()).toList();
        List<InnerReleaseApproveTaskPageDTO> businessEntityList = releaseRescheduleService.queryInnerBusiness(bizIds);
        log.info("延期申请查询审批任务列表查询签约主体信息:{}", JSONUtil.toJsonStr(businessEntityList));
        if (CollectionUtil.isEmpty(businessEntityList)) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        Map<String, InnerApproveEntity> examineApproveEntityMap = innerApproveEntities.stream()
                .collect(Collectors.toMap(InnerApproveEntity::getInstanceCode, entity -> entity));
        Map<Integer, InnerReleaseApproveTaskPageDTO> subjectMap = businessEntityList.stream()
                .collect(Collectors.toMap(InnerReleaseApproveTaskPageDTO::getId, entity -> entity));
        //组装新的分页数据返回
        PageResponseVO<InnerReleaseApproveTaskPageVO> resultPage = new PageResponseVO<>();
        resultPage.setCurrentPage(pageResponseVO.getCurrentPage());
        resultPage.setTotal(pageResponseVO.getTotal());
        resultPage.setTotalPages(pageResponseVO.getTotalPages());
        resultPage.setRows(rowList.stream().map(e -> {
            InnerReleaseApproveTaskPageVO vo = ReleaseRescheduleConvert.INSTANCE.toVo(e);
            InnerApproveEntity innerApproveEntity = examineApproveEntityMap.get(e.getInstanceCode());
            if (Objects.nonNull(innerApproveEntity) && Objects.nonNull(subjectMap.get(innerApproveEntity.getBizId().intValue()))) {
                InnerReleaseApproveTaskPageDTO dto = subjectMap.get(innerApproveEntity.getBizId().intValue());
                vo.setId(dto.getId());
                vo.setBrandId(dto.getBrandId());
                vo.setCompanyId(dto.getCompanyId());
                vo.setAdvertisingReleaseDate(dto.getAdvertisingReleaseDate());
                vo.setApplyDelayDate(dto.getApplyDelayDate());
                vo.setDepartmentId(e.getDepartId());
            } else {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).toList());
        return resultPage;
    }

    @AutoTranslate
    public InnerReleaseApproveDetailVO queryApproveDetail(Integer id) {

        InnerApproveEntity entity = innerApproveService.lambdaQuery()
                .eq(InnerApproveEntity::getBizId, id)
                .eq(InnerApproveEntity::getType, StationTargetEnum.BUSINESS_DELAY.getCode())
                .orderByDesc(InnerApproveEntity::getCreateTime)
                .last("LIMIT 1")
                .one();
        log.info("延期申请查询审批信息详情id:{}, data:{}", id, JSONUtil.toJsonStr(entity));
        if (Objects.isNull(entity)) {
            throw new BusinessException("延期申请查询审批信息详情未找到审批信息");
        }
        //查询品牌+主体信息
        InnerReleaseApproveDetailDTO dto = releaseRescheduleService.queryInnerApproveDetail(id);
        if (Objects.isNull(dto)) {
            throw new BusinessException("延期申请查询审批信息详情失败");
        }
        InnerReleaseApproveDetailVO vo = ReleaseRescheduleConvert.INSTANCE.toVo(dto);
        //查询审批信息
        InnerApproveInstanceVO innerApproveInstanceVO = approveService.queryDetail(entity.getInstanceCode());
        if (Objects.nonNull(innerApproveInstanceVO)) {
            vo.setApprovalName(innerApproveInstanceVO.getApprovalName());
            vo.setInstanceUserId(innerApproveInstanceVO.getUserId());
            vo.setInstanceCreateTime(innerApproveInstanceVO.getCreateTime());
            vo.setEndTime(innerApproveInstanceVO.getEndTime());
            vo.setApprovalResult(innerApproveInstanceVO.getApprovalResult());
            vo.setDepartmentId(innerApproveInstanceVO.getDepartId());
        }
        return vo;
    }

    @AutoTranslate
    public List<InnerReleaseHistoryVO> queryReleaseHistory(Integer id) {

        //查询该释放改期记录对应的商机id
        ReleaseRescheduleEntity entity = Optional.ofNullable(releaseRescheduleService.lambdaQuery()
                .select(ReleaseRescheduleEntity::getBusinessId, ReleaseRescheduleEntity::getCreateTime)
                .eq(ReleaseRescheduleEntity::getId, id)
                .eq(ReleaseRescheduleEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one()).orElseThrow(() -> new BusinessException("释放改期记录不存在!"));
        //查询历史记录
        List<InnerReleaseHistoryDTO> list = releaseRescheduleService.queryReleaseHistory(id, entity.getCreateTime(), entity.getBusinessId());
        log.info("延期申请查询审批历史记录id:{}, data:{}", id, JSONUtil.toJsonStr(list));
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        //有则按【状态变更时间】大->小
        list.sort(Comparator.comparing(InnerReleaseHistoryDTO::getStatusChangeTime).reversed());

        return list.stream().map(e -> {
            InnerReleaseHistoryVO vo = ReleaseRescheduleConvert.INSTANCE.toVo(e);
            vo.setInstanceUserId(e.getCreator());
            //从审批表查不出来，说明从休眠池来的，审批人查询商机表
            if(Objects.isNull(e.getCreator())){
                vo.setInstanceUserId(e.getBusinessOwnerId());
                vo.setDepartmentId(e.getBusinessDepartmentId());
            }
            return vo;
        }).toList();
    }

    @AutoTranslate
    public PageResponseVO<InnerApproveReleaseApplyPageVO> pageApplyApprove(PageRequestVO<InnerApproveApplyQueryParam> pageRequest) {

        InnerApproveApplyPageQueryParam param = new InnerApproveApplyPageQueryParam();
        param.setRuleCode(innerApproveConfig.getRelease());
        param.setFlag(pageRequest.getQuery().getExamine() ? APPROVE_ONE : APPROVE_ZERO);
        param.setSortFiled(pageRequest.getQuery().getExamine() ? "endTime" : "createTime");
        PageResponseVO pageResponseVO = approveService.queryApplyPage(param, pageRequest.getCurrentPage(), pageRequest.getPageSize());
        if (CollectionUtil.isEmpty(pageResponseVO.getRows())) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        List<InnerApproveApplyVO> rowList = JSONUtil.toList(JSONUtil.toJsonStr(pageResponseVO.getRows()), InnerApproveApplyVO.class);
        //查询审批申请code信息
        Set<String> codes = rowList.stream().map(InnerApproveApplyVO::getInstanceCode).collect(Collectors.toSet());
        List<InnerApproveEntity> innerApproveEntities = innerApproveService.queryBizId(codes, StationTargetEnum.BUSINESS_DELAY.getCode());
        log.info("延期申请查询审批申请code信息pageRequest:{},:{}", JSONUtil.toJsonStr(pageRequest), JSONUtil.toJsonStr(innerApproveEntities));
        if (CollectionUtil.isEmpty(innerApproveEntities)) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        List<Integer> bizIds = innerApproveEntities.stream().map(entity -> entity.getBizId().intValue()).toList();
        List<InnerReleaseApproveTaskPageDTO> businessEntityList = releaseRescheduleService.queryInnerBusiness(bizIds);
        log.info("延期申请查询审批申请查询签约主体信息:{}", JSONUtil.toJsonStr(businessEntityList));
        if (CollectionUtil.isEmpty(businessEntityList)) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        Map<String, InnerApproveEntity> examineApproveEntityMap = innerApproveEntities.stream()
                .collect(Collectors.toMap(InnerApproveEntity::getInstanceCode, entity -> entity));
        Map<Integer, InnerReleaseApproveTaskPageDTO> subjectMap = businessEntityList.stream()
                .collect(Collectors.toMap(InnerReleaseApproveTaskPageDTO::getId, entity -> entity));
        //组装新的分页数据返回
        PageResponseVO<InnerApproveReleaseApplyPageVO> resultPage = new PageResponseVO<>();
        resultPage.setCurrentPage(pageResponseVO.getCurrentPage());
        resultPage.setTotal(pageResponseVO.getTotal());
        resultPage.setTotalPages(pageResponseVO.getTotalPages());
        resultPage.setRows(rowList.stream().map(e -> {
            InnerApproveReleaseApplyPageVO vo = ReleaseRescheduleConvert.INSTANCE.toVo(e);
            vo.setInstanceCreateTime(e.getCreateTime());
            InnerApproveEntity innerApproveEntity = examineApproveEntityMap.get(e.getInstanceCode());
            if (Objects.nonNull(innerApproveEntity) && Objects.nonNull(subjectMap.get(innerApproveEntity.getBizId().intValue()))) {
                InnerReleaseApproveTaskPageDTO dto = subjectMap.get(innerApproveEntity.getBizId().intValue());
                vo.setId(dto.getId());
                vo.setBrandId(dto.getBrandId());
                vo.setCompanyId(dto.getCompanyId());
                vo.setAdvertisingReleaseDate(dto.getAdvertisingReleaseDate());
                vo.setApplyDelayDate(dto.getApplyDelayDate());
                vo.setExecuteStatus(dto.getExecuteStatus());
                vo.setFailReason(dto.getFailReason());
            } else {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).toList());

        return resultPage;
    }

    private void checkInitiate(Integer id) {

        BusinessEntity entity = Optional.ofNullable(businessService.lambdaQuery()
                .eq(BusinessEntity::getId, id)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one()).orElseThrow(() -> new BusinessException("商机不存在!"));
        ReleaseRescheduleEntity rescheduleEntity = releaseRescheduleService.lambdaQuery()
                .eq(ReleaseRescheduleEntity::getBusinessId, id)
                .eq(ReleaseRescheduleEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(ReleaseRescheduleEntity::getExecuteStatus, ReleaseRescheduleExecuteStatusEnum.PENDING.getCode())
                .one();
        if (!UserThreadLocal.getUserId().equals(entity.getOwnerId())) {
            throw new BusinessException("您不是当前商机的归属人，无法操作");
        }
        if (LocalDate.now().plusDays(APPROVE_THREE).isBefore(entity.getAdvertisingReleaseDate())) {
            throw new BusinessException("保护期不足3天时才可申请延期");
        }
        if (Objects.nonNull(rescheduleEntity)) {
            throw new BusinessException("已有审核中的申请，请勿重复提交");
        }
        //根据该商机id绑定的签约主体反查其余不属于该归属人的商机id
        //用当前商机的签约主体查找是否有其他《商机数据表》【归属人】≠ 当前商机管理员  & （《商机数据表》【主体释放日期】＞ 当前日期 或 【商机状态】=休眠 ）
        //的数据，若有则提示“该签约主体已被跟进占用，不能申请保护期延期”
        List<BusinessEntity> list = businessService.lambdaQuery()
                .eq(BusinessEntity::getAdvertisingSubjectId, entity.getAdvertisingSubjectId())
                .ne(BusinessEntity::getOwnerId, entity.getOwnerId())
                .list();
        log.info("延期申请查询签约主体绑定商机信息:{}", JSONUtil.toJsonStr(list));
        if (CollectionUtil.isNotEmpty(list)) {
            for (BusinessEntity businessEntity : list) {
                if (Objects.equals(APPROVE_ZERO, businessEntity.getAdvertisingReleaseFlag())
                        || BusinessStatusEnum.DORMANT.getCode().equals(businessEntity.getStatus())) {
                    throw new BusinessException("该签约主体已被跟进占用，不能申请保护期延期");
                }
            }
        }
    }

    private List<InnerApproveTemplateParam> buildApproveList(ReleaseRescheduleInitiateParam param) {

        if (Objects.isNull(param.getAdvertisingReleaseDate())) {
            throw new BusinessException("主体释放日期不能为空");
        }
        long delayDate = ChronoUnit.DAYS.between(param.getAdvertisingReleaseDate(), param.getApplyDelayDate());
        //增加延期次数
        //延期次数=【商机ID】与当前相同的，【执行状态】=已执行 的《释放改期记录》数据条数，若无则仍需传值=0
        Long count = releaseRescheduleService.lambdaQuery()
                .eq(ReleaseRescheduleEntity::getBusinessId, param.getBusinessId())
                .eq(ReleaseRescheduleEntity::getExecuteStatus, ReleaseRescheduleExecuteStatusEnum.ALREADY_EXECUTE.getCode())
                .count();

        return Arrays.asList(
                new InnerApproveTemplateParam("creator", ApproveFieldTypeEnum.NUMBER.getCode(), UserThreadLocal.getUserId().toString()),
                new InnerApproveTemplateParam("delayDate", ApproveFieldTypeEnum.NUMBER.getCode(), delayDate + ""),
                new InnerApproveTemplateParam("rescheduleCount", ApproveFieldTypeEnum.NUMBER.getCode(), count + "")
        );
    }
}