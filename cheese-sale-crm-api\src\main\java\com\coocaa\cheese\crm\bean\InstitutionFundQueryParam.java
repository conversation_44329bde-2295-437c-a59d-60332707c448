package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 机构资金查询参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构资金查询参数")
public class InstitutionFundQueryParam {

    @Schema(description = "机构账户编码", type = "String", example = "1")
    private String fundAccountCode;

    @Schema(description = "变动类型 (字典0111)", type = "String", example = "0111-1")
    private String changeType;

    @Schema(description = "变动原因 (字典0112)", type = "String", example = "0112-1")
    private String changeReason;

    @Schema(description = "创建方式 [0:系统, 1:人工]", type = "Integer", example = "0")
    private Integer creationMethod;

    @Schema(description = "查询月份 格式yyyy-MM", type = "String", example = "2025-01")
    private String month;

    @Schema(description = "开始时间", type = "LocalDateTime", example = "2025-01-01 00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", type = "LocalDateTime", example = "2025-12-31 23:59:59")
    private LocalDateTime endTime;
} 