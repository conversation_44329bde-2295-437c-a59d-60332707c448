@startuml BusinessProgressFlow

skinparam backgroundColor white
skinparam handwritten false
skinparam state {
  BackgroundColor<<Initial>> LightYellow
  BackgroundColor<<Following>> LightBlue
  BackgroundColor<<ContractPushing>> LightGreen
  BackgroundColor<<Signed>> LightPink
}

title 商机进度状态流转图

state "初始化" as Initial <<Initial>>
note right of Initial
- 商机创建后的初始状态
- 保护期默认10天, 有规则走规则
- 尚未开始跟进
end note

state "初步接洽" as Following <<Following>>
note right of Following
- 已开始与客户接触
- 正在进行商务沟通
- 有效沟通
end note

state "合同推进" as ContractPushing <<ContractPushing>>
note right of ContractPushing
- 商务条款已基本确定
- 正在推进合同签署
- 合同审批通过，状态为待发起合同
end note

state "已签约" as Signed <<Signed>>
note right of Signed
- 合同已完成签署
- 商机转化成功
- 类型≠零元赠播的《合同》状态为已签约（待执行）
- 已签约（待执行）：对方已盖章-合同未开始
- 执行中：对方已盖章-合同已开始-执行中
- 已签约（已执行）：对方已盖章-合同已开始-已执行
end note

[*] --> Initial
Initial --> Following : 开始跟进
note on link
保护期规则:
释放日期 = 原释放日期 + min(品牌/部门/渠道匹配的【有效跟进】延期值)
若无匹配则延期值为0
end note

Following --> ContractPushing : 推进合同
note on link
保护期规则:
释放日期 = 原释放日期 + min(品牌/部门/渠道匹配的【合同推进】延期值)
若无匹配则延期值为0
end note

ContractPushing --> Signed : 完成签约
note on link
保护期规则:
释放日期 = 原释放日期 + min(品牌/部门/渠道匹配的【已签约】延期值)
若无匹配则设为永久
end note
@enduml 