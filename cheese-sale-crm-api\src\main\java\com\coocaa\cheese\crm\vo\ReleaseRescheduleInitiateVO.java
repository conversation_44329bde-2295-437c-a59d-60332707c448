
package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 申请延期发起查询VO
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
@Data
@Accessors(chain = true)
@Schema(name = "ReleaseRescheduleInitiateVO", description = "申请延期发起查询VO")
public class ReleaseRescheduleInitiateVO {

    @Schema(description = "商机ID")
    private Integer id;

    @Schema(description = "品牌")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "产品线")
    private String productLine;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY, target = "advertisingSubjectName")
    private Integer companyId;
    private String advertisingSubjectName;

    @Schema(description = "商机分配方式(字典0150)")
    @TransField(type = TransTypes.DICT)
    private String assignWay;
    private String assignWayName;

    @Schema(description = "商机来源渠道")
    @TransField(type = TransTypes.CHANNEL)
    private Integer channelId;
    private String channelName;

    @Schema(description = "商机分配日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate assignTime;

    @Schema(description = "主体释放日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate advertisingReleaseDate;

    @Schema(description = "当前商机进度(字典0073)")
    @TransField(type = TransTypes.DICT)
    private String progress;
    private String progressName;
}