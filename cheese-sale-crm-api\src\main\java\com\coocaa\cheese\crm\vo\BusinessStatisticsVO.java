package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 商机进度统计VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(name = "BusinessStatisticsVO", description = "商机进度统计VO")
public class BusinessStatisticsVO {
    @TransField
    @Schema(description = "商机状态统计列表")
    private List<StatItem> statusList;

    @TransField
    @Schema(description = "商机进度统计列表")
    private List<StatItem> progressList;

    /**
     * 统计项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "统计项")
    public static class StatItem {

        @Schema(description = "统计项名称", example = "保护中")
        private String name;

        @Schema(description = "字典编码")
        @TransField(type = TransTypes.DICT, target = "dictName")
        private String dictCode;

        @Schema(description = "字典名称")
        private String dictName;

        @Schema(description = "统计数量", example = "10")
        private Integer count;

        public String getName() {
            return StringUtils.isNotBlank(dictName) ? dictName : name;
        }
    }
} 