package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.cheese.crm.common.tools.enums.ProgressTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商机进度表
 * <AUTHOR>
 * @since 2025/5/12
 */
@Data
@TableName("sale_crm_business_progress")
public class BusinessProgressEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商机ID
     */
    private Integer businessId;

    /**
     * 进度状态（0:有需求, 1:无需求）
     */
    private Integer progressStatus;

    /**
     * 进度类型
     */
    private ProgressTypeEnum progressType;

    /**
     * 细节描述
     */
    private String detailDesc;

    /**
     * 预算金额
     */

    private Integer budgetAmount;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 操作人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 删除标志位 [0:否, 1:是]
     */
    private Integer deleteFlag;
}
