package com.coocaa.cheese.crm.controller.base;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.service.ConfigService;
import com.coocaa.cheese.crm.vo.ConfigVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-22
 */
@Slf4j
@RestController
@RequestMapping("/config")
@Tag(name = "系统配置", description = "系统配置")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ConfigController {
    private final ConfigService configService;

    /**
     * 刷新配置信息缓存
     */
    @Operation(summary = "刷新配置信息缓存")
    @PutMapping
    public ResultTemplate<Boolean> refreshConfigCache() {
        return ResultTemplate.success(configService.refreshCache());
    }

    /**
     * 获取配置信息
     */
    @Operation(summary = "获取配置信息")
    @Parameter(name = "code", description = "配置编码", required = true, example = "city_watermark_price")
    @GetMapping("/{code}")
    public ResultTemplate<ConfigVO> getConfig(@PathVariable(name = "code") String code) {
        return ResultTemplate.success(configService.getConfig(code));
    }

    /**
     * 根据父编码获取配置信息
     */
    @Operation(summary = "根据父编码获取配置信息")
    @Parameter(name = "code", description = "父配置编码", required = true, example = "city_watermark_price")
    @GetMapping("/parent/{code}")
    public ResultTemplate<List<ConfigVO>> listConfigs(@PathVariable(name = "code") String code) {
        return ResultTemplate.success(configService.listConfigByParent(code));
    }

    /**
     * 修改配置信息
     */
    @Operation(summary = "修改配置信息")
    @Parameter(name = "code", description = "配置编码", required = true, example = "auto-audit")
    @PutMapping("/{code}")
    public ResultTemplate<Boolean> updateConfig(@PathVariable(name = "code") String code,
                                                @RequestParam(name = "value") String value) {
        return ResultTemplate.success(configService.createOrUpdateByCode(code, value));
    }

    /**
     * 新建或修改配置
     */
    @Operation(summary = "新建或修改配置")
    @PostMapping
    public ResultTemplate<Boolean> createOrUpdateConfig(@RequestBody ConfigVO config) {
        return ResultTemplate.success(configService.createOrUpdate(config));
    }
}
