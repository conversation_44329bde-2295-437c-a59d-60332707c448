package com.coocaa.cheese.crm.config.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作日志注解
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-04
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperateLog {
    /**
     * 功能名称
     */
    String functionName();

    /**
     * 实体编码
     */
    String entityCode() default "";
    
    /**
     * 实体ID的参数名称,支持SpEL表达式
     */
    String entityId() default "";
} 