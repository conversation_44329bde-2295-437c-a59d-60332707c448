package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

/**
 * 商机保护期创建参数
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessProtectionParam", description = "商机保护期创建参数")
public class BusinessProtectionParam {
    @NotBlank(message = "控制目标类型不能为空")
    @Schema(description = "控制目标类型(字典0076)", type = "String", example = "0076-1")
    private String targetType;

    @NotNull(message = "控制目标ID不能为空")
    @Schema(description = "控制对象ID", type = "Integer", example = "1")
    private String targetId;

    @NotNull(message = "控制目标名称不能为空")
    @Schema(description = "控制对象名称", type = "String", example = "Benz")
    private String targetName;

    @NotNull(message = "初始绑定保护天数不能为空")
    @Range(min = 1, max = Constants.FOREVER_DAYS, message = "初始绑定有效保护天数范围为{min} - {max}")
    @Schema(description = "绑定签约主体(初始绑定), 永久传9999", type = "Integer", example = "30")
    private Integer bindAdvertisingSubject;

    @NotNull(message = "首次有效跟进保护天数不能为空")
    @Range(min = 0, max = Constants.FOREVER_DAYS, message = "首次有效跟进有效保护天数范围为{min} - {max}")
    @Schema(description = "建立联系(首次有效跟进), 永久传9999", type = "Integer", example = "15")
    private Integer establishConnection;

    @NotNull(message = "首次合同审批通过保护天数不能为空")
    @Range(min = 0, max = Constants.FOREVER_DAYS, message = "首次合同审批通过有效保护天数范围为{min} - {max}")
    @Schema(description = "合同申请(首次合同审批通过), 永久传9999", type = "Integer", example = "7")
    private Integer applyContract;

    @NotNull(message = "合同签约完成保护天数不能为空")
    @Range(min = 0, max = Constants.FOREVER_DAYS, message = "合同签约完成有效保护天数范围为{min} - {max}")
    @Schema(description = "合同签约(合同签约完成), 永久传9999", type = "Integer", example = "90")
    private Integer signContract;
}