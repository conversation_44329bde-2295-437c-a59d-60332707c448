@startuml DelayQueueClassDiagram

skinparam backgroundColor white
skinparam handwritten false

title Redisson延时队列类图

package "延时队列枚举" {
  enum DelayQueueEnum {
    - queueName: String
    - messageType: String
    - delay: long
    - timeUnit: TimeUnit
    --
    + getByMessageType(messageType: String): DelayQueueEnum
  }
  
  note bottom of DelayQueueEnum
    目前支持的队列类型:
    ADVERTISING_SUBJECT_QUEUE - 主体公海保护期延迟队列
  end note
}

package "消息模型" {
  class DelayedMessage {
    - messageId: String
    - messageType: String
    - content: String
    - createTime: LocalDateTime
    - executeTime: LocalDateTime
    - extraData: Map<String, Object>
  }
}

package "消息生产" {
  class MessageProducer {
    - redisson: RedissonClient
    --
    + sendMessage(queueEnum: DelayQueueEnum, message: DelayedMessage): void
    + sendMessage(queueEnum: DelayQueueEnum, content: String): void
  }
}

package "消息消费" {
  class DelayQueueConsumerTask {
    - redisson: RedissonClient
    --
    + execute(): void
    + processQueues(messageType: String): void
  }
}

package "消息处理" {
  interface MessageHandler {
    + getBizType(): DelayQueueEnum
    + handle(message: DelayedMessage): void
  }
  
  class MessageHandlerFactory {
    - {static} HANDLERS: Map<String, MessageHandler>
    - applicationContext: ApplicationContext
    --
    + {static} getHandler(messageType: String): MessageHandler
    + setApplicationContext(applicationContext: ApplicationContext): void
    + afterPropertiesSet(): void
  }
  
  class TaskMessageHandler {
    + getBizType(): DelayQueueEnum
    + handle(message: DelayedMessage): void
  }
}

package "接口层" {
  class DelayedMessageController {
    - messageProducer: MessageProducer
    - delayQueueConsumerTask: DelayQueueConsumerTask
    --
    + sendSimpleMessage(content: String, messageType: String): ResultTemplate<Void>
    + processMessages(messageType: String): ResultTemplate<Void>
  }
}

' 关系连接
MessageProducer --> DelayQueueEnum : 使用
MessageProducer ..> DelayedMessage : 创建

DelayQueueConsumerTask --> MessageHandlerFactory : 获取处理器
DelayQueueConsumerTask ..> DelayedMessage : 消费

MessageHandler --> DelayQueueEnum : 返回类型
MessageHandler ..> DelayedMessage : 处理

MessageHandlerFactory o--> MessageHandler : 管理

TaskMessageHandler ..|> MessageHandler : 实现

DelayedMessageController --> MessageProducer : 调用
DelayedMessageController --> DelayQueueConsumerTask : 调用

@enduml 