package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 转移记录表
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@TableName("sale_crm_transfer_record")
public class TransferRecordEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务类型 (字典0113-1)
     */
    private String bizType;

    /**
     * 业务ID（商机ID或机构账户ID）
     */
    private Integer bizId;

    /**
     * 来源归属人
     */
    private Integer sourceOwnerId;


    /**
     * 来源管理部门ID
     */
    private String sourceDepartmentId;

    /**
     * 目标归属人
     */
    private Integer targetOwnerId;

    /**
     * 目标管理部门ID
     */
    private String targetDepartmentId;

    /**
     * 转移原因 [离职交接, 在职转移]
     */
    private String transferReason;

    /**
     * 备注说明
     */
    private String description;
    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
} 