package com.coocaa.cheese.crm.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.DesensitizeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 机构账户详情视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "机构账户详情视图对象")
public class InstitutionAccountDetailVO extends InstitutionAccountVO {

    @Schema(description = "统一社会信用代码", type = "String", example = "91330000XXX")
    @JSONField(serializeUsing = DesensitizeSerializer.class)
    private String creditCode;

    @Schema(description = "营业执照附件地址", type = "String", example = "https://www.baidu.com/img/bd_logo1.png")
    private String licenseUrl;


} 