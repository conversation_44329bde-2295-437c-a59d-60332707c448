
package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 产品线变更申请入参
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
@Data
@Schema(name = "ProductLineInitiateParam", description = "产品线变更申请入参")
public class ProductLineInitiateParam {

    @NotNull(message = "商机ID不能为空")
    @Schema(description = "商机ID")
    private Integer businessId;

    @NotBlank(message = "当前商机进度不能为空")
    @Schema(description = "当前商机进度(字典0073)")
    private String progress;

    @Schema(description = "变更前产品线(V0.7.2之前版本这个值为空，所以不是必传)")
    private List<String> beforeProductLines;

    @NotEmpty(message = "变更后产品线不能为空")
    @Schema(description = "变更后产品线")
    private List<String> afterProductLines;

    @NotBlank(message = "情况说明不能为空")
    @Schema(description = "情况说明", maxLength = 100)
    private String remark;
}