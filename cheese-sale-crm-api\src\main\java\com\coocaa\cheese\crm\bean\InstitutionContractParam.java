package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

/**
 * 机构合同参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构合同参数")
public class InstitutionContractParam {
    @Schema(description = "机构账户ID", type = "Integer", example = "1")
    @NotNull(message = "机构账户ID不能为空")
    private Integer institutionId;

    @Size(max = 30, message = "合同编号不能超过30个字符")
    @Schema(description = "合同编号", type = "String", example = "CT20250101001")
    @NotBlank(message = "合同编号不能为空")
    private String contractCode;

    @Schema(description = "签约日期", type = "LocalDate", example = "2025-01-01")
    @NotNull(message = "签约日期不能为空")
    private LocalDate signDate;

    @Size(max = 50, message = "备注说明不能超过50个字符")
    @Schema(description = "备注说明", type = "String", example = "重要合同")
    private String description;

    @NotNull(message = "文件附件地址不能为空")
    @Schema(description = "文件附件地址", type = "String", example = "https://www.baidu.com/img/bd_logo1.png")
    private String fileUrl;
} 