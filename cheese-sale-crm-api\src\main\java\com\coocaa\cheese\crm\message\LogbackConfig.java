package com.coocaa.cheese.crm.message;

import ch.qos.logback.classic.LoggerContext;
import jakarta.annotation.PostConstruct;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/14
 */
@Configuration
public class LogbackConfig {

    @Autowired
    private LogbackAppender logbackAppender;

    @PostConstruct
    public void init() {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        logbackAppender.setContext(loggerContext);
        logbackAppender.start();
        loggerContext.getLogger("ROOT").addAppender(logbackAppender);
    }
}
