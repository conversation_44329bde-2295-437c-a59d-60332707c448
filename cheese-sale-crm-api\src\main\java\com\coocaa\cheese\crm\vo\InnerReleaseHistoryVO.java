package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Accessors(chain = true)
@Schema(name = "InnerReleaseHistoryVO", description = "站内审批任务延期申请历史延期返回参数")
public class InnerReleaseHistoryVO {

    @Schema(description = "申请人")
    @TransField(type = TransTypes.USER)
    private Integer instanceUserId;
    private String instanceUserName;

    @Schema(description = "申请部门")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "原主体释放日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate advertisingReleaseDate;

    @Schema(description = "申请延期至的日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate applyDelayDate;

    @Schema(description = "判定人")
    @TransField(type = TransTypes.USER)
    private Integer judgeUserId;
    private String judgeUserName;
}
