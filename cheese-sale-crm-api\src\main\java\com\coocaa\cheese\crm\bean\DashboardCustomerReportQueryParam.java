package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 数据看板客户报备查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-11
 */
@Data
public class DashboardCustomerReportQueryParam {

    @Schema(description = "部门id", type = "String", example = "od-xxx")
    private String departmentId;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @NotNull(message = "开始时间不能为空")
    private LocalDate startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @NotNull(message = "结束时间不能为空")
    private LocalDate endTime;
}
