
package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品线表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
public class ProductLineDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}