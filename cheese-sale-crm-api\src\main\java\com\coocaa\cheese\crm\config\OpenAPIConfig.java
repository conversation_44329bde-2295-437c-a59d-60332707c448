package com.coocaa.cheese.crm.config;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.SpecVersion;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * openapi配置
 *
 * <AUTHOR>
 * @since 2024-09-10 15:39:39
 */
@Configuration
public class OpenAPIConfig {

    /**
     * 配置接口文档
     */
    @Bean
    public OpenAPI openApi() {
        return new OpenAPI(SpecVersion.V30)
                .info(new Info().title("销售CRM系统接口文档").version("v1"))
                .externalDocs(new ExternalDocumentation().description("项目API文档").url(Constants.SLASH));
    }
}
