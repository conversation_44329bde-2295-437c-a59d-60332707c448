package com.coocaa.cheese.crm.audit.template;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.audit.bean.ApproveRequest;
import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveDetailVO;
import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveParam;
import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveVO;
import com.coocaa.cheese.crm.audit.service.AbstractApproveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 审批模板
 * <AUTHOR>
 * @since 2025-06-18
 */
public abstract class BaseApproveTemplate<T extends BaseInnerApproveDetailVO, K extends ApproveRequest, D extends BaseInnerApproveVO, E extends BaseInnerApproveParam> {

    private final AbstractApproveService<T, K, D, E> abstractApproveService;

    protected BaseApproveTemplate(AbstractApproveService<T, K, D, E> abstractApproveService) {
        this.abstractApproveService = abstractApproveService;
    }

    @Operation(summary = "发起审批申请查询")
    @Parameter(name = "bizId", description = "bizId", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{bizId}/initiate")
    public ResultTemplate<T> queryPublicSeaAuthInitiate(@PathVariable("bizId") Long bizId) {

        return ResultTemplate.success(abstractApproveService.queryInnerApproveDetail(bizId));
    }

    @Operation(summary = "发起审批申请")
    @PostMapping("")
    public ResultTemplate<Long> initiate(@RequestBody @Validated K param) {
        return ResultTemplate.success(abstractApproveService.initiate(param));
    }

    @Operation(summary = "查询审批任务列表(分页)")
    @PostMapping("/task/page")
    public ResultTemplate<PageResponseVO<D>> pageApproveTask(@Validated @RequestBody PageRequestVO<E> pageRequest) {
        return ResultTemplate.success(abstractApproveService.pageApproveTask(pageRequest));
    }

    @Operation(summary = "查询审批信息详情")
    @Parameter(name = "id", description = "审批记录id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<T> queryApproveDetail(@PathVariable("id") Long id) {
        return ResultTemplate.success(abstractApproveService.queryApproveDetail(id));
    }

    @Operation(summary = "查询审批申请列表(分页)")
    @PostMapping("/apply/page")
    public ResultTemplate<PageResponseVO<D>> pageApplyApprove(@Validated @RequestBody PageRequestVO<E> pageRequest) {
        return ResultTemplate.success(abstractApproveService.pageApplyApprove(pageRequest));
    }
}