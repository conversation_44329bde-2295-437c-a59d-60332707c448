package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.AdvertisingSubjectParam;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectQueryParam;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectReportH5Param;
import com.coocaa.cheese.crm.common.db.bean.AdvertisingSubjectQueryDTO;
import com.coocaa.cheese.crm.common.db.bean.DashboardCustomerReportSearchDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.vo.AdvertisingSubjectVO;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 渠道信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface AdvertisingSubjectConvert extends PageableConvert<AdvertisingSubjectEntity, AdvertisingSubjectVO> {
    AdvertisingSubjectConvert INSTANCE = Mappers.getMapper(AdvertisingSubjectConvert.class);

    /**
     * Entity 转 VO
     */
    AdvertisingSubjectVO toVo(AdvertisingSubjectEntity entity);

    /**
     * VO 转 Entity
     */
    AdvertisingSubjectEntity toEntity(AdvertisingSubjectVO vo);

    /**
     * VO 转 Entity
     */
    AdvertisingSubjectEntity toEntity(AdvertisingSubjectParam param);

    /**
     * 转换查询参数
     */
    AdvertisingSubjectQueryDTO toDto(AdvertisingSubjectQueryParam param);

    /**
     * AdvertisingSubjectReportH5Param 转 Entity
     */
    AdvertisingSubjectEntity toEntity(AdvertisingSubjectReportH5Param advertisingSubjectReportH5Param);

    /**
     * DashboardCustomerReportSearchDTO 转 DashboardCustomerReportSearchVO
     */
    DashboardCustomerReportSearchVO dtoToVo(DashboardCustomerReportSearchDTO dto);
}
