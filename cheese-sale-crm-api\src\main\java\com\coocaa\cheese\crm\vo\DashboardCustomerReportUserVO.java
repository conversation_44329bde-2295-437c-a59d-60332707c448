package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 客户报备用户数据VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-10
 */
@Data
public class DashboardCustomerReportUserVO {

    @Schema(description = "用户ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer userId;
    private String userName;

    @Schema(description = "申报数量", type = "Integer", example = "10")
    private Integer applyCount;

    @Schema(description = "审批通过数量", type = "Integer", example = "3")
    private Integer approvedCount;

    @Schema(description = "商机数量", type = "Integer", example = "2")
    private Integer businessCount;
}
