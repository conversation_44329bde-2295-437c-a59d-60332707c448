package com.coocaa.cheese.crm.controller;

import com.coocaa.ad.common.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Redis数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-22
 */
@Slf4j
@RestController
@RequestMapping("/common/redis")
@Tag(name = "Redis数据", description = "Redis数据")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class RedisController extends BaseController {
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 查询string类型缓存
     */
    @Operation(summary = "查询string类型缓存")
    @GetMapping("/string/query")
    public ResultTemplate<String> stringQuery(@RequestParam(name = "key") String key) {
        return ResultTemplate.success(stringRedisTemplate.opsForValue().get(key));
    }

    /**
     * 删除string类型缓存
     */
    @Operation(summary = "删除string类型缓存")
    @GetMapping("/string/delete")
    public ResultTemplate<Boolean> stringDelete(@RequestParam(name = "key") String key) {
        return ResultTemplate.success(stringRedisTemplate.delete(key));
    }

    /**
     * 更新string类型缓存
     */
    @Operation(summary = "更新string类型缓存")
    @GetMapping("/string/add-update")
    public ResultTemplate<String> stringUpdate(@RequestParam(name = "key") String key,
                                               @RequestParam(name = "value") String value) {
        stringRedisTemplate.opsForValue().set(key, value);
        return ResultTemplate.success();
    }
}
