package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.ConfigEntity;
import com.coocaa.cheese.crm.common.db.mapper.ConfigMapper;
import com.coocaa.cheese.crm.common.db.service.IConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 合同配置 服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-22
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, ConfigEntity> implements IConfigService {

}
