package com.coocaa.cheese.crm.controller.approve;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.InnerApproveApplyQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.bean.ReleaseRescheduleInitiateParam;
import com.coocaa.cheese.crm.service.ReleaseRescheduleService;
import com.coocaa.cheese.crm.vo.InnerApproveReleaseApplyPageVO;
import com.coocaa.cheese.crm.vo.InnerReleaseApproveDetailVO;
import com.coocaa.cheese.crm.vo.InnerReleaseApproveTaskPageVO;
import com.coocaa.cheese.crm.vo.InnerReleaseHistoryVO;
import com.coocaa.cheese.crm.vo.ReleaseRescheduleInitiateVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 释放改期记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
@Slf4j
@RestController
@RequestMapping("/release/reschedule")
@Tag(name = "商机延期申请", description = "商机延期申请管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ReleaseRescheduleController {

    private final ReleaseRescheduleService releaseRescheduleService;

    @Operation(summary = "发起延期申请查询")
    @Parameter(name = "id", description = "商机id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}/initiate")
    public ResultTemplate<ReleaseRescheduleInitiateVO> queryRescheduleInitiate(@PathVariable("id") Integer id) {

        return ResultTemplate.success(releaseRescheduleService.queryRescheduleInitiate(id));
    }

    @Operation(summary = "发起延期申请")
    @PostMapping("")
    public ResultTemplate<Integer> initiate(@RequestBody @Validated ReleaseRescheduleInitiateParam param) {
        return ResultTemplate.success(releaseRescheduleService.initiate(param));
    }

    @Operation(summary = "查询审批任务列表(分页)")
    @PostMapping("/task/page")
    public ResultTemplate<PageResponseVO<InnerReleaseApproveTaskPageVO>> pageApproveTask(@Validated @RequestBody PageRequestVO<InnerApproveTaskQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(InnerApproveTaskQueryParam::new));
        return ResultTemplate.success(releaseRescheduleService.pageApproveTask(pageRequest));
    }

    @Operation(summary = "查询审批信息详情")
    @Parameter(name = "id", description = "释放改期记录id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<InnerReleaseApproveDetailVO> queryApproveDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(releaseRescheduleService.queryApproveDetail(id));
    }

    @Operation(summary = "查询历史延期")
    @Parameter(name = "id", description = "释放改期记录id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/history/{id}")
    public ResultTemplate<List<InnerReleaseHistoryVO>> queryReleaseHistory(@PathVariable("id") Integer id) {
        return ResultTemplate.success(releaseRescheduleService.queryReleaseHistory(id));
    }

    @Operation(summary = "查询审批申请列表(分页)")
    @PostMapping("/apply/page")
    public ResultTemplate<PageResponseVO<InnerApproveReleaseApplyPageVO>> pageApplyApprove(@Validated @RequestBody PageRequestVO<InnerApproveApplyQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(InnerApproveApplyQueryParam::new));
        return ResultTemplate.success(releaseRescheduleService.pageApplyApprove(pageRequest));
    }
}