package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/9
 */
@Data
@Accessors(chain = true)
public class InnerReleaseHistoryDTO {

    /**
     * 释放延期id
     */
    private Integer id;

    /**
     * 申请人
     */
    private Integer creator;

    /**
     * 申请部门
     */
    private String departmentId;

    /**
     * 申请人
     */
    private Integer businessOwnerId;

    /**
     * 申请部门
     */
    private String businessDepartmentId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 原主体释放日期
     */
    private LocalDate advertisingReleaseDate;

    /**
     * 申请延期至的日期
     */
    private LocalDate applyDelayDate;

    /**
     * 判定人
     */
    private Integer judgeUserId;

    /**
     * 状态变更时间
     */
    private LocalDateTime statusChangeTime;
}
