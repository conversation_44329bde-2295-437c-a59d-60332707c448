package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 机构转移参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构转移参数")
public class InstitutionTransferParam {
    @Schema(description = "机构账户ID列表", type = "List", example = "[1, 2, 3]")
    @NotEmpty(message = "机构账户ID列表不能为空")
    private List<Integer> institutionIds;

    @Schema(description = "目标部门ID", type = "String", example = "dept_123")
    @NotNull(message = "目标部门ID不能为空")
    private String targetDepartmentId;

    @Schema(description = "目标归属人ID", type = "Integer", example = "101")
    @NotNull(message = "目标归属人ID不能为空")
    private Integer targetOwnerId;

    @Schema(description = "转移原因 (0075)", type = "String", example = "0075-1")
    @NotNull(message = "转移原因不能为空")
    private String transferReason;

    @Schema(description = "备注说明", type = "String", example = "部门调整")
    private String description;
} 