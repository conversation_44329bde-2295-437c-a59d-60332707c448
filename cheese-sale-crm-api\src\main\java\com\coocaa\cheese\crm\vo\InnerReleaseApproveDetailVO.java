package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Accessors(chain = true)
@Schema(name = "InnerReleaseApproveDetailVO", description = "站内审批任务延期申请列表详情返回参数")
public class InnerReleaseApproveDetailVO {

    @Schema(description = "释放改期id")
    private Integer id;

    @Schema(description = "审批任务名称")
    private String approvalName;

    @Schema(description = "申请人")
    @TransField(type = TransTypes.USER)
    private Integer instanceUserId;
    private String instanceUserName;

    @Schema(description = "申请部门")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime instanceCreateTime;

    @Schema(description = "完成时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime endTime;

    @Schema(description = "审批结果，字典(0138)")
    @TransField(type = TransTypes.DICT)
    private String approvalResult;
    private String approvalResultName;

    @Schema(description = "原主体释放日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate advertisingReleaseDate;

    @Schema(description = "申请延期至的日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate applyDelayDate;

    @Schema(description = "品牌")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY, target = "advertisingSubjectName")
    private Integer companyId;
    private String advertisingSubjectName;

    @Schema(description = "商机分配方式(字典0150)")
    @TransField(type = TransTypes.DICT)
    private String assignWay;
    private String assignWayName;

    @Schema(description = "商机来源渠道")
    @TransField(type = TransTypes.CHANNEL)
    private Integer channelId;
    private String channelName;

    @Schema(description = "商机分配日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate assignTime;

    @Schema(description = "当前商机进度(字典0073)")
    @TransField(type = TransTypes.DICT)
    private String progress;
    private String progressName;

    @Schema(description = "情况说明")
    private String remark;

    @Schema(description = "执行状态(字典0151)")
    @TransField(type = TransTypes.DICT)
    private String executeStatus;
    private String executeStatusName;

    @Schema(description = "失败原因(字典0176)")
    @TransField(type = TransTypes.DICT)
    private String failReason;
    private String failReasonName;
}
