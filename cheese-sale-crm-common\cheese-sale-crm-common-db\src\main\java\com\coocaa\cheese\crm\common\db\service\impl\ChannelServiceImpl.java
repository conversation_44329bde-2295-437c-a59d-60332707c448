package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.ChannelEntity;
import com.coocaa.cheese.crm.common.db.mapper.ChannelMapper;
import com.coocaa.cheese.crm.common.db.service.IChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 渠道信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ChannelServiceImpl extends ServiceImpl<ChannelMapper, ChannelEntity> implements IChannelService {

}
