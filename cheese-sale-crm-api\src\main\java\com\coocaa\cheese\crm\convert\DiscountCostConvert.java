package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.DiscountCostParam;
import com.coocaa.cheese.crm.common.db.entity.DiscountCostEntity;
import com.coocaa.cheese.crm.vo.DiscountCostVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 折扣区间信息转换
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Mapper
public interface DiscountCostConvert {
    DiscountCostConvert INSTANCE = Mappers.getMapper(DiscountCostConvert.class);

    /**
     * Entity 转 VO
     */
    DiscountCostVO toVo(DiscountCostEntity entity);

    /**
     * Entity列表 转 VO列表
     */
    List<DiscountCostVO> entityToVO(List<DiscountCostEntity> entityList);

    /**
     * Param 转 Entity
     */
    DiscountCostEntity toEntity(DiscountCostParam param);
} 