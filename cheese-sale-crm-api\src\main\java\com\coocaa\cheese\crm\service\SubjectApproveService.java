package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.InnerApproveApplyQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineUnionDTO;
import com.coocaa.cheese.crm.common.db.bean.SignSubjectApproveDTO;
import com.coocaa.cheese.crm.common.db.bean.SignSubjectProductLineDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import com.coocaa.cheese.crm.common.tools.config.api.InnerApproveConfig;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveResultEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.convert.InnerApproveConvert;
import com.coocaa.cheese.crm.convert.ProductLineConvert;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveApplyPageQueryParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveTaskPageQueryParam;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveApplyVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveTaskVO;
import com.coocaa.cheese.crm.vo.InnerApproveApplyPageVO;
import com.coocaa.cheese.crm.vo.InnerApproveDetailVO;
import com.coocaa.cheese.crm.vo.InnerApproveTaskPageVO;
import com.coocaa.cheese.crm.vo.ProductLineUnionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/30
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SubjectApproveService {

    private static final int APPROVE_ZERO = 0;
    private static final int APPROVE_ONE = 1;
    private static final int APPROVE_TWO = 2;
    private static final int APPROVE_FORTY_EIGHT = 48;

    private final IBusinessService businessService;
    private final InnerApproveService approveService;
    private final InnerApproveConfig innerApproveConfig;
    private final IProductLineService productLineService;
    private final IInnerApproveService innerApproveService;
    private final IAdvertisingSubjectService advertisingSubjectService;

    @AutoTranslate
    public PageResponseVO<InnerApproveTaskPageVO> pageApproveTask(PageRequestVO<InnerApproveTaskQueryParam> pageRequest) {

        PageResponseVO<InnerApproveTaskPageVO> resultPage = new PageResponseVO<>();
        InnerApproveTaskPageQueryParam param = new InnerApproveTaskPageQueryParam();
        param.setRuleCode(innerApproveConfig.getReport());
        param.setFlag(pageRequest.getQuery().getExamine() ? APPROVE_ONE : APPROVE_ZERO);
        param.setApplicant(pageRequest.getQuery().getInstanceUserName());
        PageResponseVO pageResponseVO = approveService.queryTaskPage(param, pageRequest.getCurrentPage(), pageRequest.getPageSize());
        if (CollectionUtil.isEmpty(pageResponseVO.getRows())) {
            return resultPage;
        }
        List<InnerApproveTaskVO> rowList = JSONUtil.toList(JSONUtil.toJsonStr(pageResponseVO.getRows()), InnerApproveTaskVO.class);
        //查询审批code信息
        Set<String> codes = rowList.stream().map(InnerApproveTaskVO::getInstanceCode).collect(Collectors.toSet());
        List<InnerApproveEntity> innerApproveEntities = innerApproveService.queryBizId(codes, StationTargetEnum.CUSTOMER_REPORT.getCode());
        log.info("签约主体报备查询审批任务列表查询审批任务code信息pageRequest:{}, data:{}",
                JSONUtil.toJsonStr(pageRequest), JSONUtil.toJsonStr(innerApproveEntities));
        if (CollectionUtil.isEmpty(innerApproveEntities)) {
            return resultPage;
        }
        //因为InnerApproveEntity表逻辑没办法联查，所以跟主体表分开查询
        List<Integer> bizIds = innerApproveEntities.stream().map(entity -> entity.getBizId().intValue()).toList();
        List<SignSubjectApproveDTO> entityList = advertisingSubjectService.queryInnerSubjectEntity(bizIds);
        log.info("签约主体报备查询审批任务列表查询签约主体信息:{}", JSONUtil.toJsonStr(entityList));
        if (CollectionUtil.isEmpty(entityList)) {
            return resultPage;
        }
        //查询该签约主体绑定的产品线数据
        List<SignSubjectProductLineDTO> subjectProductLineDTOList = productLineService.querySignSubjectProductLine(bizIds);
        //组装map数据
        Map<String, InnerApproveEntity> examineApproveEntityMap = innerApproveEntities.stream()
                .collect(Collectors.toMap(InnerApproveEntity::getInstanceCode, entity -> entity));
        Map<Integer, SignSubjectApproveDTO> subjectMap = entityList.stream()
                .collect(Collectors.toMap(SignSubjectApproveDTO::getId, entity -> entity));

        //组装新的分页数据返回
        resultPage.setCurrentPage(pageResponseVO.getCurrentPage());
        resultPage.setTotal(pageResponseVO.getTotal());
        resultPage.setTotalPages(pageResponseVO.getTotalPages());
        resultPage.setRows(rowList.stream().map(e -> {
            InnerApproveTaskPageVO vo = InnerApproveConvert.INSTANCE.toVo(e);
            if (Objects.nonNull(examineApproveEntityMap.get(e.getInstanceCode()))
                    && Objects.nonNull(subjectMap.get(examineApproveEntityMap.get(e.getInstanceCode()).getBizId().intValue()))) {
                SignSubjectApproveDTO dto = subjectMap.get(examineApproveEntityMap.get(e.getInstanceCode()).getBizId().intValue());
                vo.setId(dto.getId());
                vo.setBrandId(dto.getBrandId());
                vo.setCompanyId(dto.getCompanyId());
                vo.setDepartmentId(e.getDepartId());
                //设置是既有品牌还是新增品牌
                vo.setBrandTimeFlag(setBrandTimeFlag(dto.getEffectiveTime(), e.getInstanceCreateTime()));
                //设置产品线
                vo.setProductLine(setProductLine(subjectProductLineDTOList, dto.getId()));
            } else {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).toList());

        return resultPage;
    }

    @AutoTranslate
    public InnerApproveDetailVO queryApproveDetail(Integer id) {

        InnerApproveEntity entity = innerApproveService.lambdaQuery()
                .eq(InnerApproveEntity::getBizId, id)
                .eq(InnerApproveEntity::getType, StationTargetEnum.CUSTOMER_REPORT.getCode())
                .orderByDesc(InnerApproveEntity::getCreateTime)
                .last("LIMIT 1")
                .one();
        log.info("签约主体报备查询审批信息详情id:{}, data:{}", id, JSONUtil.toJsonStr(entity));
        if (Objects.isNull(entity)) {
            throw new BusinessException("查询审批信息详情未找到审批code");
        }
        //查询品牌+主体信息
        InnerApproveDetailDTO dto = advertisingSubjectService.queryInnerApproveDetail(id);
        log.info("签约主体报备查询审批信息详情查询品牌+主体信息:{}", JSONUtil.toJsonStr(dto));
        InnerApproveDetailVO vo = InnerApproveConvert.INSTANCE.toVo(dto);
        //查询审批信息
        InnerApproveInstanceVO innerApproveInstanceVO = approveService.queryDetail(entity.getInstanceCode());
        if (Objects.nonNull(innerApproveInstanceVO)) {
            vo.setApprovalName(innerApproveInstanceVO.getApprovalName());
            vo.setInstanceUserId(innerApproveInstanceVO.getUserId());
            vo.setInstanceCreateTime(innerApproveInstanceVO.getCreateTime());
            vo.setEndTime(innerApproveInstanceVO.getEndTime());
            vo.setApprovalResult(innerApproveInstanceVO.getApprovalResult());
            vo.setDepartmentId(innerApproveInstanceVO.getDepartId());
        }
        //设置是既有品牌还是新增品牌
        vo.setBrandTimeFlag(setBrandTimeFlag(dto.getEffectiveTime(), Objects.isNull(innerApproveInstanceVO) ? null : innerApproveInstanceVO.getCreateTime()));
        //查询该签约主体绑定的产品线数据
        List<SignSubjectProductLineDTO> subjectProductLineDTOList = productLineService.querySignSubjectProductLine(Collections.singletonList(dto.getId()));
        //设置产品线
        vo.setProductLine(setProductLine(subjectProductLineDTOList, dto.getId()));

        return vo;
    }

    @AutoTranslate
    public List<ProductLineUnionVO> queryBrandUnionProductLine(Integer id) {

        //查询品牌信息
        InnerApproveDetailDTO dto = Optional.ofNullable(advertisingSubjectService.queryInnerApproveDetail(id))
                .orElseThrow(() -> new BusinessException("品牌报备查询品牌信息详情未找到品牌信息"));
        //查询该品牌已报备的签约主体及产品线
        List<ProductLineUnionDTO> inBrandProductLineList = productLineService.queryInBrandUnionProductLine(dto.getBrandId());
        log.info("签约主体报备查询该签约主体关联信息详情:{}", JSONUtil.toJsonStr(inBrandProductLineList));
        if (CollectionUtil.isEmpty(inBrandProductLineList)) {
            return Collections.emptyList();
        }
        //获取所有签约主体对应公司id
        List<Integer> signSubjectIds = inBrandProductLineList.stream().map(ProductLineUnionDTO::getSignSubjectId).distinct().toList();
        List<AdvertisingSubjectEntity> advertisingSubjectEntityList = advertisingSubjectService.lambdaQuery()
                .select(AdvertisingSubjectEntity::getCompanyId)
                .in(AdvertisingSubjectEntity::getId, signSubjectIds)
                .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
        List<Integer> companyIds = advertisingSubjectEntityList.stream().map(AdvertisingSubjectEntity::getCompanyId).distinct().toList();
        //查询该签约主体绑定公司对应签约主体绑定产品线
        List<ProductLineUnionDTO> outBrandProductLineList = productLineService.queryOutBrandUnionProductLine(companyIds);
        log.info("签约主体报备查询该签约主体绑定公司对应签约主体绑定产品线:{}", JSONUtil.toJsonStr(outBrandProductLineList));
        if (CollectionUtil.isEmpty(outBrandProductLineList)) {
            return Collections.emptyList();
        }

        return outBrandProductLineList.stream().map(ProductLineConvert.INSTANCE::toVo).toList();
    }

    @AutoTranslate
    public PageResponseVO<InnerApproveApplyPageVO> pageApplyApprove(PageRequestVO<InnerApproveApplyQueryParam> pageRequest) {

        PageResponseVO<InnerApproveApplyPageVO> resultPage = new PageResponseVO<>();
        InnerApproveApplyPageQueryParam param = new InnerApproveApplyPageQueryParam();
        param.setRuleCode(innerApproveConfig.getReport());
        param.setFlag(pageRequest.getQuery().getExamine() ? APPROVE_ONE : APPROVE_ZERO);
        param.setSortFiled(pageRequest.getQuery().getExamine() ? "endTime" : "createTime");
        PageResponseVO pageResponseVO = approveService.queryApplyPage(param, pageRequest.getCurrentPage(), pageRequest.getPageSize());
        if (CollectionUtil.isEmpty(pageResponseVO.getRows())) {
            return resultPage;
        }
        List<InnerApproveApplyVO> rowList = JSONUtil.toList(JSONUtil.toJsonStr(pageResponseVO.getRows()), InnerApproveApplyVO.class);
        //查询审批申请code信息
        Set<String> codes = rowList.stream().map(InnerApproveApplyVO::getInstanceCode).collect(Collectors.toSet());
        List<InnerApproveEntity> innerApproveEntities = innerApproveService.queryBizId(codes, StationTargetEnum.CUSTOMER_REPORT.getCode());
        log.info("签约主体报备查询审批申请code信息pageRequest:{},:{}", JSONUtil.toJsonStr(pageRequest), JSONUtil.toJsonStr(innerApproveEntities));
        if (CollectionUtil.isEmpty(innerApproveEntities)) {
            return resultPage;
        }
        List<Integer> bizIds = innerApproveEntities.stream().map(entity -> entity.getBizId().intValue()).toList();
        List<SignSubjectApproveDTO> entityList = advertisingSubjectService.queryInnerSubjectEntity(bizIds);
        log.info("签约主体报备查询审批申请查询签约主体信息:{}", JSONUtil.toJsonStr(entityList));
        if (CollectionUtil.isEmpty(entityList)) {
            return resultPage;
        }
        //查询该签约主体绑定的产品线数据
        List<SignSubjectProductLineDTO> subjectProductLineDTOList = productLineService.querySignSubjectProductLine(bizIds);
        //组装map数据
        Map<String, InnerApproveEntity> examineApproveEntityMap = innerApproveEntities.stream()
                .collect(Collectors.toMap(InnerApproveEntity::getInstanceCode, entity -> entity));
        Map<Integer, SignSubjectApproveDTO> subjectMap = entityList.stream()
                .collect(Collectors.toMap(SignSubjectApproveDTO::getId, entity -> entity));
        //组装新的分页数据返回
        resultPage.setCurrentPage(pageResponseVO.getCurrentPage());
        resultPage.setTotal(pageResponseVO.getTotal());
        resultPage.setTotalPages(pageResponseVO.getTotalPages());
        resultPage.setRows(rowList.stream().map(e -> {
            InnerApproveApplyPageVO vo = InnerApproveConvert.INSTANCE.toVo(e);
            vo.setInstanceCreateTime(e.getCreateTime());
            if (Objects.nonNull(examineApproveEntityMap.get(e.getInstanceCode()))
                    && Objects.nonNull(subjectMap.get(examineApproveEntityMap.get(e.getInstanceCode()).getBizId().intValue()))) {
                SignSubjectApproveDTO dto = subjectMap.get(examineApproveEntityMap.get(e.getInstanceCode()).getBizId().intValue());
                vo.setId(dto.getId());
                vo.setBrandId(dto.getBrandId());
                vo.setCompanyId(dto.getCompanyId());
                //设置是否显示建商机按钮
                vo.setCreateBusinessFlag(setBusinessFlag(examineApproveEntityMap.get(e.getInstanceCode()).getBizId().intValue(), e));
                //设置是既有品牌还是新增品牌
                vo.setBrandTimeFlag(setBrandTimeFlag(dto.getEffectiveTime(), e.getCreateTime()));
                //设置产品线
                vo.setProductLine(setProductLine(subjectProductLineDTOList, dto.getId()));
            } else {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).toList());

        return resultPage;
    }

    private Integer setBrandTimeFlag(LocalDateTime effectiveTime, LocalDateTime applyTime) {

        log.info("设置签约主体时间标识effectiveTime:{}, applyTime:{}", effectiveTime, applyTime);
        // 新增品牌  若品牌数据对应的【生效时间】为空 或 ＞审批单【开始时间】
        if (Objects.isNull(effectiveTime) || (Objects.nonNull(applyTime) && effectiveTime.isAfter(applyTime))) {
            return APPROVE_ONE;
        }
        return APPROVE_TWO;
    }

    private String setProductLine(List<SignSubjectProductLineDTO> productLineList, Integer id) {

        log.info("品牌报备设置签约主体id: {}, 产品线:{}", id, JSONUtil.toJsonStr(productLineList));
        if (CollectionUtil.isEmpty(productLineList)) {
            return Strings.EMPTY;
        }

        Map<Integer, List<SignSubjectProductLineDTO>> productLineMap = productLineList.stream()
                .collect(Collectors.groupingBy(SignSubjectProductLineDTO::getId));
        if (Objects.isNull(productLineMap.get(id))) {
            return Strings.EMPTY;
        }
        return productLineMap.get(id).stream().map(SignSubjectProductLineDTO::getName).collect(Collectors.joining(Constants.COMMA));
    }

    private Boolean setBusinessFlag(Integer bizId, InnerApproveApplyVO vo) {

        //查询是否已经创建过商机
        //显示： 【审批结果】=通过 & 【完成时间】≤48小时的数据
        List<BusinessEntity> businessEntityList = businessService.lambdaQuery()
                .eq(BusinessEntity::getAdvertisingSubjectId, bizId)
                .list();
        log.info("设置是否显示建商机按钮bizId:{}, vo:{}, entity:{}", bizId, JSONUtil.toJsonStr(vo), JSONUtil.toJsonStr(businessEntityList));
        if (Objects.equals(vo.getApprovalStatus(), InnerApproveStatusEnum.ALREADY_FINISH.getCode())
                && Objects.equals(vo.getApprovalResult(), InnerApproveResultEnum.AGREE.getCode())
                && vo.getEndTime().plusHours(APPROVE_FORTY_EIGHT).isAfter(LocalDateTime.now())
                && CollectionUtil.isEmpty(businessEntityList)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
