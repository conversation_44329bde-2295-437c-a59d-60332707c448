<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.BrandMapper">
    <!-- 按条件查询品牌列表 -->
    <select id="pageList" resultType="com.coocaa.cheese.crm.common.db.entity.BrandEntity">
        SELECT cb.id, cb.name, cb.parent_id, cb.company_id, cb.industry_code, cb.logo_url, cb.update_time, cb.operator,
        cb.effective_status, cb.effective_time, cb.creator, cb.create_time
        FROM sale_comm_brand cb
        <include refid="whereSql"/>
        ORDER BY cb.update_time DESC, id
    </select>

    <!-- 按条件查询品牌列表 -->
    <select id="h5PageList" resultType="com.coocaa.cheese.crm.common.db.entity.BrandEntity">
        SELECT cb.id, cb.parent_id, cb.name, cb.company_id, cb.industry_code
        FROM sale_comm_brand cb
        <include refid="whereSql"/>
        ORDER BY CONVERT(cb.name USING gbk) COLLATE gbk_chinese_ci, cb.update_time DESC, id
    </select>

    <!-- 公共查询条件 -->
    <sql id="whereSql">
        <where>
            AND cb.delete_flag = 0
            <if test="condition.effectiveStatus != null">
                AND cb.effective_status = #{condition.effectiveStatus}
            </if>
            <if test="condition.createStartDate != null">
                AND cb.create_time &gt;= #{condition.createStartDate}
            </if>
            <if test="condition.createEndDate != null">
                AND cb.create_time &lt; #{condition.createEndDate}
            </if>
            <if test="condition.creator != null">
                AND cb.creator = #{condition.creator}
            </if>
            <if test="condition.departmentId != null and condition.departmentId != ''">
                AND cb.department_id = #{condition.departmentId}
            </if>
             <if test="condition.creatorList != null and condition.creatorList.size() > 0">
                AND cb.creator IN
                <foreach collection="condition.creatorList" item="item" open="(" close=")" separator=",">#{item}</foreach>
             </if>
            <if test="condition.name != null and condition.name != ''">
                AND (
                <choose>
                    <when test="condition.exactFlag == 1">cb.name = #{condition.name}</when>
                    <otherwise>cb.name LIKE CONCAT('%',#{condition.name},'%')</otherwise>
                </choose>
                OR (cb.id IN (
                SELECT cb.id FROM sale_comm_brand_tag tag
                LEFT JOIN sale_comm_brand cb ON cb.id = tag.brand_id
                WHERE
                <choose>
                    <when test="condition.exactFlag == 1">tag.name = #{condition.name}</when>
                    <otherwise>tag.name LIKE CONCAT('%',#{condition.name},'%')</otherwise>
                </choose>
                )))
            </if>

            <if test="condition.industryCode != null and condition.industryCode != ''">
                AND cb.industry_code = #{condition.industryCode}
            </if>

            <if test="condition.companyName != null and condition.companyName != ''">
                AND cb.id IN (
                SELECT cb.id FROM sale_comm_company scc
                LEFT JOIN sale_comm_brand cb ON cb.company_id = scc.id
                WHERE scc.name LIKE CONCAT('%',#{condition.companyName},'%')
                )
            </if>
        </where>
    </sql>

</mapper>
