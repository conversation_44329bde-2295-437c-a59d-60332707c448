package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 折扣累计消耗金额表
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@TableName("sale_crm_discount_cost")
public class DiscountCostEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构折扣规则ID
     */
    private Integer ruleId;

    /**
     * 当前累计消耗金额(元)
     */
    private BigDecimal cost;

    /**
     * 区间标记[0:开, 1:闭, 2:正无穷]
     */
    private Integer closedFlag;

    /**
     * 折扣系数
     */
    private BigDecimal discount;

    /**
     * 增加累计消耗金额(元)
     */
    private BigDecimal costStep;

    /**
     * 折扣系数降低
     */
    private BigDecimal discountDecrease;

    /**
     * 不超过的系数
     */
    private BigDecimal discountLimit;
} 