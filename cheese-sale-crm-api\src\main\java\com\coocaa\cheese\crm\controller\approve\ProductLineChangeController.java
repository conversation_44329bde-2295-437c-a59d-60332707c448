
package com.coocaa.cheese.crm.controller.approve;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.InnerApproveApplyQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.bean.ProductLineInitiateParam;
import com.coocaa.cheese.crm.service.ProductLineChangeService;
import com.coocaa.cheese.crm.vo.InnerApprovePendingCountVO;
import com.coocaa.cheese.crm.vo.InnerApproveProductApplyPageVO;
import com.coocaa.cheese.crm.vo.InnerProductApproveDetailVO;
import com.coocaa.cheese.crm.vo.InnerProductApproveTaskPageVO;
import com.coocaa.cheese.crm.vo.ProductLineInitiateVO;
import com.coocaa.cheese.crm.vo.ProductLineUnionVO;
import com.coocaa.cheese.crm.vo.ProductLineVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 产品线变更 控制器
 *
 * <AUTHOR>
 * @since 2025-6-18
 */
@Slf4j
@RestController
@RequestMapping("/product/line/change")
@Tag(name = "产品线变更", description = "产品线变更申请管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ProductLineChangeController {

    private final ProductLineChangeService productLineChangeService;

    @Operation(summary = "产品线变更申请查询")
    @Parameter(name = "id", description = "商机id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}/initiate")
    public ResultTemplate<ProductLineInitiateVO> queryInitiate(@PathVariable("id") Integer id) {

        return ResultTemplate.success(productLineChangeService.queryInitiate(id));
    }

    @Operation(summary = "发起产品线变更申请")
    @PostMapping("")
    public ResultTemplate<Long> initiate(@RequestBody @Validated ProductLineInitiateParam param) {

        return ResultTemplate.success(productLineChangeService.initiate(param));
    }

    @Operation(summary = "查询审批任务列表(分页)")
    @PostMapping("/task/page")
    public ResultTemplate<PageResponseVO<InnerProductApproveTaskPageVO>> pageApproveTask(@Validated @RequestBody PageRequestVO<InnerApproveTaskQueryParam> pageRequest) {

        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(InnerApproveTaskQueryParam::new));
        return ResultTemplate.success(productLineChangeService.pageApproveTask(pageRequest));
    }

    @Operation(summary = "查询审批信息详情")
    @Parameter(name = "id", description = "产品线变更id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<InnerProductApproveDetailVO> queryApproveDetail(@PathVariable("id") Long id) {

        return ResultTemplate.success(productLineChangeService.queryApproveDetail(id));
    }

    @Operation(summary = "关联检查查询")
    @Parameter(name = "id", description = "产品线变更id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}/union")
    public ResultTemplate<List<ProductLineUnionVO>> queryBrandUnionProductLine(@PathVariable("id") Long id) {

        return ResultTemplate.success(productLineChangeService.queryBrandUnionProductLine(id));
    }

    @Operation(summary = "查询审批申请列表(分页)")
    @PostMapping("/apply/page")
    public ResultTemplate<PageResponseVO<InnerApproveProductApplyPageVO>> pageApplyApprove(@Validated @RequestBody PageRequestVO<InnerApproveApplyQueryParam> pageRequest) {

        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(InnerApproveApplyQueryParam::new));
        return ResultTemplate.success(productLineChangeService.pageApplyApprove(pageRequest));
    }

    @Operation(summary = "查询审批列表待办数量")
    @GetMapping("/num")
    public ResultTemplate<List<InnerApprovePendingCountVO>> queryApprovePendingCount() {

        return ResultTemplate.success(productLineChangeService.queryApprovePendingCount());
    }

    @Operation(summary = "根据签约主体查询产品线信息")
    @GetMapping("/{id}/sign")
    public ResultTemplate<List<ProductLineVO>> queryProductInfoBySign(@PathVariable("id") Integer id) {

        return ResultTemplate.success(productLineChangeService.queryProductInfoBySign(id));
    }

    @Operation(summary = "根据产品线Id查询产品线信息")
    @GetMapping("/{id}/product")
    public ResultTemplate<ProductLineVO> queryProductInfoByProduct(@PathVariable("id") Long id) {

        return ResultTemplate.success(productLineChangeService.queryProductInfoByProduct(id));
    }
}