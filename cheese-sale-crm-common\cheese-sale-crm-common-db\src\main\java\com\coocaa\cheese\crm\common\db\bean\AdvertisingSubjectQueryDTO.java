package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 查询条件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
public class AdvertisingSubjectQueryDTO {
    /**
     * 签约主体类型(字典0068)
     */
    private String type;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 所属公司名称
     */
    private String companyName;

    /**
     * 是否TOP客户 [0:否, 1:是]
     */
    private Integer topFlag;

    /**
     * 生效状态
     */
    private Integer effectiveStatus;

    /**
     * 是否公海保护期
     */
    private Integer protectionPeriodFlag;

    /**
     * 创建开始日期
     */
    private LocalDate createStartDate;

    /**
     * 创建结束日期
     */
    private LocalDate createEndDate;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 部门ID
     */
    private String departmentId;


    /**
     * 创建人数组
     */
    private List<Integer> creatorList;

    /**
     * 搜索关键词(品牌名称、签约主体名称)
     */
    private String keyword;

    /**
     * 需要排除的签约主体ID列表
     */
    private List<Integer> excludeSubjectIds;
}
