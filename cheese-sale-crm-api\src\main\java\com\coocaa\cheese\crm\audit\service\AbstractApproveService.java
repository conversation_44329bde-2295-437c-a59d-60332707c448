package com.coocaa.cheese.crm.audit.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.audit.bean.ApproveRequest;
import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveDetailVO;
import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveParam;
import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveVO;
import com.coocaa.cheese.crm.bean.InnerApproveTemplateParam;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.tools.config.api.InnerApproveConfig;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveApplyPageQueryParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveTaskPageQueryParam;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveApplyVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveTaskVO;
import com.coocaa.cheese.crm.service.InnerApproveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 通用审批服务抽象类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public abstract class AbstractApproveService<T extends BaseInnerApproveDetailVO,
        K extends ApproveRequest,
        D extends BaseInnerApproveVO,
        E extends BaseInnerApproveParam> {

    private static final int APPROVE_ZERO = 0;
    private static final int APPROVE_ONE = 1;
    private static final long APPROVE_ZERO_L = 0L;
    protected final InnerApproveConfig innerApproveConfig;
    protected final InnerApproveService approveService;
    protected final IInnerApproveService innerApproveService;

    // ——————————————————————————————
    // 子类必须实现的方法
    // ——————————————————————————————

    /**
     * 获取业务类型枚举
     */
    protected abstract StationTargetEnum getStationTarget();

    /**
     * 获取配置规则code
     */
    protected abstract Integer getInnerApproveConfig();

    /**
     * 根据 ID 查询业务详情 DTO
     */
    protected abstract T getInnerApproveDetail(Long id, Long bizId);


    /**
     * 根据 ID 查询业务详情 ID
     */
    protected abstract Long getInnerApproveDetailBizId(Long id);

    /**
     * 保存业务记录
     */
    protected abstract Long saveRecord(K param);

    /**
     * 构建审批参数列表
     */
    protected abstract List<InnerApproveTemplateParam> buildApproveList(K p);

    /**
     * 构建审批参数列表
     */
    protected abstract List<D> setApproveTaskData(List<InnerApproveTaskVO> rowList, PageRequestVO<E> e, List<InnerApproveEntity> innerApproveEntities);

    /**
     * 构建审批参数列表
     */
    protected abstract List<D> setApproveApplyData(List<InnerApproveApplyVO> rowList, PageRequestVO<E> e, List<InnerApproveEntity> innerApproveEntities);


    /**
     * 数据校验
     */
    protected abstract void checkInitiate(K param);

    // ——————————————————————————————
    // 通用审批方法
    // ——————————————————————————————

    @AutoTranslate
    public T queryInnerApproveDetail(Long bizId) {
        log.info("查询审批详情bizId:{}", bizId);
        return getInnerApproveDetail(null, bizId);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long initiate(K param) {
        // 校验数据
        checkInitiate(param);
        // 保存记录表
        Long id = saveRecord(param);
        // 发起申请
        Boolean approval = approveService.initiateApproval(id, getStationTarget(),
                getInnerApproveConfig(), buildApproveList(param));
        if (!approval) {
            throw new BusinessException("申请记录表创建失败!");
        }
        return id;
    }

    @AutoTranslate
    public PageResponseVO<D> pageApproveTask(PageRequestVO<E> pageRequest) {
        log.info("查询审批任务列表查询审批任务code信息,pageRequest:{}", JSONUtil.toJsonStr(pageRequest));
        InnerApproveTaskPageQueryParam param = new InnerApproveTaskPageQueryParam();
        param.setRuleCode(getInnerApproveConfig());
        param.setFlag(pageRequest.getQuery().getExamine() ? APPROVE_ONE : APPROVE_ZERO);
        param.setApplicant(pageRequest.getQuery().getInstanceUserName());
        PageResponseVO<?> pageResponseVO = approveService.queryTaskPage(param, pageRequest.getCurrentPage(), pageRequest.getPageSize());
        if (CollectionUtil.isEmpty(pageResponseVO.getRows())) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        List<InnerApproveTaskVO> rowList = JSONUtil.toList(JSONUtil.toJsonStr(pageResponseVO.getRows()), InnerApproveTaskVO.class);
        // 查询审批code信息
        List<InnerApproveEntity> innerApproveEntities = getInnerApproveEntities(rowList.stream().map(InnerApproveTaskVO::getInstanceCode));
        if (CollectionUtil.isEmpty(innerApproveEntities)) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        // 组装新的分页数据返回
        PageResponseVO<D> resultPage = new PageResponseVO<>();
        resultPage.setCurrentPage(pageResponseVO.getCurrentPage());
        resultPage.setTotal(pageResponseVO.getTotal());
        resultPage.setTotalPages(pageResponseVO.getTotalPages());
        List<D> r = setApproveTaskData(rowList, pageRequest, innerApproveEntities);
        resultPage.setRows(r);
        return resultPage;
    }

    @AutoTranslate
    public T queryApproveDetail(Long id) {

        InnerApproveEntity entity = innerApproveService.lambdaQuery()
                .eq(InnerApproveEntity::getBizId, id)
                .eq(InnerApproveEntity::getType, getStationTarget().getCode())
                .orderByDesc(InnerApproveEntity::getCreateTime)
                .last("LIMIT 1")
                .one();
        log.info("申请查询审批信息详情id:{}, data:{}", id, JSONUtil.toJsonStr(entity));
        if (Objects.isNull(entity)) {
            throw new BusinessException("申请查询审批信息详情未找到审批信息");
        }
        // 查询投放主体关联品牌
        T dto = getInnerApproveDetail(id, getInnerApproveDetailBizId(id));
        log.info("发起申请查询投放主体id:{}, dto:{}", id, JSONUtil.toJsonStr(dto));
        // 查询审批信息
        InnerApproveInstanceVO innerApproveInstanceVO = approveService.queryDetail(entity.getInstanceCode());
        if (Objects.nonNull(innerApproveInstanceVO)) {
            dto.setApprovalName(innerApproveInstanceVO.getApprovalName());
            dto.setInstanceUserId(innerApproveInstanceVO.getUserId());
            dto.setInstanceCreateTime(innerApproveInstanceVO.getCreateTime());
            dto.setEndTime(innerApproveInstanceVO.getEndTime());
            dto.setApprovalResult(innerApproveInstanceVO.getApprovalResult());
            dto.setDepartmentId(innerApproveInstanceVO.getDepartId());
        }
        return dto;
    }

    @AutoTranslate
    public PageResponseVO<D> pageApplyApprove(PageRequestVO<E> pageRequest) {
        log.info("查询审批任务列表查询审批任务code信息,pageRequest:{}", JSONUtil.toJsonStr(pageRequest));
        InnerApproveApplyPageQueryParam param = new InnerApproveApplyPageQueryParam();
        param.setRuleCode(getInnerApproveConfig());
        param.setFlag(pageRequest.getQuery().getExamine() ? APPROVE_ONE : APPROVE_ZERO);
        param.setSortFiled(pageRequest.getQuery().getExamine() ? "endTime" : "createTime");
        PageResponseVO<?> pageResponseVO = approveService.queryApplyPage(param, pageRequest.getCurrentPage(), pageRequest.getPageSize());
        if (CollectionUtil.isEmpty(pageResponseVO.getRows())) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        List<InnerApproveApplyVO> rowList = JSONUtil.toList(JSONUtil.toJsonStr(pageResponseVO.getRows()), InnerApproveApplyVO.class);
        List<InnerApproveEntity> innerApproveEntities = getInnerApproveEntities(rowList.stream().map(InnerApproveApplyVO::getInstanceCode));
        if (CollectionUtil.isEmpty(innerApproveEntities)) {
            return new PageResponseVO<>(APPROVE_ZERO_L);
        }
        // 组装新的分页数据返回
        PageResponseVO<D> resultPage = new PageResponseVO<>();
        resultPage.setCurrentPage(pageResponseVO.getCurrentPage());
        resultPage.setTotal(pageResponseVO.getTotal());
        resultPage.setTotalPages(pageResponseVO.getTotalPages());
        List<D> r = setApproveApplyData(rowList, pageRequest, innerApproveEntities);
        resultPage.setRows(r);
        return resultPage;
    }

    private List<InnerApproveEntity> getInnerApproveEntities(Stream<String> rowList) {
        // 查询审批code信息
        Set<String> codes = rowList.collect(Collectors.toSet());
        List<InnerApproveEntity> innerApproveEntities = innerApproveService.queryBizId(codes, getStationTarget().getCode());
        log.info("查询审批任务列表查询审批任务code结果，data:{}", JSONUtil.toJsonStr(innerApproveEntities));
        return innerApproveEntities;
    }
}
