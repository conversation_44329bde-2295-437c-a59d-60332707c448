package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.ProductLineSignSubjectParam;
import com.coocaa.cheese.crm.common.db.bean.ProductLineSignSubjectDTO;
import com.coocaa.cheese.crm.common.db.entity.ProductLineSignSubjectEntity;
import com.coocaa.cheese.crm.vo.ProductLineSignSubjectVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 产品线签约主体关系表 信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-6-17
 */
@Mapper
public interface ProductLineSignSubjectConvert extends PageableConvert<ProductLineSignSubjectEntity, ProductLineSignSubjectVO> {
    ProductLineSignSubjectConvert INSTANCE = Mappers.getMapper(ProductLineSignSubjectConvert.class);

    /**
     * Entity 转 VO
     */
    ProductLineSignSubjectVO toVo(ProductLineSignSubjectEntity entity);

    /**
     * 转换查询参数
     */
    ProductLineSignSubjectDTO toDto(ProductLineSignSubjectParam param);

    /**
     * VO 转 Entity
     */
    ProductLineSignSubjectEntity toEntity(ProductLineSignSubjectVO vo);

    /**
     * Param 转 Entity
     */
    ProductLineSignSubjectEntity toEntity(ProductLineSignSubjectParam param);
}