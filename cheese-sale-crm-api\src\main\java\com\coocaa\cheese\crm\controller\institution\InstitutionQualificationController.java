package com.coocaa.cheese.crm.controller.institution;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.InstitutionQualificationParam;
import com.coocaa.cheese.crm.bean.InstitutionQualificationQueryParam;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.InstitutionQualificationService;
import com.coocaa.cheese.crm.vo.InstitutionQualificationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 机构资质管理
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@RestController
@RequestMapping("/institution-qualifications")
@Tag(name = "机构资质管理", description = "机构资质管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionQualificationController extends BaseController {
    private final InstitutionQualificationService institutionQualificationService;

    /**
     * 分页查询机构资质列表
     */
    @Operation(summary = "分页查询机构资质列表")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<InstitutionQualificationVO>> pageListInstitutionQualification(@RequestBody PageRequestVO<InstitutionQualificationQueryParam> pageRequest) {
        return ResultTemplate.success(institutionQualificationService.pageListInstitutionQualification(pageRequest));
    }

    /**
     * 获取机构资质列表
     */
    @Operation(summary = "获取机构资质列表")
    @Parameter(name = "institutionId", description = "机构账户ID", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "int"))
    @GetMapping("/list")
    public ResultTemplate<List<InstitutionQualificationVO>> listInstitutionQualification(@RequestParam("institutionId") Integer institutionId) {
        return ResultTemplate.success(institutionQualificationService.listInstitutionQualification(institutionId));
    }

    /**
     * 上传机构资质
     */
    @Operation(summary = "上传机构资质")
    @PostMapping("/upload")
    public ResultTemplate<Boolean> uploadInstitutionQualification(@RequestBody @Validated InstitutionQualificationParam param) {
        return ResultTemplate.success(institutionQualificationService.uploadInstitutionQualification(param));
    }
} 