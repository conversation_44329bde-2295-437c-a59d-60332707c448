package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 签约主体创建参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
public class AdvertisingSubjectParam {
    @NotBlank(message = "签约主体类型不能为空")
    @Schema(description = "签约主体类型(字典0068)", type = "String", example = "0068-6")
    private String type;

    @NotNull(message = "品牌ID不能为空")
    @Schema(description = "品牌ID", type = "Integer", example = "1")
    private Integer brandId;

    @NotNull(message = "所属公司ID不能为空")
    @Schema(description = "所属公司ID", type = "Integer", example = "1")
    private Integer companyId;

    @Schema(description = "是否TOP客户 [0:否, 1:是]", type = "Integer", example = "0")
    private Integer topFlag;

    @Schema(description = "证明材料", type = "String", example = "http://example.com/evidence.pdf")
    private String evidenceUrl;

    @Size(max = 20, message = "备注说明不能超过{max}个字符")
    @Schema(description = "备注说明", type = "String", example = "重点客户签约主体")
    private String description;

    @Schema(description = "生效状态(1:生效,0:不生效)", type = "Integer", example = "1")
    private Integer effectiveStatus;

    @Schema(description = "是否公海保护期 [0:否, 1:是]", type = "Integer", example = "0")
    private Integer protectionPeriodFlag;
}
