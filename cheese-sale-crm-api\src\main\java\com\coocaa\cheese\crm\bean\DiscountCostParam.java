package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 折扣区间参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "折扣区间参数")
public class DiscountCostParam {

    @Schema(description = "当前累计消耗", example = "100000")
    private BigDecimal cost;

    @NotNull(message = "区间标记不能为空")
    @Schema(description = "区间标记 [0:开, 1:闭 ,2: 正无穷]", example = "0")
    private Integer closedFlag;

    @NotNull(message = "折扣系数不能为空")
    @Schema(description = "折扣系数", example = "0.95")
    private BigDecimal discount;

    @Schema(description = "增加预算金额(元)", example = "50000")
    private BigDecimal costStep;

    @Schema(description = "折扣系数降低", example = "0.01")
    private BigDecimal discountDecrease;

    @Schema(description = "不超过的系数", example = "0.90")
    private BigDecimal discountLimit;
} 