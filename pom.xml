<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.coocaa.ad</groupId>
    <artifactId>cheese-sale-crm</artifactId>
    <version>1.0.0</version>
    <name>cheese-sale-crm</name>
    <packaging>pom</packaging>

    <modules>
        <module>cheese-sale-crm-common</module>
        <module>cheese-sale-crm-api</module>
    </modules>

    <properties>
        <!-- 基础配置 -->
        <java.version>17</java.version>
        <maven-compiler-plugin>3.7.0</maven-compiler-plugin>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.boot.version>3.2.9</spring.boot.version>

        <!-- 项目模块版本 -->
        <cheese.tools.version>1.0.0</cheese.tools.version>  <!-- 工具模块版本 -->
        <cheese.db.version>1.0.0</cheese.db.version>  <!-- 数据库模块版本 -->
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-common-dependencies</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 项目内部模块依赖 -->
            <!--通用工具模块-->
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-sale-crm-common-tools</artifactId>
                <version>${cheese.tools.version}</version>
            </dependency>
            <!--数据库操作模块-->
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-sale-crm-common-db</artifactId>
                <version>${cheese.db.version}</version>
            </dependency>

            <!-- 通用web -->
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-common-web</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>

            <!-- 数据权限 -->
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-data-permission-starter</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <!-- 翻译器 -->
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-translate-starter</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <!-- 数据相关 -->
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-common-data</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!-- MQ数据相关 -->
            <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>cheese-common-mq</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!-- 日志相关 -->
             <dependency>
                <groupId>com.coocaa.ad</groupId>
                <artifactId>logback-config</artifactId>
                <version>1.0.0-SNAPSHOT</version>
             </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 配置将项目发布到 nexus私服中 没有则注释 -->
    <distributionManagement>
        <repository>
            <id>coocaa-nexus-releases</id>
            <url>http://172.20.135.19:8080/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>coocaa-nexus-snapshots</id>
            <url>http://172.20.135.19:8080/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
