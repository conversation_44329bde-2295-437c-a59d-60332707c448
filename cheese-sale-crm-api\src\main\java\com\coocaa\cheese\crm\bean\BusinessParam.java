package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 商机管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
@Schema(name = "BusinessParam", description = "商机参数")
public class BusinessParam {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @NotNull(message = "渠道ID不能为空")
    @Schema(description = "渠道ID", type = "Integer", example = "1")
    private Integer channelId;

    @NotNull(message = "品牌ID不能为空")
    @Schema(description = "品牌ID", type = "Integer", example = "1")
    private Integer brandId;

    @NotNull(message = "归属人ID不能为空")
    @Schema(description = "归属人ID", type = "Integer", example = "1")
    private Integer ownerId;

    @Size(max = 20, message = "归属人姓名不能超过20字")
    @Schema(description = "归属人姓名")
    private String ownerName;

    @NotBlank(message = "管理部门ID不能为空")
    @Schema(description = "管理部门ID", type = "Integer", example = "1")
    private String departmentId;

    @Schema(description = "管理部门名称", type = "String", example = "销售部")
    private String departmentName;

    @Schema(description = "签约主体ID", type = "Integer", example = "1")
    private Integer advertisingSubjectId;

    @Size(max = 50, message = "备注说明不能超过50字")
    @Schema(description = "备注说明", type = "String", example = "重点商机", maxLength = 50)
    private String description;

    @Valid
    @Schema(description = "商机联系人列表", type = "List")
    private List<BusinessContactParam> businessContactParams;
} 