package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.TranslatorFactory;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectParam;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectPublicSeaQueryParam;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectQueryH5Param;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectQueryParam;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectReportH5Param;
import com.coocaa.cheese.crm.bean.BrandReportH5Param;
import com.coocaa.cheese.crm.bean.InnerApproveTemplateParam;
import com.coocaa.cheese.crm.bean.ProductLineH5Param;
import com.coocaa.cheese.crm.bean.ReportH5Param;
import com.coocaa.cheese.crm.common.db.bean.AdvertisingSubjectQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineSignSubjectEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBrandService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import com.coocaa.cheese.crm.common.db.service.IProductLineSignSubjectService;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.common.tools.config.api.InnerApproveConfig;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.ApproveFieldTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.common.tools.enums.SubjectOperationEnum;
import com.coocaa.cheese.crm.convert.AdvertisingSubjectConvert;
import com.coocaa.cheese.crm.convert.AdvertisingSubjectH5Convert;
import com.coocaa.cheese.crm.convert.AdvertisingSubjectPublicSeaConvert;
import com.coocaa.cheese.crm.convert.BusinessConvert;
import com.coocaa.cheese.crm.kafka.producer.CrmKafkaProducer;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.cheese.crm.vo.AdvertisingSubjectH5VO;
import com.coocaa.cheese.crm.vo.AdvertisingSubjectPublicSeaVO;
import com.coocaa.cheese.crm.vo.AdvertisingSubjectVO;
import com.coocaa.cheese.crm.vo.BusinessVO;
import com.coocaa.cheese.crm.vo.ProductLineDetailVO;
import com.coocaa.cheese.crm.vo.SubjectButtonVO;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 签约主体管理服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AdvertisingSubjectService {
    private final TranslatorFactory translatorFactory;
    private final InnerApproveConfig innerApproveConfig;
    private final CrmKafkaProducer crmKafkaProducer;
    private final IAdvertisingSubjectService advertisingSubjectService;
    private final IBusinessService businessService;
    private final IBrandService ibrandService;
    private final BrandService brandService;
    private final InnerApproveService innerApproveService;
    private final IInnerApproveService nativeApproveService;
    private final IProductLineSignSubjectService productLineSignSubjectService;
    private final IProductLineService productLineService;
    private final ProductLineService productLineServiceManager;
    private final FeignAuthorityRpc feignAuthorityRpc;

    /**
     * 签约主体列表(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的签约主体列表
     */
    @AutoTranslate
    public PageResponseVO<AdvertisingSubjectVO> pageList(PageRequestVO<AdvertisingSubjectQueryParam> pageRequest) {

        AdvertisingSubjectQueryDTO queryDto = AdvertisingSubjectConvert.INSTANCE.toDto(pageRequest.getQuery());
        IPage<AdvertisingSubjectEntity> pageSubjects = advertisingSubjectService.pageList(getPage(pageRequest), queryDto);
        return AdvertisingSubjectConvert.INSTANCE.toPageResponse(pageSubjects);
    }

    /**
     * 签约主体列表(不分页)
     *
     * @param brandId 品牌ID
     * @param name    公司或品牌名称
     * @return 签约主体列表
     */
    public List<CodeNameVO> listSubjects(Integer brandId, String name) {
        Page<AdvertisingSubjectEntity> page = new Page<>(1, -1);
        AdvertisingSubjectQueryDTO queryDto = new AdvertisingSubjectQueryDTO()
                .setBrandId(brandId)
                .setCompanyName(StringUtils.trimToNull(name))
                .setEffectiveStatus(BooleFlagEnum.YES.getCode());
        IPage<AdvertisingSubjectEntity> pageSubjects = advertisingSubjectService.pageList(page, queryDto);

        // 没查到数据，直接返回
        if (CollectionUtils.isEmpty(pageSubjects.getRecords())) {
            return Collections.emptyList();
        }

        return pageSubjects.getRecords().stream()
                .map(subject -> CodeNameVO.builder().id(subject.getId()).name(subject.getCompanyName()).build())
                .toList();
    }

    /**
     * 签约主体详情
     *
     * @param id 签约主体ID
     * @return 签约主体详情
     */
    @AutoTranslate
    public AdvertisingSubjectVO getDetail(Integer id) {
        return Optional.ofNullable(advertisingSubjectService.getById(id)).map(AdvertisingSubjectConvert.INSTANCE::toVo)
                .orElse(new AdvertisingSubjectVO())
                .setProductLineList(List.of(productLineServiceManager.getProductLineBySubjectId(id)
                        .split(Constants.COMMA)));
    }

    /**
     * 签约主体新增或修改
     *
     * @param id    签约主体ID (为空表示新增)
     * @param param 签约主体信息
     * @return true: 析增或修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createOrUpdate(Integer id, AdvertisingSubjectParam param) {
        // 检查是否已存在
        checkAdvertisingSubject(id, param);

        // 转换实体
        AdvertisingSubjectEntity entity = AdvertisingSubjectConvert.INSTANCE.toEntity(param);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());

        // 新增或修改
        boolean result;
        String action;
        if (Objects.isNull(id)) {
            action = "add";
            entity.setProtectionPeriodFlag(param.getProtectionPeriodFlag());
            entity.setEffectiveTime(LocalDateTime.now());
            result = advertisingSubjectService.save(entity);
        } else {
            entity.setId(id);
            action = "update";

            // 更新主体的时候还要校验关联的品牌是否生效，如果不生效，则不允许更新签约主体
            BrandEntity brand = ibrandService.getById(param.getBrandId());
            if (BooleFlagEnum.NO.getCode().equals(brand.getEffectiveStatus())) {
                throw new BusinessException("保存失败：关联品牌未生效");
            }

            // 先查出来旧的记录，判断是否需要更新生效时间
            // 只有首次否变成是才更新生效时间
            AdvertisingSubjectEntity oldEntity = advertisingSubjectService.getById(id);
            if (BooleFlagEnum.NO.getCode().equals(oldEntity.getEffectiveStatus())
                    && BooleFlagEnum.YES.getCode().equals(entity.getEffectiveStatus())) {
                entity.setProtectionPeriodFlag(BooleFlagEnum.YES.getCode());
                if (Objects.isNull(entity.getEffectiveTime())) {
                    entity.setEffectiveTime(LocalDateTime.now());
                }
            }
            result = advertisingSubjectService.updateById(entity);
        }

        // 发送变更消息
        if (result && Objects.nonNull(entity.getId())) {
            sendChangeMessage(action, entity);
        }

        return result;
    }

    /**
     * 校验签约主体参数
     *
     * @param id    签约主体ID
     * @param param 签约主体参数
     */
    private void checkAdvertisingSubject(Integer id, AdvertisingSubjectParam param) {
        if (isExist(id, param)) {
            throw new BusinessException("已存在该品牌与签约主体的登记信息，请勿重复提交。");
        }
        BrandEntity brand = ibrandService.getById(param.getBrandId());
        // 签约主体生效，品牌未生效
        if (BooleFlagEnum.YES.getCode().equals(param.getEffectiveStatus())
                && BooleFlagEnum.NO.getCode().equals(brand.getEffectiveStatus())) {
            throw new BusinessException("保存失败：关联品牌未生效");
        }
    }

    /**
     * 签约主体删除
     *
     * @param id 签约主体ID
     * @return true: 删除成功
     */
    public boolean delete(Integer id) {
        // 检查签约主体是否存在
        AdvertisingSubjectEntity entity = Optional.ofNullable(advertisingSubjectService.getById(id))
                .orElseThrow(() -> new BusinessException("签约主体不存在，删除失败"));

        // 已关联商机
        if (businessService.lambdaQuery()
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getAdvertisingSubjectId, id).exists()) {
            throw new BusinessException("该签约主体已关联商机，禁止删除");
        }

        // 查询审批记录
        String approveInstanceCode = nativeApproveService.queryInstanceCodeById(id.longValue(), StationTargetEnum.CUSTOMER_REPORT.getCode());
        if (StringUtils.isNotBlank(approveInstanceCode)) {
            Set<String> notAllowedStatus = Set.of(InnerApproveStatusEnum.PENDING_APPROVE.getCode(), InnerApproveStatusEnum.APPROVING.getCode());
            InnerApproveInstanceVO approveInstance = innerApproveService.queryDetail(approveInstanceCode);
            if (Objects.nonNull(approveInstance) && notAllowedStatus.contains(approveInstance.getApprovalStatus())) {
                throw new BusinessException("该签约主体正在报备审批中，禁止删除");
            }
        }
        // 获取当前签约主体关联的所有 ProductLineSignSubjectEntity
        List<ProductLineSignSubjectEntity> relations = productLineSignSubjectService.lambdaQuery()
                .eq(ProductLineSignSubjectEntity::getSignSubjectId, id)
                .list();

        if (CollectionUtils.isNotEmpty(relations)) {
            List<Long> productLineIds = relations.stream()
                    .map(ProductLineSignSubjectEntity::getProductLineId)
                    .distinct()
                    .toList();

            Map<Long, Boolean> hasOtherSubjectsMap = new HashMap<>();
            for (Long productLineId : productLineIds) {
                boolean hasOtherSubjects = productLineSignSubjectService.lambdaQuery()
                        .eq(ProductLineSignSubjectEntity::getProductLineId, productLineId)
                        .ne(ProductLineSignSubjectEntity::getSignSubjectId, id)
                        .eq(ProductLineSignSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                        .exists();
                hasOtherSubjectsMap.put(productLineId, hasOtherSubjects);
            }

            List<Long> deletableProductLineIds = productLineIds.stream()
                    .filter(plId -> !hasOtherSubjectsMap.getOrDefault(plId, false))
                    .toList();

            if (CollectionUtils.isNotEmpty(deletableProductLineIds)) {
                // 逻辑删除产品线
                productLineService.removeByIds(deletableProductLineIds);
            }
        }

        // 使用 remove 方法逻辑删除 ProductLineSignSubjectEntity 关联关系
        productLineSignSubjectService.lambdaUpdate()
                .eq(ProductLineSignSubjectEntity::getSignSubjectId, id)
                .remove();

        boolean result = advertisingSubjectService.lambdaUpdate()
                .set(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(AdvertisingSubjectEntity::getId, id)
                .update();

        // 发送变更消息
        if (result && Objects.nonNull(entity.getId())) {
            sendChangeMessage("remove", entity);
        }

        return result;
    }

    /**
     * 检查名称是否重复
     *
     * @param id    签约主体ID, 修改时排除自己
     * @param param 投放参数
     * @return true: 重复
     */
    private boolean isExist(Integer id, AdvertisingSubjectParam param) {
        if (Objects.isNull(param.getBrandId()) && Objects.isNull(param.getCompanyId())) {
            return false;
        }

        LambdaQueryWrapper<AdvertisingSubjectEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());
        queryWrapper.ne(Objects.nonNull(id), AdvertisingSubjectEntity::getId, id);
        queryWrapper.eq(Objects.nonNull(param.getBrandId()), AdvertisingSubjectEntity::getBrandId, param.getBrandId());
        queryWrapper.eq(Objects.nonNull(param.getCompanyId()), AdvertisingSubjectEntity::getCompanyId, param.getCompanyId());
        return advertisingSubjectService.exists(queryWrapper);
    }

    /**
     * 获取分页对象
     */
    private Page<AdvertisingSubjectEntity> getPage(PageRequestVO<?> pageRequest) {
        // 分页查询列表，自定义统计SQL
        Page<AdvertisingSubjectEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));
        page.setCountId("pageList_COUNT");
        return page;
    }

    /**
     * 签约主体变更，发送MQ消息
     * 已知: 售卖系统建方案需要
     */
    public void sendChangeMessage(String action, AdvertisingSubjectEntity entity) {
        if (StringUtils.isBlank(action) || Objects.isNull(entity) || Objects.isNull(entity.getId())) {
            return;
        }

        // 优先从传来参数查询操作人，如果没有则需要查数据库
        Integer operator = entity.getOperator();
        if (Objects.isNull(operator)) {
            operator = Optional.ofNullable(advertisingSubjectService.getById(entity.getId()))
                    .map(AdvertisingSubjectEntity::getOperator).orElse(null);
        }

        // 变更消息
        ChangeMessage changeMessage = new ChangeMessage()
                .setAction(action)
                .setId(entity.getId())
                .setBrandId(entity.getBrandId())
                .setCompanyId(entity.getCompanyId())
                .setOperator(operator);

        // 翻译数据
        translatorFactory.translate(List.of(changeMessage));
        crmKafkaProducer.sendAdvertisingSubjectChange(changeMessage);
    }


    /**
     * 签约主体报备
     *
     * @param param 报备参数
     * @return Integer 主体的id
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer report(ReportH5Param param) {
        AdvertisingSubjectReportH5Param advertisingSubjectReportH5Param = param.getAdvertisingSubjectReportH5Param();
        ProductLineH5Param productLineH5Param = param.getProductLineH5Param();
        BrandReportH5Param brandReportH5Param = param.getBrandReportH5Param();
        Integer brandId = brandReportH5Param.getId();
        Integer subjectId = advertisingSubjectReportH5Param.getId();
        AdvertisingSubjectEntity entity;

        // 检查品牌有效性
        if (Objects.nonNull(brandId)) {
            BrandEntity brandEntity = Optional.ofNullable(ibrandService.getById(brandId))
                    .orElseThrow(() -> new BusinessException("品牌不存在，报备失败"));
            if (BooleFlagEnum.isNo(brandEntity.getEffectiveStatus())) {
                throw new BusinessException("该品牌在报备审批中，请勿重复报备。");
            }
        }

        // 检查产品线有效性
        checkProductLine(productLineH5Param, brandReportH5Param);

        // 检查主体有效性
        if (Objects.nonNull(subjectId)) {
            AdvertisingSubjectEntity subjectEntity = Optional.ofNullable(advertisingSubjectService.getById(subjectId))
                    .orElseThrow(() -> new BusinessException("签约主体不存在，报备失败"));
            if (BooleFlagEnum.isNo(subjectEntity.getEffectiveStatus())) {
                throw new BusinessException("该签约主体在报备审批中，请勿重复报备。");
            }
            throw new BusinessException("该签约主体已存在，请勿重复报备。");
        }

        // 创建品牌和主体
        if (Objects.isNull(brandId)) {
            BrandEntity brandEntity = brandService.brandH5Save(brandReportH5Param);
            entity = saveAdvertisingSubject(brandEntity.getId(), null, advertisingSubjectReportH5Param);
        } else {
            entity = saveAdvertisingSubject(brandId, brandReportH5Param.getCompanyId(), advertisingSubjectReportH5Param);
        }
        //创建产品线
        productLineServiceManager.reportSaveProductLine(productLineH5Param, entity);

        // 构建审批参数
        List<InnerApproveTemplateParam> params = buildInnerApproveTemplateParam(entity, Objects.isNull(brandId));

        // 调用审批接口
        boolean result = innerApproveService.initiateApproval(entity.getId().longValue(),
                StationTargetEnum.CUSTOMER_REPORT, innerApproveConfig.getReport(), params);
        if (!result) {
            throw new BusinessException("报备审批发起失败");
        }
        return entity.getId();
    }

    private void checkProductLine(ProductLineH5Param productLineH5Param, BrandReportH5Param brandReportH5Param) {

        //产品线名称不能与界面填写的品牌名称或品牌别名相同；不能在一个界面重复录入相同名称的产品线（不同的品牌允许有相同的产品线，故无需跨品牌验重）；
        Set<String> invalidNames = new HashSet<>();

        // 检查数组内是否有重复元素
        Set<String> uniqueNames = new HashSet<>();
        for (String name : productLineH5Param.getNameStr()) {
            if (!uniqueNames.add(name)) {
                invalidNames.add(name);
            }
        }

        // 检查是否与品牌名称或标签冲突
        String brandName = brandReportH5Param.getName();
        List<String> brandTags = brandReportH5Param.getTags();

        Set<String> conflictNames = productLineH5Param.getNameStr().stream()
                .filter(n -> n.equals(brandName) || (CollectionUtil.isNotEmpty(brandTags) && brandTags.contains(n)))
                .collect(Collectors.toSet());

        invalidNames.addAll(conflictNames);

        if (CollectionUtil.isNotEmpty(invalidNames)) {
            throw new BusinessException("存在重复的产品线: " + invalidNames);
        }
    }

    /**
     * 保存主体
     *
     * @param brandId 品牌id
     * @param param   主体报备H5参数
     */
    @Transactional(rollbackFor = Exception.class)
    public AdvertisingSubjectEntity saveAdvertisingSubject(Integer brandId, Integer companyId,
                                                           AdvertisingSubjectReportH5Param param) {

        //如果持有公司不为空就保存
        if (Objects.nonNull(companyId)) {
            ibrandService.lambdaUpdate().set(BrandEntity::getCompanyId, companyId).eq(BrandEntity::getId, brandId).update();
        }
        // 校验主体
        check(brandId, param.getCompanyId());
        AdvertisingSubjectEntity entity = AdvertisingSubjectConvert.INSTANCE.toEntity(param);
        entity.setDepartmentId(UserThreadLocal.getDeptId());
        entity.setBrandId(brandId);
        entity.setProtectionPeriodFlag(BooleFlagEnum.YES.getCode());
        advertisingSubjectService.save(entity);
        return entity;
    }

    /**
     * 检查主体是否存在
     *
     * @param brandId   品牌ID
     * @param companyId 公司ID
     */
    private void check(Integer brandId, Integer companyId) {
        AdvertisingSubjectEntity subjectEntity = advertisingSubjectService.lambdaQuery()
                .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(AdvertisingSubjectEntity::getBrandId, brandId)
                .eq(AdvertisingSubjectEntity::getCompanyId, companyId)
                .last("limit 1")
                .one();
        if (Objects.nonNull(subjectEntity)) {
            if (BooleFlagEnum.isNo(subjectEntity.getEffectiveStatus())) {
                throw new BusinessException("该签约主体在报备审批中，请勿重复报备。");
            }
            throw new BusinessException("该签约主体已存在，请勿重复报备。");
        }
    }

    /**
     * 构建审批参数
     *
     * @param advertisingSubject 签约主体
     */
    private List<InnerApproveTemplateParam> buildInnerApproveTemplateParam(AdvertisingSubjectEntity advertisingSubject, Boolean isNewBrand) {
        final String typeCode = ApproveFieldTypeEnum.NUMBER.getCode();
        return List.of(
                new InnerApproveTemplateParam("creator", typeCode, String.valueOf(advertisingSubject.getCreator())),
                new InnerApproveTemplateParam("topFlag", typeCode, String.valueOf(advertisingSubject.getTopFlag())),
                new InnerApproveTemplateParam("newBrand", typeCode, isNewBrand ? "1" : "0")
        );
    }


    /**
     * H5签约主体列表(分页)
     * <p>
     * 条件：
     * 1. 只展示生效状态为是的数据
     * 2. 按钮状态说明：
     * - 无关联商机时：
     * - 保护期内且非本人报备：显示"该客户处于商机创建保护中"
     * - 保护期内且本人报备：显示"您已完成报备，请尽快创建商机"
     * - 有关联商机时：
     * - 本人已有该主体商机：显示"您名下已有该主体对应的商机"
     * - 最新商机处于休眠保护：显示"该客户处于商机休眠保护中"
     * - 最新商机处于跟进保护：显示"该客户处于商机跟进保护中"
     * - 其他情况：显示打捞
     *
     * @param pageRequest 分页查询条件
     * @return 分页的签约主体列表
     */
    @AutoTranslate
    public PageResponseVO<AdvertisingSubjectH5VO> h5PageList(PageRequestVO<AdvertisingSubjectQueryH5Param> pageRequest) {

        // 构建查询条件
        AdvertisingSubjectQueryDTO queryDto = AdvertisingSubjectH5Convert.INSTANCE.toDto(pageRequest.getQuery());
        queryDto.setEffectiveStatus(BooleFlagEnum.YES.getCode());

        // 查询数据
        IPage<AdvertisingSubjectEntity> pageSubjects = advertisingSubjectService.h5PageList(getPage(pageRequest), queryDto);

        // 使用Convert转换
        PageResponseVO<AdvertisingSubjectH5VO> result = AdvertisingSubjectH5Convert.INSTANCE.toPageResponse(pageSubjects);

        // 如果没有数据，直接返回空结果
        if (CollectionUtils.isEmpty(result.getRows())) {
            return result;
        }

        // 预处理数据
        prepareSubjectData(result);
        return result;
    }

    /**
     * 预处理签约主体数据，包括设置产品线名称和按钮状态
     *
     * @param result 签约主体VO列表
     */
    private void prepareSubjectData(PageResponseVO<AdvertisingSubjectH5VO> result) {
        // 获取所有主体ID
        List<Integer> subjectIds = result.getRows().stream()
                .map(AdvertisingSubjectH5VO::getId)
                .toList();

        // 批量查询所有关联商机
        Map<Integer, List<BusinessEntity>> subjectBusinessMap = getSubjectBusinessMap(subjectIds);
        // 批量查询所有productLineNames
        Map<Integer, String> allProductLineNames = productLineServiceManager.getProductLineBySubjectId(subjectIds);
        // 设置产品线名称和按钮状态
        result.getRows().forEach(vo -> {
            // 产品线名称设置
            vo.setProductLineNames(allProductLineNames.get(vo.getId()));

            // 按钮状态设置
            setButtonStatus(vo, subjectBusinessMap);
        });
    }

    /**
     * 获取签约主体与商机的映射关系
     *
     * @param subjectIds 签约主体ID列表
     * @return 签约主体ID与商机列表的映射关系
     */
    private Map<Integer, List<BusinessEntity>> getSubjectBusinessMap(List<Integer> subjectIds) {
        List<BusinessEntity> allBusinesses = businessService.lambdaQuery()
                .select(
                        BusinessEntity::getId,
                        BusinessEntity::getAdvertisingSubjectId,
                        BusinessEntity::getCode,
                        BusinessEntity::getAssignTime,
                        BusinessEntity::getAdvertisingReleaseDate,
                        BusinessEntity::getProgress,
                        BusinessEntity::getStatus,
                        BusinessEntity::getAdvertisingReleaseFlag,
                        BusinessEntity::getOwnerId,
                        BusinessEntity::getUpdateTime
                )
                .in(BusinessEntity::getAdvertisingSubjectId, subjectIds)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(BusinessEntity::getAdvertisingReleaseDate)
                .list();

        return CollectionUtils.isEmpty(allBusinesses)
                ? Maps.newHashMap()
                : allBusinesses.stream()
                .collect(Collectors.groupingBy(BusinessEntity::getAdvertisingSubjectId));
    }


    /**
     * 根据业务确定按钮状态
     *
     * @param vo                 主体VO
     * @param subjectBusinessMap 签约主体与商机映射
     */
    private void setButtonStatus(AdvertisingSubjectH5VO vo,
                                 Map<Integer, List<BusinessEntity>> subjectBusinessMap) {
        List<BusinessEntity> relatedBusinesses = subjectBusinessMap.getOrDefault(vo.getId(), Collections.emptyList());
        SubjectButtonVO subjectButtonVO = new SubjectButtonVO();

        if (CollectionUtils.isEmpty(relatedBusinesses)) {
            handleNoRelatedBusinesses(vo, subjectButtonVO);
        } else {
            handleRelatedBusinesses(subjectButtonVO, relatedBusinesses);
        }

        vo.setSubjectButtonVO(subjectButtonVO);
    }

    /**
     * 处理无商机关联的情况
     *
     * @param vo              主体VO
     * @param subjectButtonVO 主体按钮VO
     */
    private void handleNoRelatedBusinesses(AdvertisingSubjectH5VO vo,
                                           SubjectButtonVO subjectButtonVO) {
        Integer currentUserId = UserThreadLocal.getUserId();
        if (BooleFlagEnum.isYes(vo.getProtectionPeriodFlag())) {
            if (Objects.equals(vo.getCreator(), currentUserId)) {
                subjectButtonVO.setType(SubjectOperationEnum.CREATE_OPPORTUNITY.getCode());
                subjectButtonVO.setMessage("您已完成报备，请尽快创建商机");
            } else {
                subjectButtonVO.setType(SubjectOperationEnum.CREATE_PROTECTION.getCode());
                subjectButtonVO.setMessage("该客户处于商机创建保护中");
            }
        } else {
            subjectButtonVO.setType(SubjectOperationEnum.SALVAGE.getCode());
        }
    }

    /**
     * 处理有商机关联的情况
     *
     * @param subjectButtonVO   主体按钮VO
     * @param relatedBusinesses 相关商机列表
     */
    private void handleRelatedBusinesses(SubjectButtonVO subjectButtonVO, List<BusinessEntity> relatedBusinesses) {
        Integer currentUserId = UserThreadLocal.getUserId();
        // 过滤出当前用户的商机
        List<BusinessEntity> ownerBusinesses = relatedBusinesses.stream()
                .filter(item -> item.getOwnerId().equals(currentUserId)).toList();
        // 如果有当前用户的业务
        if (CollectionUtils.isNotEmpty(ownerBusinesses)) {
            List<BusinessVO> businesses = BusinessConvert.INSTANCE.toVOList(ownerBusinesses);
            subjectButtonVO.setBusinessVOs(businesses);
            subjectButtonVO.setType(SubjectOperationEnum.VIEW_OPPORTUNITY.getCode());
            subjectButtonVO.setMessage("您名下已有该主体对应的商机");
        } else {
            determineButtonStatusByBusiness(subjectButtonVO, relatedBusinesses);
        }
    }

    /**
     * 根据商机确定按钮状态
     *
     * @param subjectButtonVO   主体按钮VO
     * @param relatedBusinesses 商机列表
     */
    private void determineButtonStatusByBusiness(SubjectButtonVO subjectButtonVO, List<BusinessEntity> relatedBusinesses) {
        // 过滤出休眠中的商机
        BusinessEntity dormantBusiness = relatedBusinesses.stream().filter(b ->
                BusinessStatusEnum.DORMANT.getCode().equals(b.getStatus())).findFirst().orElse(null);
        // 过滤出【释放保护】=保护中 &【状态】≠休眠的商机
        BusinessEntity business = relatedBusinesses.stream().filter(b ->
                BooleFlagEnum.NO.getCode().equals(b.getAdvertisingReleaseFlag())
                        && !BusinessStatusEnum.DORMANT.getCode().equals(b.getStatus())).findFirst().orElse(null);
        if (dormantBusiness != null) {
            subjectButtonVO.setBusinessId(dormantBusiness.getId());
            subjectButtonVO.setType(SubjectOperationEnum.PK_CHALLENGE.getCode());
            subjectButtonVO.setMessage("该客户处于商机休眠保护中");
        } else if (business != null) {
            subjectButtonVO.setBusinessId(business.getId());
            subjectButtonVO.setType(SubjectOperationEnum.PK_CHALLENGE.getCode());
            subjectButtonVO.setMessage("该客户处于商机跟进保护中");
        } else {
            subjectButtonVO.setType(SubjectOperationEnum.SALVAGE.getCode());
        }
    }

    /**
     * 根据签约主体ID查询关联的产品线信息
     */
    @AutoTranslate
    public ProductLineDetailVO getProductLinesBySubjectId(Integer subjectId) {
        // 获取签约主体实体
        AdvertisingSubjectEntity subject = advertisingSubjectService.getById(subjectId);
        if (subject == null) {
            throw new BusinessException("签约主体不存在");
        }
        //设置公司名称和品牌名称
        return new ProductLineDetailVO()
                .setCompanyId(subject.getCompanyId())
                .setBrandId(subject.getBrandId())
                .setProductLineNames(productLineServiceManager.getProductLineBySubjectId(subjectId));
    }


    @Data
    @Accessors(chain = true)
    static class ChangeMessage {
        /**
         * action: add/update/remove,
         */
        private String action;
        private Integer id;

        /**
         * 品牌ID
         */
        @TransField(type = TransTypes.BRAND)
        private Integer brandId;
        private String brandName;

        /**
         * 公司ID
         */
        @TransField(type = TransTypes.COMPANY)
        private Integer companyId;
        private String companyName;

        /**
         * Top标记
         */
        private Integer topFlag;

        /**
         * 操作人
         */
        private Integer operator;
    }

    /**
     * 签约主体公海列表(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的签约主体公海列表
     */
    @AutoTranslate
    public PageResponseVO<AdvertisingSubjectPublicSeaVO> publicSeaPageList(PageRequestVO<AdvertisingSubjectPublicSeaQueryParam> pageRequest) {
        Integer currentUserId = UserThreadLocal.getUserId();
        AdvertisingSubjectQueryDTO queryDto = AdvertisingSubjectPublicSeaConvert.INSTANCE.toDto(pageRequest.getQuery());

        // 设置分页对象，使用自定义统计SQL
        Page<AdvertisingSubjectEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));

        // 先查询所有符合基础条件的签约主体
        IPage<AdvertisingSubjectEntity> pageSubjects = advertisingSubjectService.publicSeaPageList(page, queryDto);

        // 获取需要排除的签约主体ID列表
        Set<Integer> excludeSubjectIds = getExcludeSubjectIds(currentUserId);

        // 过滤掉需要排除的签约主体
        List<AdvertisingSubjectEntity> filteredRecords = pageSubjects.getRecords().stream()
                .filter(subject -> !excludeSubjectIds.contains(subject.getId()))
                .toList();

        // 重新设置分页结果
        pageSubjects.setRecords(filteredRecords);
        pageSubjects.setTotal((long) filteredRecords.size());

        PageResponseVO<AdvertisingSubjectPublicSeaVO> result = AdvertisingSubjectPublicSeaConvert.INSTANCE.toPageResponse(pageSubjects);

        // 没查到数据，直接返回
        if (CollectionUtils.isEmpty(result.getRows())) {
            return result;
        }

        // 收集所有签约主体ID
        List<Integer> subjectIds = result.getRows().stream()
                .map(AdvertisingSubjectPublicSeaVO::getId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 批量获取签约主体所绑定的产品线名称
        Map<Integer, String> productLineNames = productLineServiceManager.getProductLineBySubjectId(subjectIds);
        result.getRows().forEach(vo -> vo.setProductLineNames(productLineNames.get(vo.getId())));

        // 设置是否可打捞标识
        setCanSalvageFlag(result.getRows(), currentUserId);

        return result;
    }

    /**
     * 获取需要排除的签约主体ID列表
     * 包含两个逻辑：
     * 1. 主体在商机表中存在保护期的数据
     * 2. 主体在商机表中存在当前用户的数据
     *
     * @param currentUserId 当前用户ID
     * @return 需要排除的签约主体ID集合
     */
    private Set<Integer> getExcludeSubjectIds(Integer currentUserId) {
        Set<Integer> excludeIds = new HashSet<>();

        // 逻辑1：查询主体在商机表中存在保护期的数据
        // 保护期条件：delete_flag = 0 AND advertising_release_flag = 0 (保护中)
        List<Integer> protectedSubjectIds = businessService.lambdaQuery()
                .select(BusinessEntity::getAdvertisingSubjectId)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.NO.getCode())
                .isNotNull(BusinessEntity::getAdvertisingSubjectId)
                .list()
                .stream()
                .map(BusinessEntity::getAdvertisingSubjectId)
                .distinct()
                .toList();

        excludeIds.addAll(protectedSubjectIds);

        // 逻辑2：查询主体在商机表中存在当前用户的数据
        List<Integer> currentUserSubjectIds = businessService.lambdaQuery()
                .select(BusinessEntity::getAdvertisingSubjectId)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getOwnerId, currentUserId)
                .isNotNull(BusinessEntity::getAdvertisingSubjectId)
                .list()
                .stream()
                .map(BusinessEntity::getAdvertisingSubjectId)
                .distinct()
                .toList();

        excludeIds.addAll(currentUserSubjectIds);

        log.info("公海查询排除签约主体ID，当前用户：{}，保护期主体数：{}，当前用户主体数：{}，合并去重后总数：{}",
                currentUserId, protectedSubjectIds.size(), currentUserSubjectIds.size(), excludeIds.size());

        return excludeIds;
    }

    /**
     * 设置是否可打捞标识
     *
     * @param records 签约主体公海列表
     * @param currentUserId 当前用户ID
     */
    private void setCanSalvageFlag(List<AdvertisingSubjectPublicSeaVO> records, Integer currentUserId) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        List<Integer> subjectIds = records.stream().map(AdvertisingSubjectPublicSeaVO::getId).toList();

        // 查询这些签约主体关联的商机
        List<BusinessEntity> allBusinesses = businessService.lambdaQuery()
                .select(
                        BusinessEntity::getAdvertisingSubjectId,
                        BusinessEntity::getOwnerId,
                        BusinessEntity::getAdvertisingReleaseFlag
                )
                .in(BusinessEntity::getAdvertisingSubjectId, subjectIds)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();

        Map<Integer, List<BusinessEntity>> subjectBusinessMap = CollectionUtils.isEmpty(allBusinesses)
                ? Maps.newHashMap()
                : allBusinesses.stream().collect(Collectors.groupingBy(BusinessEntity::getAdvertisingSubjectId));

        // 设置每个签约主体的可打捞标识
        for (AdvertisingSubjectPublicSeaVO vo : records) {
            List<BusinessEntity> relatedBusinesses = subjectBusinessMap.getOrDefault(vo.getId(), Collections.emptyList());

            // 判断是否可打捞：
            // 1. 没有关联商机，或者
            // 2. 所有关联商机的归属人都不是当前用户，且都已释放到公海
            boolean canSalvage = relatedBusinesses.isEmpty() ||
                    relatedBusinesses.stream().allMatch(business ->
                            !business.getOwnerId().equals(currentUserId) &&
                            BooleFlagEnum.YES.getCode().equals(business.getAdvertisingReleaseFlag()));

            vo.setCanSalvage(canSalvage);
        }
    }
}
