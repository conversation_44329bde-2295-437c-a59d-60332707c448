
package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品线表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Schema(name = "ProductLineParam", description = "产品线表参数")
public class ProductLineParam {

    @NotNull(message = "主键ID不能为空")
    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "名称不能为空")
    @Schema(description = "名称", maxLength = 50)
    private String name;

    @NotNull(message = "品牌ID不能为空")
    @Schema(description = "品牌ID")
    private Integer brandId;

    @NotNull(message = "删除标记  [0:否, 1:是]不能为空")
    @Schema(description = "删除标记  [0:否, 1:是]", maxLength = 1)
    private Integer deleteFlag;

    @NotNull(message = "创建人不能为空")
    @Schema(description = "创建人")
    private Integer creator;

    @NotNull(message = "创建时间不能为空")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @NotNull(message = "操作人不能为空")
    @Schema(description = "操作人")
    private Integer operator;

    @NotNull(message = "更新时间不能为空")
    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;
}