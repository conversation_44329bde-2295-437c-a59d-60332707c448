package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.bean.BankQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.BankEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 银行帐户 服务类
 *
 * <AUTHOR>
 * @since 2025-02-25
 */
public interface IBankService extends IService<BankEntity> {
    /**
     * 按条件查询银行列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 银行列表
     */
    IPage<BankEntity> pageList(@Param("page") IPage<BankEntity> page, @Param("condition") BankQueryDTO condition);
}
