package com.coocaa.cheese.crm.util;

import cn.hutool.core.util.RandomUtil;
import com.coocaa.cheese.crm.common.tools.util.CacheKeyUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 各种编码生成
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public final class CodeGenerator {
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 生成商机编码
     * 商机ID规则: 6位（数字+字母随机数）
     *
     * @return 商机编码
     */
    public String generateBusinessCode() {
        return RandomUtil.randomString(6).toUpperCase();
    }

    /**
     * 生成通用编码
     *
     * @return 编码
     */
    public String generateInstitutionAccountCode() {
        // 生成10位随机数字+字母
        return RandomUtil.randomString(10).toUpperCase();
    }

    /**
     * 生成折扣策略编码
     * 规则: DS + 年月日 + 5位数字(每天从00001开始)
     * =>DS25030112345
     */
    public String generateDiscountRuleCode() {
        String today = DATE_FORMATTER.format(LocalDate.now());
        String cacheKey = CacheKeyUtils.getCacheKeyKey("discount", "code", today);
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("DS%s%05d", DATE_FORMATTER.format(LocalDate.now()), index);
    }
}
