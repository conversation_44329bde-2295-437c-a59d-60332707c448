package com.coocaa.cheese.crm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
@Accessors(chain = true)
@Schema(name = "InnerApprovePendingCountVO", description = "站内审批审批列表待审任务数量返回参数")
public class InnerApprovePendingCountVO {

    @Schema(description = "审批规则编号")
    private Integer code;

    @Schema(description = "代办数量")
    private Integer count;
}
