INSERT INTO sale_comm_config
    (name, parent_code, code, value, ext1, ext2, ext3, description, status)
VALUES ('商机首页查询-时间', 'business', 'business_index', '3', '', '', '', '', 1);
INSERT INTO sale_comm_config
    (name, parent_code, code, value, ext1, ext2, ext3, description, status)
VALUES ('商机状态变更-时间', 'business', 'business_protection', '7', '0', '0', '0', '', 1);

CREATE TABLE `sale_crm_business_progress`
(
    `id`              INT(11) UNSIGNED    NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `business_id`     INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '商机ID',
    `progress_status` TINYINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '进度状态（0:有需求, 1:无需求）',
    `detail_desc`     VARCHAR(50)         NOT NULL DEFAULT '' COMMENT '细节描述',
    `budget_amount`   INT(10) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '预算金额',
    `progress_type`   VARCHAR(20)         NOT NULL DEFAULT '' COMMENT '进度类型(INTENTION:意向,BUDGET:预算,REQUIREMENT:需求)',
    `create_time`     DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator`         INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '创建人',
    `operator`        INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '操作人',
    `delete_flag`     TINYINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除标记 [0:否, 1:是]',
    PRIMARY KEY (`id`),
    KEY `idx_business_id` (`business_id`)
) ENGINE = INNODB COMMENT = '商机进度表';