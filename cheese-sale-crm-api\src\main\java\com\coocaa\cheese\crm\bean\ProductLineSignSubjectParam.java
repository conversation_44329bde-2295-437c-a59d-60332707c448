
package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品线签约主体关系表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Schema(name = "ProductLineSignSubjectParam", description = "产品线签约主体关系表参数")
public class ProductLineSignSubjectParam {

    @NotNull(message = "主键ID不能为空")
    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "产品线ID不能为空")
    @Schema(description = "产品线ID")
    private Long productLineId;

    @NotNull(message = "签约主体ID不能为空")
    @Schema(description = "签约主体ID")
    private Integer signSubjectId;

    @Schema(description = "统一社会信用代码(加密存储)", maxLength = 100)
    private String code;

    @NotNull(message = "删除标记  [0:否, 1:是]不能为空")
    @Schema(description = "删除标记  [0:否, 1:是]", maxLength = 1)
    private Integer deleteFlag;

    @NotNull(message = "创建人不能为空")
    @Schema(description = "创建人")
    private Integer creator;

    @NotNull(message = "创建时间不能为空")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;
}