
package com.coocaa.cheese.crm.vo;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 产品线签约主体关系表VO
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "ProductLineSignSubjectVO", description = "产品线签约主体关系表VO")
public class ProductLineSignSubjectVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "产品线ID")
    private Long productLineId;

    @Schema(description = "签约主体ID")
    private Integer signSubjectId;

    @Schema(description = "统一社会信用代码(加密存储)", maxLength = 100)
    private String code;

    @Schema(description = "删除标记  [0:否, 1:是]", maxLength = 1)
    private Integer deleteFlag;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;
}