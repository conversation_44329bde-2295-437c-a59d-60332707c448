package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 签约主体商机信息
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
public class BusinessAdvertisingSubjectVO {

    @Schema(description = "主键ID")
    private Integer id;

    @TransField(type = TransTypes.BRAND)
    @Schema(description = "品牌ID")
    private Integer brandId;

    @Schema(description = "品牌名称", type = "String", example = "创维")
    private String brandName;

    @Schema(description = "归属人ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER_WNO)
    private Integer ownerId;
    private String ownerName;

    @Schema(description = "商机释放状态 [0:保护中, 1:已释放]", type = "Integer", example = "0")
    private Integer advertisingReleaseFlag;

    @Schema(description = "商机编码", example = "BIZ001")
    private String code;

    @Schema(description = "商机状态(字典0074)", type = "String", example = "0074-1")
    @TransField(type = TransTypes.DICT)
    private String status;
    private String statusName;

    @Schema(description = "签约主体ID")
    private Integer advertisingSubjectId;

    @Schema(description = "行业编码", type = "String", example = "0074-1")
    @TransField(type = TransTypes.INDUSTRY)
    private String industryCode;
    private String industryName;

}