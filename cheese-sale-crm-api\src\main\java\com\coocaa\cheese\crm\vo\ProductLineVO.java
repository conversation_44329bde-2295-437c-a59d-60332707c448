
package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 产品线表VO
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "ProductLineVO", description = "产品线表VO")
public class ProductLineVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "名称", maxLength = 50)
    private String name;

    @Schema(description = "品牌Id")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "删除标记  [0:否, 1:是]", maxLength = 1)
    private Integer deleteFlag;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "操作人")
    private Integer operator;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;
}