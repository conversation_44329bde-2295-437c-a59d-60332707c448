package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 商机信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessIndexVO", description = "商机信息VO")
public class BusinessIndexVO {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "品牌ID")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;

    @Schema(description = "品牌名称", type = "String", example = "创维")
    private String brandName;

    @Schema(description = "签约主体ID")
    private Integer advertisingSubjectId;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "主体释放日期")
    private LocalDate advertisingReleaseDate;

    @Schema(description = "归属人ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer ownerId;
    private String ownerName;

    @Schema(description = "管理部门ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "是否TOP客户 [0:否, 1:是]", type = "Integer", example = "1")
    private Integer topFlag;

    @Schema(description = "商机进度(字典0073)", type = "String", example = "0073-1")
    @TransField(type = TransTypes.DICT)
    private String progress;
    private String progressName;

    @Schema(description = "签约主体公司ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;

    @Schema(description = "签约主体名称-关联查询的公司名称", type = "String", example = "创维集团")
    private String companyName;
} 