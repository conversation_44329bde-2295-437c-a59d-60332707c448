<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.AdvertisingSubjectMapper">
    <!-- 按条件查询签约主体列表 -->
    <select id="pageList" resultType="com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity">
        SELECT cas.id, cas.type, cas.brand_id, cas.company_id, scc.name as companyName,
        cas.top_flag, cas.evidence_url, cas.description, cas.update_time, cas.operator, cas.creator,
        cas.effective_status, cas.effective_time, cas.protection_period_flag, cas.create_time
        FROM sale_comm_advertising_subject cas
        <include refid="joinSql"/>
        <include refid="whereSql"/>
        <if test="(condition.brandName != null and condition.brandName != '') or (condition.companyName != null and condition.companyName != '')">
            GROUP BY cas.id
        </if>
        ORDER BY cas.update_time DESC
    </select>

    <!-- 按条件查询签约主体列表 -->
    <select id="h5PageList" resultType="com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity">
        SELECT cas.id, cas.type, cas.brand_id, cas.company_id, scc.name as companyName,
        cas.top_flag, cas.evidence_url, cas.description, cas.update_time, cas.operator, cas.creator,
        cas.effective_status, cas.effective_time, cas.protection_period_flag
        FROM sale_comm_advertising_subject cas
        <include refid="joinSql"/>
        <include refid="whereSql"/>
        <if test="(condition.brandName != null and condition.brandName != '') or (condition.companyName != null and condition.companyName != '')">
            GROUP BY cas.id
        </if>
        ORDER BY CONVERT(scc.name USING gbk) COLLATE gbk_chinese_ci, cas.update_time DESC
    </select>

    <!-- 根据条件统计品牌数量 -->
    <select id="pageList_COUNT" resultType="java.lang.Integer">
        SELECT count(distinct cas.id)
        FROM sale_comm_advertising_subject cas
        <include refid="joinSql"/>
        <include refid="whereSql"/>
    </select>

    <!-- 品牌列表查询的公共部分提取出来 -->
    <sql id="joinSql">
        LEFT JOIN sale_comm_company scc ON cas.company_id = scc.id
        <if test="(condition.brandName != null and condition.brandName != '') or condition.brandId != null">
            LEFT JOIN sale_comm_brand scb ON cas.brand_id = scb.id
        </if>
    </sql>

    <sql id="whereSql">
        <where>
            AND cas.delete_flag = 0
            <if test="condition.brandId != null">
                AND cas.brand_id = #{condition.brandId}
            </if>
            <if test="condition.createStartDate != null">
                AND cas.create_time &gt;= #{condition.createStartDate}
            </if>
            <if test="condition.createEndDate != null">
                AND cas.create_time &lt; #{condition.createEndDate}
            </if>
            <if test="condition.creator != null">
                AND cas.creator = #{condition.creator}
            </if>
            <if test="condition.departmentId != null and condition.departmentId != ''">
                AND cas.department_id = #{condition.departmentId}
            </if>
             <if test="condition.creatorList != null and condition.creatorList.size() > 0">
                AND cas.creator IN
                <foreach collection="condition.creatorList" item="item" open="(" close=")" separator=",">#{item}</foreach>
             </if>
            <if test="condition.protectionPeriodFlag != null">
                AND cas.protection_period_flag = #{condition.protectionPeriodFlag}
            </if>
            <if test="condition.effectiveStatus != null">
                AND cas.effective_status = #{condition.effectiveStatus}
            </if>
            <if test="condition.brandName != null and condition.brandName != ''">
                AND scb.name LIKE CONCAT('%',#{condition.brandName},'%')
            </if>

            <if test="condition.companyName != null and condition.companyName != ''">
                AND scc.name LIKE CONCAT('%',#{condition.companyName},'%')
            </if>

            <if test="condition.type != null and condition.type != ''">
                AND cas.type = #{condition.type}
            </if>

            <if test="condition.topFlag != null">
                AND cas.top_flag = #{condition.topFlag}
            </if>
        </where>
    </sql>

    <select id="queryInnerSubjectEntity" resultType="com.coocaa.cheese.crm.common.db.bean.SignSubjectApproveDTO">
        SELECT
            scas.id,
            scas.brand_id,
            scas.company_id,
            scb.effective_time
        FROM sale_comm_advertising_subject scas
        LEFT JOIN sale_comm_brand scb ON scas.brand_id = scb.id AND scb.delete_flag = 0
        WHERE scas.id IN
        <foreach collection="bizIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        GROUP BY scas.id
    </select>

    <select id="queryInnerApproveDetail" resultType="com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO">
        SELECT
          scas.id,
          scas.company_id,
          scas.type,
          scas.top_flag,
          scas.description,
          scb.id                     AS brandId,
          scb.name                   AS brandName,
          scb.industry_code,
          scb.effective_time,
          GROUP_CONCAT(scbt.name)    AS brandTag,
          scb.company_id             AS holdCompanyId
          FROM sale_comm_advertising_subject scas
          LEFT JOIN sale_comm_brand scb ON scas.brand_id = scb.id
          LEFT JOIN sale_comm_brand_tag scbt ON scb.id = scbt.brand_id AND scas.brand_id = scbt.brand_id
         WHERE scas.id = #{id}
         GROUP BY scas.id
    </select>
    <!-- 查询签约主体公海列表 -->
    <select id="publicSeaPageList" resultType="com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity">
        SELECT cas.id, cas.brand_id, cas.company_id, scc.name as companyName,
               scb.name as brandName
        FROM sale_comm_advertising_subject cas
        LEFT JOIN sale_comm_company scc ON cas.company_id = scc.id
        LEFT JOIN sale_comm_brand scb ON cas.brand_id = scb.id
        LEFT JOIN sale_comm_brand_tag scbt ON scb.id = scbt.brand_id
        <where>
            AND cas.delete_flag = 0
            AND cas.effective_status = 1
            AND cas.protection_period_flag = 0
            AND NOT EXISTS (
                SELECT 1 FROM sale_crm_business scbb
                WHERE cas.id = scbb.advertising_subject_id
                AND scbb.delete_flag = 0
                AND scbb.owner_id = #{currentUserId}
            )
            <if test="condition.keyword != null and condition.keyword != ''">
                AND (
                    scb.name LIKE CONCAT('%', #{condition.keyword}, '%')
                    OR scbt.name LIKE CONCAT('%', #{condition.keyword}, '%')
                    OR scc.name LIKE CONCAT('%', #{condition.keyword}, '%')
                )
            </if>
        </where>
        ORDER BY cas.create_time DESC
    </select>

    <select id="innerSubjectEntity" resultType="com.coocaa.cheese.crm.common.db.bean.SignSubjectApproveDTO">
        SELECT
        scas.id,
        scas.brand_id,
        scas.company_id,
        scb.effective_time
        FROM sale_comm_advertising_subject scas
        LEFT JOIN sale_comm_brand scb ON scas.brand_id = scb.id AND scb.delete_flag = 0
        WHERE scas.id IN
        <foreach collection="bizIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <select id="customerReportPageList"
            resultType="com.coocaa.cheese.crm.common.db.bean.DashboardCustomerReportSearchDTO">
        SELECT
        scc.NAME companyName,
        scb.NAME brandName,
        scas.id,
        scas.brand_id,
        scas.company_id,
        scas.creator,
        scas.create_time,
        scas.creator_name,
        scas.department_id,
        scas.delete_flag,
        scas.effective_status
        FROM
        sale_comm_advertising_subject scas
        LEFT JOIN sale_comm_company scc ON scas.company_id = scc.id
        LEFT JOIN sale_comm_brand scb ON scas.brand_id = scb.id
        <if test="condition != null and condition != ''">
           where scc.NAME LIKE CONCAT('%',#{condition},'%')
        or scb.NAME LIKE CONCAT('%',#{condition},'%')
        or scas.creator_name LIKE CONCAT('%',#{condition},'%')
        </if>
    </select>
</mapper>
