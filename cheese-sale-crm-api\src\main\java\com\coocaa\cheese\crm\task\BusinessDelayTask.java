package com.coocaa.cheese.crm.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.common.db.bean.BusinessDelayDTO;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.tools.config.api.ApiConfig;
import com.coocaa.cheese.crm.common.tools.enums.MessageChatEnum;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import com.coocaa.cheese.crm.rpc.bean.UserMessageParam;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/10
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessDelayTask {

    private static final int BUSINESS_DELAY_ZERO = 0;
    private static final int BUSINESS_DELAY_THREE = 3;

    private final IBusinessService businessService;
    private final ApiConfig apiConfig;
    private final FeignAuthorityRpc feignAuthorityRpc;

    /**
     * 商机延期消息通知定时任务
     */
    @XxlJob("businessDelayJob")
    public void businessDelay() {

        //每天早上8：30，检查《商机数据表》， 《商机数据表》【主体释放日期】减 当前日期  = 3 时发送消息通知
        log.info("商机延期消息通知定时任务,时间:{}", LocalDateTime.now());
        try {
            log.info("开始执行商机释放提醒任务...");
            // 获取当前日期和3天后的日期
            LocalDate today = LocalDate.now();
            LocalDate threeDaysLater = today.plusDays(BUSINESS_DELAY_THREE);
            // 查询商机
            List<BusinessDelayDTO> businessList = businessService.queryBusinessToBeReleased(threeDaysLater);
            log.info("{}查询到即将释放的商机:{}", today, JSONUtil.toJsonStr(businessList));
            if (CollectionUtil.isEmpty(businessList)) {
                return;
            }
            int noticeCount = BUSINESS_DELAY_ZERO;
            for (BusinessDelayDTO dto : businessList) {
                UserMessageParam userMessageParam = buildParam(dto);
                log.info("发送商机释放提醒任务入参:{}", JSONUtil.toJsonStr(userMessageParam));
                try {
                    ResultTemplate<?> resultTemplate = feignAuthorityRpc.sendUserMessage(userMessageParam);
                    log.info("发送商机释放提醒任务结果:{}", JSONUtil.toJsonStr(resultTemplate));
                } catch (Exception e) {
                    log.error("发送商机释放提醒任务失败: {}", e.getMessage());
                }
                // 发送通知
                noticeCount++;
            }
            log.info("商机释放提醒任务执行完成，共发送 {} 条通知", noticeCount);
        } catch (Exception e) {
            log.error("商机释放提醒任务执行失败: {}", e.getMessage());
        }
    }

    private UserMessageParam buildParam(BusinessDelayDTO dto) {

        // 您的商机（【品牌名称】/【签约主体名称】）保护即将在【主体释放日期】凌晨到期，请及时更新商机进度触发保护期延长，如有必要可在商机详情提交“延期申请”。
        String content = String.format("您的客户（%s/%s）保护即将在%s凌晨到期，请及时更新商机进度触发保护期延长，如有必要可在商机详情提交“延期申请”。",
                dto.getBrandName(), dto.getSignSubjectName(), dto.getSubjectReleaseDate());
        return UserMessageParam.builder()
                .appCode(MessageChatEnum.SALE_WORKBENCH.getCode())
                .title("客户保护到期提醒")
                .content(content)
                .receiveUserIds(new HashSet<>(Collections.singletonList(dto.getOwnerId())))
                .feiShuFlag(Boolean.TRUE)
                .templateId(apiConfig.templateId)
                .templateParams(new String[]{dto.getBrandName(), dto.getSignSubjectName(), dto.getSubjectReleaseDate().toString()})
                .build();
    }
}
