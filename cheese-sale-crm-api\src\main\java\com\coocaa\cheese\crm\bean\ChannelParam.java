package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
public class ChannelParam {
    @Schema(description = "父渠道ID", type = "Integer", example = "0")
    private Integer parentId;

    @NotBlank(message = "渠道名称不能为空")
    @Size(max = 10, message = "渠道名称不能超过{max}个字符")
    @Schema(description = "渠道名称", type = "String", example = "个人开发")
    private String name;

    @NotBlank(message = "渠道别名不能为空")
    @Size(max = 10, message = "渠道别名不能超过{max}个字符")
    @Schema(description = "渠道别名", type = "String", example = "门店")
    private String nickName;

    @Size(max = 20, message = "备注说明不能超过{max}个字符")
    @Schema(description = "备注说明", type = "String", example = "线下实体门店渠道")
    private String description;
}
