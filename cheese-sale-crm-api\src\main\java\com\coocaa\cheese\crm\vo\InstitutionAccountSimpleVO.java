package com.coocaa.cheese.crm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 简化版机构账户视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "简化版机构账户视图对象")
public class InstitutionAccountSimpleVO {
    @Schema(description = "机构账户ID")
    private Integer id;

    @Schema(description = "公司ID")
    private Integer companyId;

    @Schema(description = "公司名称")
    private String companyName;
} 