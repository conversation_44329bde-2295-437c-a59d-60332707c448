package com.coocaa.cheese.crm.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.DesensitizeSerializer;
import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 公司信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "CompanyVO", description = "公司信息VO")
public class CompanyVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "公司名称", type = "String", example = "创维集团")
    private String name;

    @JSONField(serializeUsing = DesensitizeSerializer.class)
    @Schema(description = "统一社会信用代码", type = "String", example = "加密传输")
    private String code;

    @Schema(description = "营业执照附件地址", type = "String", example = "https://www.baidu.com/img/bd_logo1.png")
    private String licenseUrl;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "操作人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer operator;

    @Schema(description = "操作人", type = "Integer", example = "1")
    private String operatorName;
}