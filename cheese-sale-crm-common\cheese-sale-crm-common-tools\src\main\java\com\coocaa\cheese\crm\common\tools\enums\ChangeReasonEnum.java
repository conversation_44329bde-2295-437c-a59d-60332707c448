package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售-变动原因枚举(字典0112)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-28
 */
@Getter
@AllArgsConstructor
public enum ChangeReasonEnum implements IEnumType<String> {
    LISTING_FEE("0112-1", "刊例费");

    private final String code;
    private final String desc;

    private static final Map<String, ChangeReasonEnum> BY_CODE_MAP =
            Arrays.stream(ChangeReasonEnum.values())
                    .collect(Collectors.toMap(ChangeReasonEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static ChangeReasonEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static ChangeReasonEnum parse(String code, ChangeReasonEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ChangeReasonEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 