package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.BusinessWebQueryParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.vo.BusinessWebVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商机信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BusinessWebConvert extends PageableConvert<BusinessEntity, BusinessWebVO> {
    BusinessWebConvert INSTANCE = Mappers.getMapper(BusinessWebConvert.class);

    /**
     * Entity 转 VO
     */
    BusinessWebVO toVo(BusinessEntity entity);

    /**
     * VO 转 Entity
     */
    BusinessEntity toEntity(BusinessWebVO vo);

    /**
     * Param 转 Entity
     */
    BusinessEntity toEntity(BusinessWebQueryParam param);
} 