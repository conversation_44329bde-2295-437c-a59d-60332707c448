package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/18
 */
@Data
public class ProductLineH5Param {

    @NotEmpty
    @Schema(description = "产品线名称", example = "[奔驰,宝马]")
    @Size(max = 10, message = "产品线名称不能超过{max}个字符")
    private List<String> nameStr;
}
