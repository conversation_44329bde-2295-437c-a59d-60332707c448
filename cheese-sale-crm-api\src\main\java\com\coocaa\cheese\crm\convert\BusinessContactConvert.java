package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.BusinessContactParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessContactEntity;
import com.coocaa.cheese.crm.vo.BusinessContactVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商机联系人信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BusinessContactConvert extends PageableConvert<BusinessContactEntity, BusinessContactVO> {
    BusinessContactConvert INSTANCE = Mappers.getMapper(BusinessContactConvert.class);

    /**
     * Entity 转 VO
     */
    BusinessContactVO toVo(BusinessContactEntity entity);

    /**
     * VO 转 Entity
     */
    BusinessContactEntity toEntity(BusinessContactVO vo);

    /**
     * Param 转 Entity
     */
    BusinessContactEntity toEntity(BusinessContactParam param);

    /**
     * Param 转 Entity
     */
    List<BusinessContactEntity> toList(List<BusinessContactParam> param);
} 