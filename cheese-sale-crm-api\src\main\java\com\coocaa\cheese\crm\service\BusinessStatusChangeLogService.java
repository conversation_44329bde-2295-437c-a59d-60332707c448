package com.coocaa.cheese.crm.service;

import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessStatusChangeLogEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessStatusChangeLogService;
import com.coocaa.cheese.crm.common.tools.enums.BusinessChangeStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 商机状态变更
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessStatusChangeLogService {
    private final IBusinessStatusChangeLogService businessStatusChangeLogService;

    public void saveBusinessStatusChange(BusinessChangeStatusEnum type, BusinessEntity businessEntity, String status, String content) {
        BusinessStatusChangeLogEntity changeLog = new BusinessStatusChangeLogEntity();
        changeLog.setType(type.getCode());
        changeLog.setBizId(businessEntity.getId());
        changeLog.setBizCode(businessEntity.getCode());
        changeLog.setStatus(status);
        CachedUser cachedUser = UserThreadLocal.getUser();
        if (cachedUser != null) {
            changeLog.setOperator(cachedUser.getId());
            changeLog.setOperatorWno(cachedUser.getWno());
            changeLog.setOperatorName(cachedUser.getName());
        }
        changeLog.setChangeTime(LocalDateTime.now());
        changeLog.setContent(content);
        changeLog.setDeleteFlag(BooleFlagEnum.NO.getCode());
        businessStatusChangeLogService.save(changeLog);
    }
}
