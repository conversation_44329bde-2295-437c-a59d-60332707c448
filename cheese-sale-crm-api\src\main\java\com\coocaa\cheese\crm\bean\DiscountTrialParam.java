package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 折扣试算参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "折扣试算参数")
public class DiscountTrialParam {

    @Schema(description = "城市ID", type = "String", example = "110100")
    @NotNull(message = "城市ID不能为空")
    private Integer cityId;

    @Schema(description = "累计消耗金额", type = "BigDecimal", example = "100000")
    @NotNull(message = "累计消耗金额不能为空")
    private BigDecimal cost;

    @Schema(description = "折扣规则参数", type = "DiscountRuleParam")
    @NotNull(message = "折扣规则参数不能为空")
    @Valid
    private DiscountRuleParam ruleParam;
} 