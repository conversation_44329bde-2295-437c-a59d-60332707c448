package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 渠道信息
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName("sale_comm_channel")
public class ChannelEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 父渠道ID
     */
    private Integer parentId;

    /**
     * 渠道名称: 2-50个字符
     */
    private String name;

    /**
     * 渠道别名
     */
    private String nickName;

    /**
     * 备注说明
     */
    private String description;

    /**
     * 保护标记 [0:否, 1:是]
     */
    private Integer lockFlag;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
