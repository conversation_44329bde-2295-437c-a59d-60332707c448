package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 刊例费报价表
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@TableName("sale_crm_listing_fee_quote")
public class ListingFeeQuoteEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 资金变动记录ID
     */
    private Integer fundChangeRecordId;

    /**
     * 投放城市id
     */
    private Integer cityId;

    /**
     * 投放城市名称
     */
    private String cityName;

    /**
     * 计费方式 (0087)
     */
    private String billingType;

    /**
     * 点位数量
     */
    private Integer pointCount;

    /**
     * 计费点位数量
     */
    private Integer billingPointCount;

    /**
     * 点位单价
     */
    private BigDecimal pointUnitPrice;

    /**
     * 计费周期
     */
    private Integer billingCycle;

    /**
     * 适用折扣
     */
    private BigDecimal applicableDiscount;

    /**
     * 折前总价
     */
    private BigDecimal preDiscountTotal;

    /**
     * 折后总价
     */
    private BigDecimal postDiscountTotal;

    /**
     * 实际发生
     */
    private BigDecimal actualAmount;

    /**
     * 占位费用
     */
    private BigDecimal occupyAmount;

    /**
     * 删除标记[0:未删除, 1:已删除]
     */
    private Integer deleteFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 