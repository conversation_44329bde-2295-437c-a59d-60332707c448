package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.BusinessParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.vo.BusinessAdvertisingSubjectVO;
import com.coocaa.cheese.crm.vo.BusinessHisVO;
import com.coocaa.cheese.crm.vo.BusinessVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商机信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BusinessConvert extends PageableConvert<BusinessEntity, BusinessVO> {
    BusinessConvert INSTANCE = Mappers.getMapper(BusinessConvert.class);

    /**
     * Entity 转 VO
     */
    BusinessVO toVo(BusinessEntity entity);

    /**
     * VO 转 Entity
     */
    BusinessEntity toEntity(BusinessVO vo);

    /**
     * Param 转 Entity
     */
    BusinessEntity toEntity(BusinessParam param);

    /**
     * EntityList 转 VOList
     */
    List<BusinessVO> toVOList(List<BusinessEntity> entity);

    /**
     * Entity 转 toBusinessAdvertisingSubjectVOList
     */
    List<BusinessAdvertisingSubjectVO> toAdvertisingSubjectVOList(List<BusinessEntity> entity);


    /**
     * EntityList 转 VOList
     */
    List<BusinessHisVO> toHisList(List<BusinessEntity> entity);

}