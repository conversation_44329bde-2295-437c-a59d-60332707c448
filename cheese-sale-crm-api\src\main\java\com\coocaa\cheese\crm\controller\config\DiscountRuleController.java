package com.coocaa.cheese.crm.controller.config;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.DiscountRuleParam;
import com.coocaa.cheese.crm.bean.DiscountRuleQueryParam;
import com.coocaa.cheese.crm.bean.DiscountTrialParam;
import com.coocaa.cheese.crm.config.annotation.OperateLog;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.DiscountRuleService;
import com.coocaa.cheese.crm.vo.DiscountRuleDetailVO;
import com.coocaa.cheese.crm.vo.DiscountRuleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 机构折扣规则管理
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@RestController
@RequestMapping("/discount-rules")
@Tag(name = "机构折扣规则管理", description = "机构折扣规则管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DiscountRuleController extends BaseController {
    private final DiscountRuleService discountRuleService;

    /**
     * 折扣规则列表(分页)
     */
    @Operation(summary = "折扣规则列表(分页)")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<DiscountRuleVO>> pageListDiscountRule(@RequestBody PageRequestVO<DiscountRuleQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(DiscountRuleQueryParam::new));
        return ResultTemplate.success(discountRuleService.pageListDiscountRule(pageRequest));
    }

    /**
     * 折扣规则详情
     */
    @Operation(summary = "折扣规则详情")
    @Parameter(name = "id", description = "折扣规则ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<DiscountRuleDetailVO> getDiscountRuleDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(discountRuleService.getDiscountRuleDetail(id));
    }

    @Operation(summary = "作废折扣策略")
    @Parameter(name = "id", description = "策略ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @PutMapping("/{id}/invalid")
    public ResultTemplate<Boolean> invalidDiscountRule(@PathVariable("id") Integer id) {
        return ResultTemplate.success(discountRuleService.invalidDiscountRule(id));
    }

    /**
     * 创建折扣规则
     */
    @Operation(summary = "创建折扣规则")
    @PostMapping
    @OperateLog(functionName = "创建折扣规则", entityCode = "0006-05")
    public ResultTemplate<Boolean> createDiscountRule(@RequestBody @Validated DiscountRuleParam param) {
        return ResultTemplate.success(discountRuleService.createDiscountRule(param));
    }

    /**
     * 折扣试算
     */
    @Operation(summary = "折扣试算")
    @PostMapping("/trial")
    public ResultTemplate<BigDecimal> trialDiscount(@RequestBody @Validated DiscountTrialParam param) {
        return ResultTemplate.success(discountRuleService.trialDiscount(
                param.getCityId(),
                param.getCost(),
                param.getRuleParam()));
    }
} 