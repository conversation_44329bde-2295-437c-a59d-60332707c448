package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 机构账户接口
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
public interface InstitutionAccountMapper extends BaseMapper<InstitutionAccountEntity> {

    /**
     * 按条件查询机构账户列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 机构账户列表
     */
    IPage<InstitutionAccountEntity> pageList(@Param("page") IPage<InstitutionAccountEntity> page,
                                             @Param("condition") Object condition);
} 