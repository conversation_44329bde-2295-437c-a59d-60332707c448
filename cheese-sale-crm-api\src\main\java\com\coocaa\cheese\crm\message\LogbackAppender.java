package com.coocaa.cheese.crm.message;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.ThrowableProxyUtil;
import ch.qos.logback.core.AppenderBase;
import cn.hutool.core.date.DateUtil;
import com.coocaa.cheese.crm.common.tools.config.api.ApiConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/14
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class LogbackAppender extends AppenderBase<ILoggingEvent> {

    private final ApiConfig apiConfig;
    private final FeishuNotificationService feishuNotificationService;

    @Override
    protected void append(ILoggingEvent event) {
        if (event.getLevel().isGreaterOrEqual(Level.ERROR)) {
            String title = "系统错误 - " + event.getLoggerName();
            String content = buildContent(event);

            feishuNotificationService.sendAsyncNotification(title, content);
        }
    }

    private String buildContent(ILoggingEvent event) {
        StringBuilder sb = new StringBuilder();
        String dateTimeStr = DateUtil.format(DateUtil.date(event.getTimeStamp()), "yyyy-MM-dd HH:mm:ss");
        sb.append("服务: ").append(apiConfig.appName).append("\n");
        sb.append("级别: ").append(event.getLevel()).append("\n");
        sb.append("时间: ").append(dateTimeStr).append("\n");
        sb.append("消息: ").append(event.getFormattedMessage()).append("\n");

        if (event.getThrowableProxy() != null) {
            sb.append("异常: ").append(event.getThrowableProxy().getClassName()).append("\n");
            // 可以添加堆栈跟踪的前几行
            String stackTrace = ThrowableProxyUtil.asString(event.getThrowableProxy());
            String[] lines = stackTrace.split("\n");
            sb.append("\n### 堆栈跟踪(前10行)\n");
            for (int i = 0; i < Math.min(10, lines.length); i++) {
                sb.append(lines[i]).append("\n");
            }
        }

        return sb.toString();
    }
}
