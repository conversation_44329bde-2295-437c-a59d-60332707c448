package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 客户报备卡片VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-10
 */
@Data
public class DashboardCustomerReportCardVO {

    @Schema(description = "管理部门ID", type = "String", example = "1")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "申报数量", type = "Integer", example = "10")
    private Integer applyCount;

    @Schema(description = "待审数量", type = "Integer", example = "5")
    private Integer pendingCount;

    @Schema(description = "审批通过数量", type = "Integer", example = "3")
    private Integer approvedCount;

    @Schema(description = "商机数量", type = "Integer", example = "2")
    private Integer businessCount;
}
