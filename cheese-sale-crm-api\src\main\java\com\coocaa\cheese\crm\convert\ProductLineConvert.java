package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.ProductLineParam;
import com.coocaa.cheese.crm.common.db.bean.InnerProductLineDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineUnionDTO;
import com.coocaa.cheese.crm.common.db.entity.ProductLineEntity;
import com.coocaa.cheese.crm.vo.ProductLineInitiateVO;
import com.coocaa.cheese.crm.vo.ProductLineUnionVO;
import com.coocaa.cheese.crm.vo.ProductLineVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 产品线表 信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-6-17
 */
@Mapper
public interface ProductLineConvert extends PageableConvert<ProductLineEntity, ProductLineVO> {
    ProductLineConvert INSTANCE = Mappers.getMapper(ProductLineConvert.class);

    /**
     * Entity 转 VO
     */
    ProductLineVO toVo(ProductLineEntity entity);

    /**
     * 转换查询参数
     */
    ProductLineDTO toDto(ProductLineParam param);

    /**
     * VO 转 Entity
     */
    ProductLineEntity toEntity(ProductLineVO vo);

    /**
     * Param 转 Entity
     */
    ProductLineEntity toEntity(ProductLineParam param);

    /**
     * dto 转 vo
     */
    ProductLineInitiateVO toVo(InnerProductLineDetailDTO dto);

    /**
     * dto 转 vo
     */
    ProductLineUnionVO toVo(ProductLineUnionDTO dto);

    /**
     * dto 转 vo
     */
    ProductLineVO toVo(ProductLineDTO dto);
}