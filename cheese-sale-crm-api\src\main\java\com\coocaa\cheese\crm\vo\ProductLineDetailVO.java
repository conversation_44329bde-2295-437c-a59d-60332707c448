
package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 产品线表VO
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "ProductLineVO", description = "产品线表VO")
public class ProductLineDetailVO {
    /**
     * 品牌ID
     */
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    /**
     * 签约主体或品牌持有人
     */
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;

    /**
     * 产品线名称
     */
    private String productLineNames;

}