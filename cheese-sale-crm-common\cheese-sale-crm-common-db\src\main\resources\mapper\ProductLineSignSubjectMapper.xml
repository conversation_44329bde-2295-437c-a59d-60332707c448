<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.ProductLineSignSubjectMapper">

    <select id="getProductLineNameList" resultType="java.lang.String">
        SELECT
            pl.NAME
        FROM
            sale_crm_product_line pl
                LEFT JOIN sale_crm_product_line_sign_subject plss ON pl.id = plss.product_line_id
        WHERE
            plss.delete_flag = 0
            AND plss.sign_subject_id = #{id}
    </select>
</mapper>