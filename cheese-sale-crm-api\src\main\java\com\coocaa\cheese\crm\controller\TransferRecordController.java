package com.coocaa.cheese.crm.controller;

import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.TransferParam;
import com.coocaa.cheese.crm.bean.TransferRecordQueryParam;
import com.coocaa.cheese.crm.handler.transfer.TransferHandler;
import com.coocaa.cheese.crm.handler.transfer.TransferHandlerFactory;
import com.coocaa.cheese.crm.service.TransferRecordService;
import com.coocaa.cheese.crm.vo.TransferRecordVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 转移记录管理
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@RestController
@RequestMapping("/transfer-records")
@Tag(name = "转移记录管理", description = "转移记录管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class TransferRecordController extends BaseController {
    private final TransferRecordService transferRecordService;

    /**
     * 分页查询转移记录
     */
    @Operation(summary = "分页查询转移记录")
    @PostMapping("/page-list")
    public ResultTemplate<PageResponseVO<TransferRecordVO>> pageListTransferRecord(@RequestBody PageRequestVO<TransferRecordQueryParam> pageRequest) {
        return ResultTemplate.success(transferRecordService.pageListTransferRecord(pageRequest));
    }

    /**
     * 获取业务转移记录列表
     */
    @Operation(summary = "获取业务转移记录列表")
    @Parameter(name = "bizType", description = "业务类型", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "string"))
    @Parameter(name = "bizId", description = "业务ID", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "int"))
    @GetMapping("/list")
    public ResultTemplate<List<TransferRecordVO>> listTransferRecord(
            @RequestParam("bizType") String bizType,
            @RequestParam("bizId") Integer bizId) {
        return ResultTemplate.success(transferRecordService.listTransferRecord(bizType, bizId));
    }

    /**
     * 转移操作
     */
    @Operation(summary = "转移操作")
    @PostMapping
    public ResultTemplate<Boolean> transfer(@RequestBody @Validated TransferParam param) {
        TransferHandler handler = TransferHandlerFactory.getHandler(param.getBizType());
        if (Objects.isNull(handler)) {
            throw new BusinessException("不支持的业务类型");
        }
        return ResultTemplate.success(handler.handleTransfer(param));
    }
}