package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 报备参数
 *
 * <AUTHOR>
 * @since 2025/4/29
 */
@Data
@Schema(description = "报备参数")
public class ReportH5Param {

    @Valid
    @NotNull(message = "签约主体创建参数不能为空")
    @Schema(description = "签约主体创建参数")
    private AdvertisingSubjectReportH5Param advertisingSubjectReportH5Param;

    @Valid
    @NotNull(message = "产品线创建参数不能为空")
    @Schema(description = "产品线创建参数")
    private ProductLineH5Param productLineH5Param;

    @Valid
    @NotNull(message = "品牌创建参数不能为空")
    @Schema(description = "品牌创建参数")
    private BrandReportH5Param brandReportH5Param;
}
