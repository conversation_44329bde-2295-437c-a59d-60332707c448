package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Schema(name = "SubjectExamineParam", description = "站内审批签约主体入参")
public class SubjectExamineParam {

    @NotBlank(message = "审批结果不能为空")
    @Schema(description = "审批结果(字典0147)")
    private String approveType;

    @Schema(description = "申请延期至的日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate applyDelayDate;

    @Schema(description = "审批意见")
    private String comment;

    @NotBlank(message = "审批类型不能为空")
    @Schema(description = "审批类型(0145)")
    private String stationTarget;

}
