package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Accessors(chain = true)
public class InnerReleaseBusinessDetailDTO {

    /**
     * 商机id
     */
    private Integer id;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 签约主体id
     */
    private Integer signSubjectId;

    /**
     * 签约主体公司id
     */
    private Integer companyId;

    /**
     * 商机来源渠道
     */
    private String channelId;

    /**
     * 商机分配日期
     */
    private LocalDateTime assignTime;

    /**
     * 申请时的主体释放日期
     */
    private LocalDate advertisingReleaseDate;

    /**
     * 当前商机进度
     */
    private String progress;
}
