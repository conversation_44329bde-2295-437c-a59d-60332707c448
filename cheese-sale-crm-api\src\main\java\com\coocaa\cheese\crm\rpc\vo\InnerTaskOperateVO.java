package com.coocaa.cheese.crm.rpc.vo;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/8
 */
@Data
public class InnerTaskOperateVO {

    @Schema(description = "审批状态，字典0141,0141-4代表审批单已完成")
    private String approvalStatus;

    @Schema(description = "申请延期至的日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate applyDelayDate;
}
