package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.entity.InstitutionFundChangeRecordEntity;
import com.coocaa.cheese.crm.vo.InstitutionFundChangeRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 机构资金变动记录信息转换
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Mapper
public interface InstitutionFundChangeRecordConvert  extends PageableConvert<InstitutionFundChangeRecordEntity, InstitutionFundChangeRecordVO> {
    InstitutionFundChangeRecordConvert INSTANCE = Mappers.getMapper(InstitutionFundChangeRecordConvert.class);

    /**
     * Entity 转 VO
     */
    InstitutionFundChangeRecordVO toVo(InstitutionFundChangeRecordEntity entity);

    /**
     * Entity列表 转 VO列表
     */
    List<InstitutionFundChangeRecordVO> entityToVO(List<InstitutionFundChangeRecordEntity> entityList);
} 