package com.coocaa.cheese.crm.vo;

import com.coocaa.cheese.crm.common.tools.enums.BusinessTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.ContractTypeEnum;
import lombok.Data;

/**
 * 合同状态变更VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-05
 */
@Data
public class ContractStatusChangeVO {
    /**
     * 合同ID
     */
    private Integer bzId;

    /**
     * 合同类型
     *
     * @see ContractTypeEnum
     */
    private String contractType;

    /**
     * 消息类型
     *
     * @see BusinessTypeEnum
     */
    private Integer notifyStatusType;

    /**
     * 商机ID
     */
    private Integer businessId;

    /**
     * 审批状态 (字典0079)
     */
    private String status;
} 