package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.cheese.crm.common.db.bean.BrandQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 品牌信息接口
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface BrandMapper extends BaseMapper<BrandEntity> {

    /**
     * 按条件查询品牌列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 品牌列表
     */
    IPage<BrandEntity> pageList(@Param("page") IPage<BrandEntity> page, @Param("condition") BrandQueryDTO condition);

    /**
     * 按条件查询品牌列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 品牌列表
     */
    IPage<BrandEntity> h5PageList(@Param("page") IPage<BrandEntity> page, @Param("condition") BrandQueryDTO condition);

}
