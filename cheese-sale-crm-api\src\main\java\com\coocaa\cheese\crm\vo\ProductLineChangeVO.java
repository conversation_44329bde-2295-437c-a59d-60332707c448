
package com.coocaa.cheese.crm.vo;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 产品线变更记录表VO
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "ProductLineChangeVO", description = "产品线变更记录表VO")
public class ProductLineChangeVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "商机ID")
    private Integer businessId;

    @Schema(description = "商机进度(字典0073)", maxLength = 10)
    private String progress;

    @Schema(description = "变更前产品线", maxLength = 255)
    private String beforeProductLine;

    @Schema(description = "变更后产品线", maxLength = 255)
    private String afterProductLine;

    @Schema(description = "情况说明", maxLength = 100)
    private String remark;

    @Schema(description = "执行状态(字典0163)", maxLength = 10)
    private String executeStatus;

    @Schema(description = "状态变更时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime statusChangeTime;

    @Schema(description = "删除标记  [0:否, 1:是]", maxLength = 1)
    private Integer deleteFlag;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "操作人")
    private Integer operator;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;
}