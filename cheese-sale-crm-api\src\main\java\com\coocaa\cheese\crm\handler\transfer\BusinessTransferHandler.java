package com.coocaa.cheese.crm.handler.transfer;

import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.bean.TransferParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.TransferRecordEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.ITransferRecordService;
import com.coocaa.cheese.crm.common.tools.constant.BusinessConstants;
import com.coocaa.cheese.crm.common.tools.enums.TransferBizTypeEnum;
import com.coocaa.cheese.crm.kafka.producer.CrmKafkaProducer;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import com.coocaa.cheese.crm.service.ConfigService;
import com.coocaa.cheese.crm.vo.ConfigVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务转移处理器
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessTransferHandler implements TransferHandler {

    private final IBusinessService businessService;
    private final ITransferRecordService transferRecordService;
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final CrmKafkaProducer crmKafkaProducer;
    private final ConfigService configService;

    @Override
    public TransferBizTypeEnum getTransferBizType() {
        return TransferBizTypeEnum.BUSINESS;
    }

    @Override
    public Boolean handleTransfer(TransferParam param) {
        // 1. 验证目标归属人
        validateTargetOwner(param);
        ConfigVO configVO = configService.getConfig(BusinessConstants.BUSINESS_PROTECTION_DAY_KEY);
        LocalDateTime now = LocalDateTime.now();
        LocalDate daysLater = LocalDate.now().plusDays(Integer.parseInt(configVO.getValue()));

        // 2. 批量更新商机
        List<BusinessEntity> businesses = businessService.listByIds(param.getBizIds());
        List<TransferRecordEntity> batchSaves = new ArrayList<>();

        for (BusinessEntity business : businesses) {
            // 更新商机归属信息
            updateBusinessOwnership(business, param, now);

            // 处理签约主体释放日期
            handleAdvertisingReleaseDate(business, daysLater);

            // 创建转移记录
            TransferRecordEntity record = createTransferRecord(param, business);
            batchSaves.add(record);
        }

        // 3. 批量保存数据
        saveTransferData(businesses, batchSaves, param);

        return true;
    }

    /**
     * 验证目标归属人
     */
    private void validateTargetOwner(TransferParam param) {
        List<Integer> userIds = feignAuthorityRpc.getSubordinates(param.getTargetOwnerId()).getData();
        if (CollectionUtils.isEmpty(userIds)) {
            throw new BusinessException("目标归属人不存在！");
        }
    }

    /**
     * 更新商机归属信息
     */
    private void updateBusinessOwnership(BusinessEntity business, TransferParam param, LocalDateTime now) {
        business.setOwnerId(param.getTargetOwnerId());
        business.setOwnerName(param.getTargetOwnerName());
        business.setDepartmentId(param.getTargetDepartmentId());
        business.setDepartmentName(param.getTargetDepartmentName());
        business.setAssignTime(now);
    }

    /**
     * 处理签约主体释放日期
     */
    private void handleAdvertisingReleaseDate(BusinessEntity business, LocalDate tenDaysLater) {
        if (shouldExtendReleaseDate(business, tenDaysLater)) {
            business.setAdvertisingReleaseDate(tenDaysLater);
        }
    }

    /**
     * 判断是否需要延长释放日期
     */
    private boolean shouldExtendReleaseDate(BusinessEntity business, LocalDate tenDaysLater) {
        return business.getAdvertisingReleaseDate() != null
                && business.getAdvertisingReleaseDate().isAfter(LocalDate.now())
                && business.getAdvertisingReleaseDate().isBefore(tenDaysLater)
                && BooleFlagEnum.NO.getCode().equals(business.getAdvertisingReleaseFlag());
    }

    /**
     * 创建转移记录
     */
    private TransferRecordEntity createTransferRecord(TransferParam param, BusinessEntity business) {
        TransferRecordEntity record = new TransferRecordEntity();
        record.setBizId(business.getId());
        record.setBizType(TransferBizTypeEnum.BUSINESS.getCode());
        record.setSourceOwnerId(business.getOwnerId());
        record.setSourceDepartmentId(business.getDepartmentId());
        record.setTargetOwnerId(param.getTargetOwnerId());
        record.setTargetDepartmentId(param.getTargetDepartmentId());
        record.setTransferReason(param.getTransferReason());
        record.setDescription(param.getDescription());
        return record;
    }

    /**
     * 批量保存转移数据
     */
    private void saveTransferData(List<BusinessEntity> businesses, List<TransferRecordEntity> batchSaves, TransferParam param) {
        // 批量更新转移数据
        businessService.updateBatchById(businesses);
        // 批量保存转移记录
        transferRecordService.saveBatch(batchSaves);
        // 发送kafka消息
        crmKafkaProducer.sendBusinessOwnerChange(param);
    }
} 