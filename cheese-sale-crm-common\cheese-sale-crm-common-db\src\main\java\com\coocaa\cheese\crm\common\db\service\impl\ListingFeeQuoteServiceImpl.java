package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.db.entity.ListingFeeQuoteEntity;
import com.coocaa.cheese.crm.common.db.mapper.ListingFeeQuoteMapper;
import com.coocaa.cheese.crm.common.db.service.IListingFeeQuoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 刊例费报价 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ListingFeeQuoteServiceImpl
        extends ServiceImpl<ListingFeeQuoteMapper, ListingFeeQuoteEntity>
        implements IListingFeeQuoteService {

    @Override
    public List<ListingFeeQuoteEntity> getListingFeeQuoteList(Integer recordId) {
        return this.lambdaQuery()
                .eq(ListingFeeQuoteEntity::getFundChangeRecordId, recordId)
                .eq(ListingFeeQuoteEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
    }
}