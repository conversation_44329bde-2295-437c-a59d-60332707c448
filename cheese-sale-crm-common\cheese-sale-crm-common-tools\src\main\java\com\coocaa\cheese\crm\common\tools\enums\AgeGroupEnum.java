package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 年龄段枚举
 * 70前, 70后, 80后, 90后, 00后
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Getter
@AllArgsConstructor
public enum AgeGroupEnum implements IEnumType<String> {
    BEFORE_70("0071-1", "70前"),
    AFTER_70("0071-2", "70后"),
    AFTER_80("0071-3", "80后"),
    AFTER_90("0071-4", "90后"),
    AFTER_00("0071-5", "00后");

    private final String code;
    private final String desc;

    private static final Map<String, AgeGroupEnum> BY_CODE_MAP =
            Arrays.stream(AgeGroupEnum.values())
                    .collect(Collectors.toMap(AgeGroupEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static AgeGroupEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code         代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static AgeGroupEnum parse(String code, AgeGroupEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(AgeGroupEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 