package com.coocaa.cheese.crm.service;

import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.BusinessFollowParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessFollowEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessFollowService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.BusinessChangeStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessProgressEnum;
import com.coocaa.cheese.crm.common.tools.enums.ContractStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.ContractTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.EvidenceStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.FollowSubjectEnum;
import com.coocaa.cheese.crm.convert.BusinessFollowConvert;
import com.coocaa.cheese.crm.vo.BusinessFollowVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商机跟进记录管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessFollowService {
    // 默认永久天数
    private static final int DEFAULT_FOREVER_DAYS = 9999;
    private final IBusinessFollowService businessFollowService;
    private final IBusinessService businessService;
    private final BusinessProtectionService businessProtectionService;
    private final BusinessStatusChangeLogService businessStatusChangeLogService;

    /**
     * 跟进记录详情
     *
     * @param id 跟进记录ID
     * @return 跟进记录详情
     */
    @AutoTranslate
    public BusinessFollowVO getFollowDetail(Integer id) {
        BusinessFollowEntity businessFollow = businessFollowService.getById(id);
        if (Objects.isNull(businessFollow)) {
            throw new BusinessException("跟进记录不存在");
        }
        BusinessFollowVO businessFollowVO = BusinessFollowConvert.INSTANCE.toVo(businessFollow);

        //处理跟进时间
        if (Objects.nonNull(businessFollowVO.getFollowTime())
                && businessFollowVO.getFollowTime().isBefore(Constants.CONSTANT_DEFAULT_TIME)) {
            businessFollowVO.setFollowTime(null);
        }
        return businessFollowVO;
    }

    /**
     * 创建首次跟进记录
     */
    public void createFirstFollow(BusinessEntity businessEntity, Integer ownerId) {
        BusinessFollowEntity follow = new BusinessFollowEntity();
        follow.setBusinessId(businessEntity.getId());
        follow.setCreator(ownerId);
        follow.setFollowSubject(FollowSubjectEnum.FIRST_CONTACT.getCode());
        follow.setEffectiveCommunicationFlag(BooleFlagEnum.NO.getCode());
        follow.setDeleteFlag(BooleFlagEnum.NO.getCode());
        businessFollowService.save(follow);
        // 保存进度变更记录
        businessStatusChangeLogService.saveBusinessStatusChange(BusinessChangeStatusEnum.PROGRESS,
                businessEntity, BusinessProgressEnum.INITIAL.getCode(), "商机进度初始化");
    }

    /**
     * 创建或更新跟进记录
     *
     * @param follow 跟进记录信息
     * @return true: 创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createOrUpdateFollow(Integer id, BusinessFollowParam follow) {
        BusinessEntity business = businessService.getById(follow.getBusinessId());
        if (Objects.isNull(business)) {
            throw new BusinessException("商机不存在!");
        }
        if (Objects.isNull(id) && !UserThreadLocal.getUserId().equals(business.getOwnerId())) {
            throw new BusinessException("无权创建该商机的跟进记录");
        }
        if (Objects.nonNull(id)) {
            BusinessFollowEntity businessFollow = businessFollowService.getById(id);
            if (Objects.isNull(businessFollow)) {
                throw new BusinessException("跟进记录不存在");
            } else if (!UserThreadLocal.getUserId().equals(businessFollow.getCreator())) {
                throw new BusinessException("无权修改该商机的跟进记录");
            }
        }
        // 1. 保存跟进记录
        BusinessFollowEntity entity = BusinessFollowConvert.INSTANCE.toEntity(follow);
        entity.setId(id);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());
        return Objects.isNull(id) ? businessFollowService.save(entity) : businessFollowService.updateById(entity);
    }

    /**
     * 更新商机进度
     *
     * @param businessId   商机ID
     * @param status       状态
     * @param contractType 合同类型
     */
    public void updateBusinessProgress(Integer businessId, String status, String contractType) {
        BusinessEntity business = businessService.lambdaQuery()
                .eq(BusinessEntity::getId, businessId)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        if (business == null) {
            log.error("商机不存在,ID:{}", businessId);
            return;
        }
        String oldProgress = business.getProgress();
        String newProgress = null;
        // 1. 合同已审批,更新为合同推进
        if (ContractStatusEnum.TREAT_INITIATE_CONTRACT.getCode().equals(status)
                && (BusinessProgressEnum.INITIAL.getCode().equals(oldProgress)
                || BusinessProgressEnum.FOLLOWING.getCode().equals(oldProgress))) {
            newProgress = BusinessProgressEnum.CONTRACT_PUSHING.getCode();
        }
        // 2. 合同已签约且不是零元赠播,更新为已签约
        else if ((ContractStatusEnum.ALREADY_SIGN.getCode().equals(status)
                || ContractStatusEnum.IN_PROGRESS.getCode().equals(status))
                && !ContractTypeEnum.ZERO_GIFT_PLAY.getCode().equals(contractType)
                && (BusinessProgressEnum.INITIAL.getCode().equals(oldProgress)
                || BusinessProgressEnum.FOLLOWING.getCode().equals(oldProgress)
                || BusinessProgressEnum.CONTRACT_PUSHING.getCode().equals(oldProgress))) {
            newProgress = BusinessProgressEnum.SIGNED.getCode();
        }
        // 3. 定版单已确认
        else if (EvidenceStatusEnum.CONFIRMED.getCode().equals(status)
                && !ContractTypeEnum.ZERO_GIFT_PLAY.getCode().equals(contractType)
                && (BusinessProgressEnum.INITIAL.getCode().equals(oldProgress)
                || BusinessProgressEnum.FOLLOWING.getCode().equals(oldProgress)
                || BusinessProgressEnum.CONTRACT_PUSHING.getCode().equals(oldProgress))) {
            newProgress = BusinessProgressEnum.SIGNED.getCode();
        }
        // 3. 如果需要更新进度
        if (newProgress != null && !newProgress.equals(oldProgress)) {
            updateBusinessProgressAndProtection(business, newProgress);
        }
    }

    /**
     * 更新商机进度和保护期
     */
    public void updateBusinessProgressAndProtection(BusinessEntity business, String newProgress) {
        String oldProgress = business.getProgress();
        business.setProgress(newProgress);
        if (business.getAdvertisingSubjectId() != null && business.getAdvertisingReleaseDate() != null) {
            Integer extendDays = businessProtectionService.getProtectDays(
                    business.getBrandId(), business.getDepartmentId(), business.getChannelId(), newProgress);
            if (Objects.nonNull(extendDays) && extendDays > 0) {
                if (Objects.equals(extendDays, DEFAULT_FOREVER_DAYS)) {
                    business.setAdvertisingReleaseDate(Constants.FOREVER_DATE);
                } else {
                    business.setAdvertisingReleaseDate(
                            business.getAdvertisingReleaseDate().plusDays(extendDays));
                }
            }
            businessService.updateById(business);
            // 商机进度变更
            businessStatusChangeLogService.saveBusinessStatusChange(BusinessChangeStatusEnum.PROGRESS,
                    business, newProgress, "从" + oldProgress + "变更为" + newProgress);
        }
    }

    /**
     * 删除跟进记录
     *
     * @param id 跟进记录ID
     * @return true: 删除成功
     */
    public boolean deleteFollow(Integer id) {
        BusinessFollowEntity businessFollow = businessFollowService.getById(id);
        if (businessFollow == null) {
            throw new BusinessException("跟进记录不存在");
        } else if (!UserThreadLocal.getUserId().equals(businessFollow.getCreator())) {
            throw new BusinessException("无权删除该商机的跟进记录");
        }
        return businessFollowService.lambdaUpdate()
                .set(BusinessFollowEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(BusinessFollowEntity::getId, id)
                .update();
    }

    /**
     * 获取商机的跟进记录列表
     */
    public List<BusinessFollowVO> getFollowsByBusinessId(Integer businessId) {
        return businessFollowService.lambdaQuery()
                .eq(BusinessFollowEntity::getBusinessId, businessId)
                .eq(BusinessFollowEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(BusinessFollowEntity::getCreateTime)
                .list()
                .stream()
                .map(BusinessFollowConvert.INSTANCE::toVo)
                .peek(businessFollowVO -> {
                    // 处理跟进时间
                    if (Objects.nonNull(businessFollowVO.getFollowTime())
                            && businessFollowVO.getFollowTime().isBefore(Constants.CONSTANT_DEFAULT_TIME)) {
                        businessFollowVO.setFollowTime(null);
                    }
                })
                .collect(Collectors.toList());
    }
}