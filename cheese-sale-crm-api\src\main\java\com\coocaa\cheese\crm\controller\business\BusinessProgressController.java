package com.coocaa.cheese.crm.controller.business;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.BusinessProgressParam;
import com.coocaa.cheese.crm.service.BusinessProgressService;
import com.coocaa.cheese.crm.vo.BusinessProgressVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商机进度管理
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@RestController
@RequestMapping({"/business_progress", "/business-progress"})
@Tag(name = "商机进度", description = "商机进度")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessProgressController {
    private final BusinessProgressService businessProgressService;

    /**
     * 进度详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "进度详情")
    public ResultTemplate<BusinessProgressVO> getById(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessProgressService.getDetail(id));
    }

    /**
     * 进度保存
     */
    @PostMapping
    @Operation(summary = "进度保存")
    public ResultTemplate<Boolean> save(@RequestBody BusinessProgressParam param) {
        return ResultTemplate.success(businessProgressService.createOrUpdate(param));
    }

    /**
     * 进度更新
     */
    @PutMapping
    @Operation(summary = "进度更新")
    public ResultTemplate<Boolean> update(@RequestBody BusinessProgressParam param) {
        return ResultTemplate.success(businessProgressService.createOrUpdate(param));
    }

    /**
     * 删除进度
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除进度")
    public boolean delete(@PathVariable("id") Integer id) {
        return businessProgressService.delete(id);
    }

    /**
     * 进度列表
     */
    @GetMapping("/list/{id}")
    @Operation(summary = "进度列表")
    public ResultTemplate<List<BusinessProgressVO>> list(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessProgressService.listByBusinessId(id));
    }
}

