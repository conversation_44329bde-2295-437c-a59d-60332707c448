package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.vo.BrandH5VO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 公品牌信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BrandH5Convert extends PageableConvert<BrandEntity, BrandH5VO> {
    BrandH5Convert INSTANCE = Mappers.getMapper(BrandH5Convert.class);

    /**
     * Entity 转 VO
     */
    BrandH5VO toVo(BrandEntity entity);
}
