package com.coocaa.cheese.crm.controller;

import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.service.BusinessProtectionService;
import com.coocaa.cheese.crm.service.HackService;
import com.coocaa.cheese.crm.service.PublicSeaQuotaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 用于特殊处理测试数据
 * 参考文档：https://coocaa.feishu.cn/wiki/EBfOwb4idivKTzk9f9ucBG34n0d
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Slf4j
@RestController
@RequestMapping("/hack")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class HackController {
    private final HackService hackService;
    private final BusinessProtectionService businessProtectionService;
    private final PublicSeaQuotaService publicSeaQuotaService;

    /**
     * 删除商机及关联数据
     * 品牌、渠道、投放主体、公司、产品线、银行
     */
    @DeleteMapping("/business/{id}")
    public ResultTemplate<Boolean> deleteByBusiness(@PathVariable("id") Integer businessId) {
        verifyParam(businessId, "商机");
        return ResultTemplate.success(hackService.deleteByBusiness(businessId));
    }

    /**
     * 删除机构折扣规则
     */
    @DeleteMapping("/discount-rule/{id}")
    public ResultTemplate<Boolean> deleteDiscountRule(@PathVariable("id") Integer ruleId) {
        verifyParam(ruleId, "机构折扣");
        return ResultTemplate.success(hackService.deleteDiscountRule(ruleId));
    }

    /**
     * 删除机构账户
     */
    @DeleteMapping("/institution-account/{id}")
    public ResultTemplate<Boolean> deleteInstitutionAccount(@PathVariable("id") Integer accountId) {
        verifyParam(accountId, "机构账户");
        return ResultTemplate.success(hackService.deleteInstitutionAccount(accountId));
    }

    /**
     * 删除商机保护期设置
     */
    @DeleteMapping("/business-protection/{id}")
    public ResultTemplate<Boolean> deleteBusinessProtection(@PathVariable("id") Integer protectionId) {
        verifyParam(protectionId, "商机保护期");
        return ResultTemplate.success(businessProtectionService.delete(protectionId));
    }

    /**
     * 删除公海配额
     */
    @DeleteMapping("/public-sea-quota/{id}")
    public ResultTemplate<Boolean> deleteSeaQuota(@PathVariable("id") Integer quotaId) {
        verifyParam(quotaId, "公海配额");
        return ResultTemplate.success(publicSeaQuotaService.deleteById(quotaId));
    }


    /**
     * 检查参数和删除权限
     */
    private void verifyParam(Integer identity, String type) {
        if (Objects.isNull(identity) || identity <= 0) {
            throw new BusinessException(type + "ID错误");
        }

        // 检查删除权限
        hackService.checkDeletePermission();
    }
}
