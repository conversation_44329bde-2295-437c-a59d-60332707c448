package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 客户报备部门详情VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-10
 */
@Data
public class DashboardCustomerReportDepartmentDetailVO {

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "周数")
    private Integer weekNumber;

    @Schema(description = "用户数据")
    @TransField
    private List<DashboardCustomerReportUserVO> userList;

    @Schema(description = "管理部门ID", type = "String", example = "1")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

}
