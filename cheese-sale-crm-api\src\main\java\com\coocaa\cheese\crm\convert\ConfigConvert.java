package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.entity.ConfigEntity;
import com.coocaa.cheese.crm.vo.ConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 配置信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-22
 */
@Mapper
public interface ConfigConvert extends PageableConvert<ConfigEntity, ConfigVO> {
    ConfigConvert INSTANCE = Mappers.getMapper(ConfigConvert.class);

    /**
     * Entity 转 VO
     */
    ConfigVO toVo(ConfigEntity entity);

    /**
     * VO 转 Entity
     */
    ConfigEntity toEntity(ConfigVO vo);
}
