package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.ChannelParam;
import com.coocaa.cheese.crm.common.db.entity.ChannelEntity;
import com.coocaa.cheese.crm.vo.ChannelVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 渠道信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface ChannelConvert extends PageableConvert<ChannelEntity, ChannelVO> {
    ChannelConvert INSTANCE = Mappers.getMapper(ChannelConvert.class);

    /**
     * Entity 转 VO
     */
    @Mapping(target = "parentId", expression = "java(null == entity.getParentId() || entity.getParentId() <= 0 ? null : entity.getParentId())")
    ChannelVO toVo(ChannelEntity entity);

    /**
     * VO 转 Entity
     */
    ChannelEntity toEntity(ChannelVO vo);

    /**
     * VO 转 Entity
     */
    ChannelEntity toEntity(ChannelParam param);
}
