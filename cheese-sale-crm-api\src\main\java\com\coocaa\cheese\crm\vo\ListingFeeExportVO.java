package com.coocaa.cheese.crm.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 刊例费导出VO
 * <AUTHOR>
 * @since 2025/4/10
 */
@Data
@Accessors(chain = true)
@Schema(name = "ListingFeeExportVO", description = "刊例费导出VO")
public class ListingFeeExportVO {

    /**
     * 方案id
     */
    @ExcelProperty("方案ID")
    private Integer planId;

    /**
     * 点位编码
     */
    @ExcelProperty("点位编号")
    private String pointCode;

    /**
     * 广告播放次数
     */
    @ExcelProperty("实际播放(次)")
    private Integer playCount;

    /**
     * 城市名称
     */
    @ExcelProperty("城市")
    private String cityName;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 播放日期
     */
    @ExcelProperty("播放日期")
    private LocalDate playDate;

    /**
     * 预计播放次数
     */
    @ExcelProperty("预计播放(次)")
    private Integer prePlayCount;

    /**
     * 达成率
     */
    @ExcelProperty("达成率(%)")
    private BigDecimal achievementRate;

    /**
     * 点位标识 “被动加点”
     */
    @ExcelProperty("备注")
    private String pointMark;

    /**
     * 该点位当日的实际消费
     */
    @ExcelProperty("消耗金额")
    private BigDecimal actualConsumption;
}
