package com.coocaa.cheese.crm.rpc.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 刊例明细计算入参
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class EvidencePublishCalculateParam {
    /**
     * 计费方式字典(按天、按周)
     */
    @NotNull(message = "计费方式不能为空")
    @Schema(description = "计费方式字典(按天、按周)", type = "0087-1")
    private String billingType;

    @NotNull(message = "投放周期不能为空")
    @Schema(description = "投放周期", type = "Integer", example = "投放周期")
    private Integer launchCycle;

    @NotEmpty(message = "方案id集合不能为空")
    @Schema(description = "方案id集合", type = "List", example = "[8,23,34]")
    private List<Integer> planIdList;
}
