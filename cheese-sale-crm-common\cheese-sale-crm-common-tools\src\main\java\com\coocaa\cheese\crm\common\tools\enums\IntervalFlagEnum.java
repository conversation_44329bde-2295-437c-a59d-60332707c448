package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 区间标记枚举
 * 用于表示数学区间的开闭状态
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-13
 */
@Getter
@AllArgsConstructor
public enum IntervalFlagEnum implements IEnumType<Integer> {
    /**
     * 开区间
     */
    OPEN(0, "开区间"),

    /**
     * 闭区间
     */
    CLOSED(1, "闭区间"),

    /**
     * 正无穷
     */
    POSITIVE_INFINITY(2, "正无穷");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, IntervalFlagEnum> BY_CODE_MAP =
            Arrays.stream(IntervalFlagEnum.values())
                    .collect(Collectors.toMap(IntervalFlagEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static IntervalFlagEnum parse(Integer code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static IntervalFlagEnum parse(Integer code, IntervalFlagEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code) {
        return Optional.ofNullable(parse(code)).map(IntervalFlagEnum::getDesc).orElse(StringUtils.EMPTY);
    }

    /**
     * 判断是否为开区间
     */
    public static boolean isOpen(Integer code) {
        return code != null && code == OPEN.code;
    }

    /**
     * 判断是否为闭区间
     */
    public static boolean isClosed(Integer code) {
        return code != null && code == CLOSED.code;
    }

    /**
     * 判断是否为正无穷
     */
    public static boolean isPositiveInfinity(Integer code) {
        return code != null && code == POSITIVE_INFINITY.code;
    }
} 