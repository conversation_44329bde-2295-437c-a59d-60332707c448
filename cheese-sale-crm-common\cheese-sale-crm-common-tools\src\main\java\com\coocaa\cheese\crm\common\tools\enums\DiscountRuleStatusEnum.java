package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售-折扣策略状态枚举(0081)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-26
 */
@Getter
@AllArgsConstructor
public enum DiscountRuleStatusEnum implements IEnumType<String> {

    NOT_EFFECTIVE("0081-1", "未生效"),
    EFFECTIVE("0081-2", "已生效"),
    INVALID("0081-3", "生效前已作废");

    private static final Map<String, DiscountRuleStatusEnum> BY_CODE_MAP =
            Arrays.stream(DiscountRuleStatusEnum.values())
                    .collect(Collectors.toMap(DiscountRuleStatusEnum::getCode, item -> item));
    private final String code;
    private final String desc;

    /**
     * 将代码转成枚举
     */
    public static DiscountRuleStatusEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static DiscountRuleStatusEnum parse(String code, DiscountRuleStatusEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(DiscountRuleStatusEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 