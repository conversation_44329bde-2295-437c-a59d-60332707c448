package com.coocaa.cheese.crm.common.tools.enums;

import lombok.Getter;

/**
 * 主体操作枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-19
 */
@Getter
public enum SubjectOperationEnum {

    PK_CHALLENGE(1, "PK挑战"),
    CREATE_PROTECTION(2, "创建保护"),
    CREATE_OPPORTUNITY(3, "创建商机"),
    VIEW_OPPORTUNITY(4, "查看商机"),
    SALVAGE(5, "打捞");

    private final Integer code;
    private final String desc;

    SubjectOperationEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SubjectOperationEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SubjectOperationEnum button : SubjectOperationEnum.values()) {
            if (button.getCode().equals(code)) {
                return button;
            }
        }
        return null;
    }

    public static SubjectOperationEnum getByDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (SubjectOperationEnum button : SubjectOperationEnum.values()) {
            if (button.getDesc().equals(desc)) {
                return button;
            }
        }
        return null;
    }
}
