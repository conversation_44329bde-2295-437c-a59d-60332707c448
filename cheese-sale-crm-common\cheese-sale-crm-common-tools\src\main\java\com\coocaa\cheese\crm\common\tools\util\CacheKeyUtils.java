package com.coocaa.cheese.crm.common.tools.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Arrays;
import java.util.StringJoiner;

/**
 * 统一生成缓存Key
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-11
 */
@Slf4j
public class CacheKeyUtils {
    private static final String SEPARATOR = ":";
    private static final String PREFIX = "sale:crm";

    /**
     * 生成分布式锁的Key
     * => sale:crm:lock:{identity}
     *
     * @return 缓存Key
     */
    public static String getLockKey(String identity) {
        return new StringJoiner(SEPARATOR).add(PREFIX).add("lock").add(identity).toString().toLowerCase();
    }

    /**
     * 系统配置Key
     */
    public static String getConfigKey() {
        return new StringJoiner(SEPARATOR).add(PREFIX).add("config").toString().toLowerCase();
    }

    /**
     * 根据缓存因子生成缓存Key
     * <p>
     * getCacheKeyKey("contract", "code", DATE_FORMATTER.format(LocalDate.now())) => sale:cms:contract:code:20250221
     *
     * @param factors 缓存因子
     * @return 缓存Key
     */
    public static String getCacheKeyKey(String... factors) {
        StringJoiner joiner = new StringJoiner(SEPARATOR).add(PREFIX);
        if (ArrayUtils.isNotEmpty(factors)) {
            Arrays.stream(factors).filter(StringUtils::isNotBlank).map(String::trim).forEach(joiner::add);
        }
        return joiner.toString().toLowerCase();
    }
    /**
     * 重复提交Key
     */
    public static String getRepeatSubmitKey() {
        return new StringJoiner(SEPARATOR).add(PREFIX).add("repeat").toString().toLowerCase();
    }

}
