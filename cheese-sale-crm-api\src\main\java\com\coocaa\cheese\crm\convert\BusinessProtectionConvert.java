package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.BusinessProtectionParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessProtectionEntity;
import com.coocaa.cheese.crm.vo.BusinessProtectionVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商机保护期信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BusinessProtectionConvert extends PageableConvert<BusinessProtectionEntity, BusinessProtectionVO> {
    BusinessProtectionConvert INSTANCE = Mappers.getMapper(BusinessProtectionConvert.class);

    /**
     * Entity 转 VO
     */
    BusinessProtectionVO toVo(BusinessProtectionEntity entity);

    /**
     * VO 转 Entity
     */
    BusinessProtectionEntity toEntity(BusinessProtectionVO vo);

    /**
     * VO 转 Entity
     */
    BusinessProtectionEntity toEntity(BusinessProtectionParam param);
}
