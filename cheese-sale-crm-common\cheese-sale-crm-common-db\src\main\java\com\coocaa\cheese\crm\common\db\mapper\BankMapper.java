package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.cheese.crm.common.db.bean.BankQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.BankEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 银行帐户 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-02-25
 */
public interface BankMapper extends BaseMapper<BankEntity> {
    /**
     * 按条件查询银行列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 银行列表
     */
    IPage<BankEntity> pageList(@Param("page") IPage<BankEntity> page, @Param("condition") BankQueryDTO condition);

}
