<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.ProductLineMapper">

    <select id="getProductLineNameList" resultType="java.lang.String">
        SELECT NAME
        FROM
            sale_crm_product_line
        WHERE
            delete_flag = 0
          AND brand_id = #{brandId}
    </select>

    <select id="queryProductLineBySignSubjectId" resultType="java.lang.String">
        SELECT
          scpl.name
          FROM sale_crm_product_line scpl, sale_crm_product_line_sign_subject scplss
         WHERE scplss.sign_subject_id = #{signSubjectId}
           AND scpl.id = scplss.product_line_id
           AND scpl.delete_flag = 0
           AND scplss.delete_flag = 0
    </select>

    <select id="queryInBrandUnionProductLine" resultType="com.coocaa.cheese.crm.common.db.bean.ProductLineUnionDTO">
        SELECT
          scpl.brand_id,
          scplss.sign_subject_id,
          group_concat(scpl.name) AS productLine
         FROM sale_crm_product_line scpl, sale_crm_product_line_sign_subject scplss
        WHERE scpl.brand_id = #{id}
          AND scpl.id = scplss.product_line_id
          AND scpl.delete_flag = 0
          AND scplss.delete_flag = 0
        GROUP BY scpl.brand_id, scplss.sign_subject_id
    </select>

    <select id="queryOutBrandUnionProductLine" resultType="com.coocaa.cheese.crm.common.db.bean.ProductLineUnionDTO">
        SELECT
          scpl.brand_id,
          scplss.sign_subject_id,
          scas.company_id,
          group_concat(scpl.name) AS productLine
          FROM sale_crm_product_line scpl, sale_crm_product_line_sign_subject scplss, sale_comm_advertising_subject scas
         WHERE scas.company_id IN
        <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
           AND scas.id = scplss.sign_subject_id
           AND scpl.id = scplss.product_line_id
           AND scpl.brand_id = scas.brand_id
           AND scpl.delete_flag = 0
           AND scplss.delete_flag = 0
           AND scas.delete_flag = 0
           AND scas.effective_status = 1
         GROUP BY scpl.brand_id, scplss.sign_subject_id
    </select>

    <select id="querySignSubjectProductLine" resultType="com.coocaa.cheese.crm.common.db.bean.SignSubjectProductLineDTO">
        SELECT
          scas.id,
          scpl.name
          FROM sale_crm_product_line scpl, sale_crm_product_line_sign_subject scplss, sale_comm_advertising_subject scas
        WHERE scas.id = scplss.sign_subject_id
          AND scpl.id = scplss.product_line_id
          AND scpl.brand_id = scas.brand_id
          AND scpl.delete_flag = 0
          AND scplss.delete_flag = 0
          AND scas.delete_flag = 0
          AND scas.id IN
        <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>
</mapper>