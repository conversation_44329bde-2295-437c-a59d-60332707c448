<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.PublicSeaAuthMapper">
    <!-- 按条件查询列表 -->
    <select id="pageList" resultType="com.coocaa.cheese.crm.common.db.entity.PublicSeaAuthEntity">
        SELECT pu.* 
        FROM sale_crm_public_sea_auth pu
        <where>
            AND pu.delete_flag = 0
        </where>
        ORDER BY pu.update_time DESC
    </select>
</mapper>