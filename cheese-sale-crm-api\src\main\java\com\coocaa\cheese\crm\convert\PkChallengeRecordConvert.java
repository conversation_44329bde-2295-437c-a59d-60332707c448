package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.PkChallengeRecordParam;
import com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.entity.PkChallengeRecordEntity;
import com.coocaa.cheese.crm.vo.InnerApprovePkChallengeDetailVO;
import com.coocaa.cheese.crm.vo.PkChallengeRecordDetailVO;
import com.coocaa.cheese.crm.vo.PkChallengeRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Pk挑战记录信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@Mapper
public interface PkChallengeRecordConvert extends PageableConvert<PkChallengeRecordEntity, PkChallengeRecordVO> {
    PkChallengeRecordConvert INSTANCE = Mappers.getMapper(PkChallengeRecordConvert.class);

    PkChallengeRecordEntity paramToEntity(PkChallengeRecordParam param);

    InnerApprovePkChallengeDetailVO dtoToVo(InnerApproveDetailDTO dto);

    PkChallengeRecordDetailVO entityToVO(PkChallengeRecordEntity entity);
}
