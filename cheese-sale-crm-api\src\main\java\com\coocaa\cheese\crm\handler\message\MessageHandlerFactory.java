package com.coocaa.cheese.crm.handler.message;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 消息处理器工厂
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Component
public class MessageHandlerFactory implements ApplicationContextAware, InitializingBean {

    private static final Map<String, MessageHandler> HANDLERS = new ConcurrentHashMap<>();
    private ApplicationContext applicationContext;

    /**
     * 获取消息处理器
     *
     * @param messageType 消息类型
     * @return 对应的消息处理器，如果未找到则返回null
     */
    public static MessageHandler getHandler(String messageType) {
        return HANDLERS.get(messageType);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() {
        Map<String, MessageHandler> handlerMap = applicationContext.getBeansOfType(MessageHandler.class);
        for (MessageHandler handler : handlerMap.values()) {
            HANDLERS.put(handler.getBizType().getMessageType(), handler);
        }
    }
} 