package com.coocaa.cheese.crm.controller.approve;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.SubjectExamineParam;
import com.coocaa.cheese.crm.service.CommonApproveService;
import com.coocaa.cheese.crm.vo.InnerExamineApproveVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共审批接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-27
 */
@RestController
@RequestMapping("/common/approve")
@Tag(name = "公共审批", description = "通用审批接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CommonApproveController {
    private final CommonApproveService commonApproveService;

    /**
     * 查询审批节点
     */
    @Operation(summary = "查询审批节点")
    @Parameter(name = "id", description = "审批记录id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}/node")
    public ResultTemplate<List<InnerExamineApproveVO>> queryApproveList(
            @PathVariable("id") Long id,
            @RequestParam(name = "stationTarget") String stationTarget) {
        return ResultTemplate.success(commonApproveService.queryApproveList(id, stationTarget));
    }

    /**
     * 审批
     */
    @Operation(summary = "审批")
    @Parameter(name = "id", description = "审批记录id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> approval(@PathVariable(name = "id") Long id,
                                            @RequestBody @Validated SubjectExamineParam param) {
        return ResultTemplate.success(commonApproveService.approval(id, param));
    }

    /**
     * 审批某节点通过通知下一个审批人外链
     */
    @Operation(summary = "审批某节点通过通知下一个审批人外链")
    @Parameter(name = "code", description = "审批单code", required = true, in = ParameterIn.PATH, schema = @Schema(type = "string"))
    @GetMapping("/external/{instanceCode}/task")
    public ResultTemplate<Long> queryApproveTaskUrl(@PathVariable("instanceCode") String instanceCode,
                                                    @RequestParam(name = "stationTarget") String stationTarget) {
        return ResultTemplate.success(commonApproveService.queryApproveTaskUrl(instanceCode, stationTarget));
    }

    /**
     * 审批全部通过通知提交人外链
     */
    @Operation(summary = "审批全部通过通知提交人外链")
    @Parameter(name = "code", description = "审批单code", required = true, in = ParameterIn.PATH, schema = @Schema(type = "string"))
    @GetMapping("/external/{instanceCode}/apply")
    public ResultTemplate<Long> queryApproveApplyUrl(@PathVariable("instanceCode") String instanceCode,
                                                     @RequestParam(name = "stationTarget") String stationTarget) {
        return ResultTemplate.success(commonApproveService.queryApproveApplyUrl(instanceCode, stationTarget));
    }

    /**
     * 审批全部通过通知抄送人外链
     */
    @Operation(summary = "审批全部通过通知抄送人外链")
    @Parameter(name = "code", description = "审批单code", required = true, in = ParameterIn.PATH, schema = @Schema(type = "string"))
    @GetMapping("/external/{instanceCode}/cc")
    public ResultTemplate<Long> queryApproveCcUrl(@PathVariable("instanceCode") String instanceCode,
                                                  @RequestParam(name = "stationTarget") String stationTarget) {
        return ResultTemplate.success(commonApproveService.queryApproveCcUrl(instanceCode, stationTarget));
    }
}
