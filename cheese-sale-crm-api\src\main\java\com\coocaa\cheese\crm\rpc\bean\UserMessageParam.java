package com.coocaa.cheese.crm.rpc.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025-04-02
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMessageParam {

    @NotBlank(message = "应用类型编码不能为空")
    @Schema(description = "应用类型编码(字典0124)")
    private String appCode;

    @Schema(description = "模块类型编码(字典0125)")
    private String moduleCode;

    @Schema(description = "标题,默认为消息通知")
    private String title = "消息通知";

    @NotBlank(message = "内容不能为空")
    @Schema(description = "内容")
    private String content;

    @Schema(description = "跳转链接")
    private String url;

    @Schema(description = "飞书跳转链接的文本内容")
    private String urlText;

    @NotEmpty(message = "接收用户ID不能为空")
    @Schema(description = "接收用户ID")
    private Set<Integer> receiveUserIds;

    @Schema(description = "发送用户ID,不传默认是当前登陆人")
    private Integer sendUserId;

    @Schema(description = "是否发送飞书通知,不是飞书用户则发送短信")
    private Boolean feiShuFlag = false;

    @Schema(description = "短信模板ID")
    private String templateId;

    @Schema(description = "模板参数")
    private String[] templateParams;

}
