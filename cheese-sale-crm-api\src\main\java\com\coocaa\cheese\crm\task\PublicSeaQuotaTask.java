package com.coocaa.cheese.crm.task;

import com.coocaa.cheese.crm.common.db.entity.PublicSeaQuotaEntity;
import com.coocaa.cheese.crm.common.db.service.IPublicSeaQuotaService;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * 公有海域配额定时任务
 *
 * <AUTHOR>
 * @since 2025/5/6
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PublicSeaQuotaTask {

    private final IPublicSeaQuotaService publicSeaQuotaService;

    /**
     * 配额日使用月使用清零
     */
    @XxlJob("updateMonthOrDailyUsed")
    public void updateMonthOrDailyUsed() {
        log.info("公海配额定时任务开始执行");
        LocalDate today = LocalDate.now();
        try {
            // 这里一次性全查出来(因为配置的是业务员，估计不会太多，就先不分页)
            List<PublicSeaQuotaEntity> quotas = publicSeaQuotaService.lambdaQuery()
                    .eq(PublicSeaQuotaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .list();
            if (quotas.isEmpty()) {
                log.info("公海配额定时任务结束执行，无数据需要更新");
                return;
            }
            log.info("公海配额定时任务开始执行，共有{}条数据需要更新", quotas.size());
            // 判断是否是月初，如果是则更新每月和每日已使用配额
            if (today.getDayOfMonth() == 1) {
                // 每月和每日已使用配额清零
                for (PublicSeaQuotaEntity quota : quotas) {
                    quota.setMonthlyUsed(0);
                    quota.setDailyUsed(0);
                    publicSeaQuotaService.updateById(quota);
                }
            } else {
                // 不是月初，则更新每日已使用配额
                for (PublicSeaQuotaEntity quota : quotas) {
                    quota.setDailyUsed(0);
                    publicSeaQuotaService.updateById(quota);
                }
            }
            log.info("公海配额定时任务结束执行");
        } catch (Exception e) {
            log.error("公海配额定时任务执行失败", e);
        }
    }
}
