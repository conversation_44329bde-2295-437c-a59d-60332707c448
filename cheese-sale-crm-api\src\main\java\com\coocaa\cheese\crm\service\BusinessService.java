package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.BusinessH5QueryParam;
import com.coocaa.cheese.crm.bean.BusinessJudgeParam;
import com.coocaa.cheese.crm.bean.BusinessParam;
import com.coocaa.cheese.crm.bean.BusinessRequestParam;
import com.coocaa.cheese.crm.bean.BusinessWebQueryParam;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.ChannelEntity;
import com.coocaa.cheese.crm.common.db.entity.CompanyEntity;
import com.coocaa.cheese.crm.common.db.entity.ReleaseRescheduleEntity;
import com.coocaa.cheese.crm.common.db.entity.TransferRecordEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBrandService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IChannelService;
import com.coocaa.cheese.crm.common.db.service.ICompanyService;
import com.coocaa.cheese.crm.common.db.service.IReleaseRescheduleService;
import com.coocaa.cheese.crm.common.db.service.ITransferRecordService;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.common.tools.constant.BusinessConstants;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.AdvertisingReleaseStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessChangeStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessProgressEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.ReleaseRescheduleAssignWayEnum;
import com.coocaa.cheese.crm.common.tools.enums.ReleaseRescheduleExecuteStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.TransferReasonEnum;
import com.coocaa.cheese.crm.convert.BusinessConvert;
import com.coocaa.cheese.crm.convert.BusinessDetailConvert;
import com.coocaa.cheese.crm.convert.BusinessH5Convert;
import com.coocaa.cheese.crm.convert.BusinessIndexConvert;
import com.coocaa.cheese.crm.convert.BusinessWebConvert;
import com.coocaa.cheese.crm.util.CodeGenerator;
import com.coocaa.cheese.crm.vo.BusinessAdvertisingSubjectVO;
import com.coocaa.cheese.crm.vo.BusinessContactVO;
import com.coocaa.cheese.crm.vo.BusinessDetailVO;
import com.coocaa.cheese.crm.vo.BusinessFollowVO;
import com.coocaa.cheese.crm.vo.BusinessH5VO;
import com.coocaa.cheese.crm.vo.BusinessIndexVO;
import com.coocaa.cheese.crm.vo.BusinessStatisticsVO;
import com.coocaa.cheese.crm.vo.BusinessWebVO;
import com.coocaa.cheese.crm.vo.ConfigVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 商机基础数据管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessService {
    private final IBusinessService businessService;
    private final BusinessContactService businessContactService;
    private final BusinessFollowService businessFollowService;
    private final IChannelService channelService;
    private final IBrandService brandService;
    private final BrandService brandServiceList;
    private final IAdvertisingSubjectService advertisingSubjectService;
    private final ProductLineService productLineServiceManager;
    private final CodeGenerator codeGenerator;
    private final BusinessProtectionService businessProtectionService;
    private final BusinessStatusChangeLogService businessStatusChangeLogService;
    private final ICompanyService companyService;
    private final PublicSeaQuotaService publicSeaQuotaService;
    private final ConfigService configService;
    private final IReleaseRescheduleService releaseRescheduleService;
    private final ITransferRecordService transferRecordService;


    @Value("${channel.default.id:2}")
    private String defaultChannelId;

    /**
     * 分页和排序 处理
     *
     * @param pageRequest  分页查询条件
     * @param queryWrapper 查询条件
     * @return 商机列表
     */
    private Page<BusinessEntity> getBusinessPageAndSort(PageRequestVO<?> pageRequest, QueryWrapper<BusinessEntity> queryWrapper) {
        // 正序排序处理
        if (CollectionUtil.isNotEmpty(pageRequest.getAscColumnNames())) {
            pageRequest.getAscColumnNames().forEach(columnName -> queryWrapper.orderByAsc(StrUtil.toUnderlineCase(columnName)));
        }
        //倒序排序
        if (CollectionUtil.isNotEmpty(pageRequest.getDescColumnNames())) {
            pageRequest.getDescColumnNames().forEach(columnName -> queryWrapper.orderByDesc(StrUtil.toUnderlineCase(columnName)));
        }
        queryWrapper.orderByAsc("id");
        //分页查询
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));
    }

    /**
     * 延期日期处理
     *
     * @param param    天数
     * @param business 商机
     * @return LocalDate
     */
    private LocalDate getReleaseDate(BusinessRequestParam param, BusinessEntity business) {
        Integer days = param.getDays();
        LocalDate releaseDate = param.getAdvertisingReleaseDate();
        LocalDate now = LocalDate.now();
        LocalDate newReleaseDate;
        if (days == null && releaseDate == null) {
            // 延期至永久
            newReleaseDate = BusinessConstants.FOREVER_DATE;
        } else if (releaseDate == null) {
            // 从当前释放日期开始计算
            LocalDate baseDate = business.getAdvertisingReleaseDate();
            if (baseDate == null || baseDate.isBefore(now)) {
                baseDate = now;
            }
            newReleaseDate = baseDate.plusDays(days);
        } else if (days == null) {
            newReleaseDate = releaseDate;
        } else {
            throw new BusinessException("参数有问题，请检查！");
        }
        return newReleaseDate;
    }

    /**
     * 查询主体信息
     */
    private Map<Integer, AdvertisingSubjectEntity> getIntegerAdvertisingSubjectEntityMap(List<Integer> subjectIds) {
        // 批量查询签约主体并转为Map
        Map<Integer, AdvertisingSubjectEntity> subjectMap = Collections.emptyMap();
        if (!subjectIds.isEmpty()) {
            LambdaQueryWrapper<AdvertisingSubjectEntity> subjectEntityWrapper = new LambdaQueryWrapper<>();
            subjectEntityWrapper
                    .select(AdvertisingSubjectEntity::getId,
                            AdvertisingSubjectEntity::getCompanyId,
                            AdvertisingSubjectEntity::getTopFlag)
                    .in(AdvertisingSubjectEntity::getId, subjectIds)
                    .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());
            List<AdvertisingSubjectEntity> subjectList = advertisingSubjectService.list(subjectEntityWrapper);
            subjectMap = subjectList.stream()
                    .collect(Collectors.toMap(AdvertisingSubjectEntity::getId, subject -> subject, (s1, s2) -> s1));
        }
        return subjectMap;
    }

    /**
     * 首页商机列表
     * 查询条件:
     * 1. 商机状态=活跃
     * 2. 满足权限配置的商机数据范围
     * 3. 主体释放日期在10天内(0 < 释放日期-当前日期 <= 10)
     * 4. 按主体释放日期升序排序
     *
     * @return 首页页的商机列表
     */
    @AutoTranslate
    public List<BusinessIndexVO> getListBusinessIndex(Integer showNum) {
        QueryWrapper<BusinessEntity> queryWrapper = new QueryWrapper<>(BusinessEntity.class);
        LambdaQueryWrapper<BusinessEntity> queryWrapperLambda = queryWrapper.lambda();
        ConfigVO configVO = configService.getConfig(BusinessConstants.BUSINESS_INDEX_DAY_KEY);
        // 基本查询条件
        LocalDate now = LocalDate.now();
        LocalDate threeDaysLater = now.plusDays(Integer.parseInt(configVO.getValue()));
        queryWrapperLambda
                // 未删除
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                // 商机状态=活跃
                .eq(BusinessEntity::getStatus, BusinessStatusEnum.ACTIVE.getCode())
                // 排除手动释放
                .eq(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.NO.getCode())
                // 主体释放日期在3天内(0 < 释放日期-当前日期 <= 3)
                .gt(BusinessEntity::getAdvertisingReleaseDate, now)
                .le(BusinessEntity::getAdvertisingReleaseDate, threeDaysLater);
        // 按主体释放日期升序排序
        queryWrapperLambda.orderByAsc(BusinessEntity::getAdvertisingReleaseDate);
        //如果前端没有传showNum 默认查询4条
        showNum = Optional.ofNullable(showNum).orElse(4);
        queryWrapper.last("limit " + showNum);
        // 查询数据库
        List<BusinessEntity> businessList = businessService.list(queryWrapper);
        // 没查询到数据，直接返回
        if (CollectionUtil.isEmpty(businessList)) {
            return Collections.emptyList();
        }
        // 收集所有签约主体ID
        List<Integer> subjectIds = businessList.stream()
                .map(BusinessEntity::getAdvertisingSubjectId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        // 查询关联主体转换为VO时关联签约主体信息
        Map<Integer, AdvertisingSubjectEntity> finalSubjectMap = getIntegerAdvertisingSubjectEntityMap(subjectIds);
        return businessList.stream()
                .map(entity -> {
                    BusinessIndexVO businessIndexVO = BusinessIndexConvert.INSTANCE.toVo(entity);
                    if (Objects.isNull(businessIndexVO.getAdvertisingSubjectId())) {
                        return businessIndexVO;
                    }
                    AdvertisingSubjectEntity subject = finalSubjectMap.get(entity.getAdvertisingSubjectId());
                    if (subject != null) {
                        businessIndexVO.setCompanyId(subject.getCompanyId());
                        businessIndexVO.setTopFlag(subject.getTopFlag());
                        businessIndexVO.setAdvertisingSubjectId(subject.getId());
                    }
                    return businessIndexVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 商机列表H5(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的商机列表
     */
    @AutoTranslate
    public PageResponseVO<BusinessH5VO> pageListBusinessH5(PageRequestVO<BusinessH5QueryParam> pageRequest) {
        List<String> statusList = ListUtil.toList(BusinessStatusEnum.ACTIVE.getCode(), BusinessStatusEnum.TERMINATED.getCode());
        QueryWrapper<BusinessEntity> queryWrapper = new QueryWrapper<>(BusinessEntity.class);
        LambdaQueryWrapper<BusinessEntity> queryWrapperLambda = queryWrapper.lambda();
        BusinessH5QueryParam query = pageRequest.getQuery();

        // 1.是否是本人创建
        queryWrapperLambda.eq(BooleFlagEnum.isYes(query.getOwnerFlag()), BusinessEntity::getCreator, UserThreadLocal.getUserId());
        queryWrapperLambda.ne(BooleFlagEnum.isNo(query.getOwnerFlag()), BusinessEntity::getCreator, UserThreadLocal.getUserId());

        // 2.是否是个人渠道
        queryWrapperLambda.eq(BooleFlagEnum.isYes(query.getOwnerChannelFlag()), BusinessEntity::getChannelId, defaultChannelId);
        queryWrapperLambda.ne(BooleFlagEnum.isNo(query.getOwnerChannelFlag()), BusinessEntity::getChannelId, defaultChannelId);

        // 3.进度状态
        queryWrapperLambda.in(CollectionUtil.isNotEmpty(query.getProgresses()), BusinessEntity::getProgress, query.getProgresses());

        // 3.商机状态
        queryWrapperLambda.in(CollectionUtil.isNotEmpty(query.getBusinessStatus()), BusinessEntity::getStatus, query.getBusinessStatus());
        if (CollectionUtil.isEmpty(query.getBusinessStatus())) {
            queryWrapperLambda.in(BusinessEntity::getStatus, statusList);
        }

        // 4.基本查询条件
        // 未删除
        queryWrapperLambda.eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());

        // 品牌需要查询品牌接口
        if (StringUtils.isNotBlank(query.getQueryName())) {
            // 查询品牌接口
            List<Integer> brandIds = brandServiceList.listBrands(query.getQueryName(), Boolean.FALSE)
                    .stream().map(CodeNameVO::getId).toList();
            // 增加or条件查询 品牌、归属人、商机编码
            queryWrapperLambda.and(queryOr ->
                    queryOr.like(BusinessEntity::getCode, query.getQueryName())
                            .or().like(BusinessEntity::getOwnerName, query.getQueryName())
                            .or().in(CollectionUtil.isNotEmpty(brandIds), BusinessEntity::getBrandId, brandIds)
            );
        }
        // 签约主体是否释放
        queryWrapperLambda.eq(query.getAdvertisingReleaseFlag() != null, BusinessEntity::getAdvertisingReleaseFlag, query.getAdvertisingReleaseFlag());
        // 排序和分页
        Page<BusinessEntity> page = getBusinessPageAndSort(pageRequest, queryWrapper);
        // 查询数据库
        businessService.page(page, queryWrapper);
        // 构建分页响应
        PageResponseVO<BusinessH5VO> pageResponse = BusinessH5Convert.INSTANCE.toPageResponse(page);
        // 没查到数据，直接返回
        if (CollectionUtils.isEmpty(pageResponse.getRows())) {
            return pageResponse;
        }
        // 收集所有签约主体ID
        List<Integer> subjectIds = pageResponse.getRows().stream()
                .map(BusinessH5VO::getAdvertisingSubjectId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        // 查询关联主体转换为VO时关联签约主体信息
        Map<Integer, AdvertisingSubjectEntity> finalSubjectMap = getIntegerAdvertisingSubjectEntityMap(subjectIds);
        // 批量获取签约主体所绑定的产品线名称
        Map<Integer, String> productLineNames = productLineServiceManager.getProductLineBySubjectId(subjectIds);
        pageResponse.getRows().forEach(vo -> {
            // 如果主体是空，则返回
            if (vo.getAdvertisingSubjectId() == null) {
                return;
            }
            AdvertisingSubjectEntity subject = finalSubjectMap.get(vo.getAdvertisingSubjectId());
            if (subject != null) {
                vo.setCompanyId(subject.getCompanyId());
                vo.setTopFlag(subject.getTopFlag());
                vo.setAdvertisingSubjectId(subject.getId());
                vo.setProductLineNames(productLineNames.get(subject.getId()));
            }
        });
        return pageResponse;
    }

    /**
     * 获取签约主体状态
     */
    private String getAdvertisingReleaseStatus(LocalDate releaseDate) {
        LocalDate now = LocalDate.now();
        if (releaseDate == null) {
            return AdvertisingReleaseStatusEnum.UNBOUND.getCode();
        }
        if (releaseDate.isEqual(Constants.FOREVER_DATE) || releaseDate.isAfter(Constants.FOREVER_DATE)) {
            return AdvertisingReleaseStatusEnum.FOREVER.getCode();
        }
        if (releaseDate.isBefore(now) || releaseDate.isEqual(now)) {
            return AdvertisingReleaseStatusEnum.RELEASED.getCode();
        }
        return releaseDate.toString();
    }

    /**
     * 商机列表(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的商机列表
     */
    @AutoTranslate
    public PageResponseVO<BusinessWebVO> pageListBusinessWeb(PageRequestVO<BusinessWebQueryParam> pageRequest) {
        QueryWrapper<BusinessEntity> queryWrapper = new QueryWrapper<>(BusinessEntity.class);
        LambdaQueryWrapper<BusinessEntity> queryWrapperLambda = queryWrapper.lambda();
        BusinessWebQueryParam query = pageRequest.getQuery();
        //  1、基本查询条件
        // 未删除状态
        queryWrapperLambda.eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());
        // 商品编码
        queryWrapperLambda.eq(StringUtils.isNotBlank(query.getCode()), BusinessEntity::getCode, query.getCode());
        // 品牌查询
        queryWrapperLambda.eq(query.getBrandId() != null, BusinessEntity::getBrandId, query.getBrandId());
        // 签约主体查询
        queryWrapperLambda.eq(query.getAdvertisingSubjectId() != null, BusinessEntity::getAdvertisingSubjectId, query.getAdvertisingSubjectId());
        // 商机渠道查询
        queryWrapperLambda.eq(query.getChannelId() != null, BusinessEntity::getChannelId, query.getChannelId());
        // 归属人查询
        queryWrapperLambda.eq(query.getOwnerId() != null, BusinessEntity::getOwnerId, query.getOwnerId());
        // 部门查询
        queryWrapperLambda.eq(query.getDepartmentId() != null, BusinessEntity::getDepartmentId, query.getDepartmentId());
        // 商机进度
        queryWrapperLambda.eq(StringUtils.isNotBlank(query.getProgress()), BusinessEntity::getProgress, query.getProgress());
        // 分配时间范围查询
        Optional.ofNullable(query.getAssignStartDate())
                .ifPresent(date -> queryWrapperLambda.ge(BusinessEntity::getAssignTime, date.atStartOfDay()));
        Optional.ofNullable(query.getAssignEndDate())
                .ifPresent(date -> queryWrapperLambda.le(BusinessEntity::getAssignTime, date.atTime(23, 59, 59)));
        // 日期范围查询
        Optional.ofNullable(query.getCreateStartDate())
                .ifPresent(date -> queryWrapperLambda.ge(BusinessEntity::getCreateTime, date.atStartOfDay()));
        Optional.ofNullable(query.getCreateEndDate())
                .ifPresent(date -> queryWrapperLambda.le(BusinessEntity::getCreateTime, date.atTime(23, 59, 59)));
        //默认排序
        queryWrapperLambda.orderByDesc(BusinessEntity::getCreateTime);
        // 处理分页和排序
        Page<BusinessEntity> page = getBusinessPageAndSort(pageRequest, queryWrapper);
        // 查询数据库
        businessService.page(page, queryWrapper);
        // 构建分页响应
        PageResponseVO<BusinessWebVO> pageResponse = BusinessWebConvert.INSTANCE.toPageResponse(page);
        // 没查到数据，直接返回
        if (CollectionUtils.isEmpty(pageResponse.getRows())) {
            return pageResponse;
        }
        // 收集所有签约主体ID
        List<Integer> subjectIds = page.getRecords().stream()
                .map(BusinessEntity::getAdvertisingSubjectId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        // 转换为VO时关联签约主体信息
        Map<Integer, AdvertisingSubjectEntity> finalSubjectMap = getIntegerAdvertisingSubjectEntityMap(subjectIds);
        // 查询渠道信息
        Map<Integer, String> finalChannelAliasMap = getChannelAliasMap(page.getRecords());
        pageResponse.setRows(page.getRecords().stream()
                .map(entity -> {
                    BusinessWebVO vo = BusinessWebConvert.INSTANCE.toVo(entity);
                    // 设置签约主体信息
                    if (entity.getAdvertisingSubjectId() != null) {
                        AdvertisingSubjectEntity subject = finalSubjectMap.get(entity.getAdvertisingSubjectId());
                        if (subject != null) {
                            vo.setCompanyId(subject.getCompanyId());
                            vo.setTopFlag(subject.getTopFlag());
                            vo.setAdvertisingSubjectId(subject.getId());
                        }
                    }
                    // 设置渠道别名
                    if (entity.getChannelId() != null) {
                        vo.setChannelAlias(finalChannelAliasMap.get(entity.getChannelId()));
                    }
                    // 设置签约主体状态
                    vo.setAdvertisingStatusSet(getAdvertisingReleaseStatus(entity.getAdvertisingReleaseDate()));
                    return vo;
                }).collect(Collectors.toList()));
        return pageResponse;
    }

    /**
     * 查询渠道别名
     */
    private Map<Integer, String> getChannelAliasMap(List<BusinessEntity> list) {
        List<Integer> channelIds = list.stream()
                .map(BusinessEntity::getChannelId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, String> channelAliasMap = Collections.emptyMap();
        if (!channelIds.isEmpty()) {
            channelAliasMap = channelService.lambdaQuery()
                    .select(ChannelEntity::getId, ChannelEntity::getNickName)
                    .in(ChannelEntity::getId, channelIds)
                    .eq(ChannelEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .list()
                    .stream()
                    .collect(Collectors.toMap(
                            ChannelEntity::getId,
                            ChannelEntity::getNickName,
                            (k1, k2) -> k1
                    ));
        }
        return channelAliasMap;
    }

    /**
     * 商机详情
     *
     * @param id 商机ID
     * @return 商机详情
     */
    @AutoTranslate
    public BusinessDetailVO getBusinessDetail(Integer id) {
        BusinessDetailVO detail = businessService.lambdaQuery()
                .eq(BusinessEntity::getId, id)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .oneOpt()
                .map(BusinessDetailConvert.INSTANCE::toVo)
                .orElse(null);
        if (detail == null) {
            throw new BusinessException("商机不存在！");
        }

        // 查询品牌信息获取行业
        if (detail.getBrandId() != null) {
            BrandEntity brand = brandService.lambdaQuery()
                    .select(BrandEntity::getIndustryCode, BrandEntity::getCompanyId)
                    .eq(BrandEntity::getId, detail.getBrandId())
                    .one();
            if (brand != null) {
                detail.setIndustryCode(brand.getIndustryCode());
                detail.setBrandCompanyId(brand.getCompanyId());
            }
        }

        // 查询渠道别名
        if (detail.getChannelId() != null) {
            ChannelEntity channel = channelService.lambdaQuery()
                    .select(ChannelEntity::getNickName)
                    .eq(ChannelEntity::getId, detail.getChannelId())
                    .eq(ChannelEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .one();
            if (channel != null) {
                detail.setChannelAlias(channel.getNickName());
            }
        }

        // 如果商机绑定了主体，需要查询主体和公司数据
        if (BooleFlagEnum.isYes(detail.getAdvertisingBindFlag())) {
            AdvertisingSubjectEntity subject = advertisingSubjectService.lambdaQuery()
                    .select(
                            AdvertisingSubjectEntity::getId,
                            AdvertisingSubjectEntity::getCompanyId,
                            AdvertisingSubjectEntity::getTopFlag,
                            AdvertisingSubjectEntity::getType
                    )
                    .eq(AdvertisingSubjectEntity::getId, detail.getAdvertisingSubjectId())
                    .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .one();

            if (subject == null) {
                throw new BusinessException("主体数据不存在！请检查是否被异常删除。");
            }
            // 设置主体相关信息
            detail.setCompanyId(subject.getCompanyId());
            detail.setTopFlag(subject.getTopFlag());
            detail.setAdvertisingSubjectId(subject.getId());
            detail.setAdvertisingSubjectTopFlag(subject.getTopFlag());
            detail.setSubjectType(subject.getType());
            // 查询产品线
            detail.setProductLineNames(productLineServiceManager.getProductLineBySubjectId(subject.getId()));
            detail.setProductLineList(List.of(detail.getProductLineNames().split(Constants.COMMA)));
            // 查询公司信息获取统一社会信用代码
            if (subject.getCompanyId() != null) {
                CompanyEntity company = companyService.lambdaQuery()
                        .select(CompanyEntity::getCode)
                        .eq(CompanyEntity::getId, subject.getCompanyId())
                        .one();
                if (company != null) {
                    detail.setCreditCode(company.getCode());
                }
            }
        }

        return detail;
    }

    /**
     * 创建商机
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createBusiness(BusinessParam param) {
        log.info("创建商机，参数：{}", param);
        // 1. 渠道校验
        checkChannel(param.getChannelId());
        // 2. 品牌校验
        checkBrand(param.getBrandId());
        // 3. 签约主体校验
        checkAdvertisingSubject(param);
        // 4. 商机重复校验
        checkDuplicate(param);
        // 5. 检查公海配额
        if (param.getAdvertisingSubjectId() != null) {
            publicSeaQuotaService.checkAndDeductPublicSeaQuota(UserThreadLocal.getUserId(), param.getAdvertisingSubjectId());
        }
        // 6. 创建商机
        BusinessEntity businessEntity = BusinessConvert.INSTANCE.toEntity(param);
        LocalDateTime now = LocalDateTime.now();
        // 7.设置基本信息
        businessEntity.setCode(getBusinessCode(3));
        businessEntity.setProgress(BusinessProgressEnum.INITIAL.getCode());
        businessEntity.setStatus(BusinessStatusEnum.ACTIVE.getCode());
        businessEntity.setAssignTime(now);
        // 8. 设置签约主体信息
        if (param.getAdvertisingSubjectId() != null) {
            businessEntity.setAdvertisingBindDate(LocalDate.now());
            Integer protectDays = businessProtectionService.getProtectDays(
                    param.getBrandId(), businessEntity.getDepartmentId(),
                    param.getChannelId(), BusinessProgressEnum.INITIAL.getCode());
            businessEntity.setAdvertisingReleaseDate(LocalDate.now().plusDays(protectDays));
            businessEntity.setAdvertisingBindFlag(BooleFlagEnum.YES.getCode());
        }
        // 保存商机
        boolean saved = businessService.save(businessEntity);
        if (!saved) {
            return false;
        }
        // 9. 创建联系人
        businessContactService.batchCreateContacts(param.getBusinessContactParams(), businessEntity.getId());
        // 10. 创建首次跟进记录
        businessFollowService.createFirstFollow(businessEntity, businessEntity.getOwnerId());
        // 11.创建状态初始化记录
        businessStatusChangeLogService.saveBusinessStatusChange(BusinessChangeStatusEnum.STATUS,
                businessEntity, BusinessStatusEnum.ACTIVE.getCode(), "商机状态初始化");
        return true;
    }

    /**
     * 修改商机备注说明
     *
     * @param id          商机ID
     * @param description 备注说明
     * @return true: 修改成功
     */
    public boolean updateBusinessDescription(Integer id, String description) {
        // 校验商机是否存在
        BusinessEntity business = businessService.lambdaQuery()
                .eq(BusinessEntity::getId, id)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        if (business == null) {
            throw new BusinessException("商机不存在!");
        }

        if (!UserThreadLocal.getUserId().equals(business.getOwnerId())) {
            throw new BusinessException("无权修改该商机!");
        }

        // 更新备注说明
        return businessService.lambdaUpdate()
                .set(BusinessEntity::getDescription, description)
                .eq(BusinessEntity::getId, id)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .update();
    }

    /**
     * 校验渠道
     */
    private void checkChannel(Integer channelId) {
        boolean channelExists = channelService.lambdaQuery()
                .eq(ChannelEntity::getId, channelId)
                .eq(ChannelEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .exists();
        if (!channelExists) {
            throw new BusinessException("渠道不存在");
        }
    }

    /**
     * 校验品牌
     */
    private void checkBrand(Integer brandId) {
        boolean brandExists = brandService.lambdaQuery()
                .eq(BrandEntity::getId, brandId)
                .eq(BrandEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .exists();
        if (!brandExists) {
            throw new BusinessException("品牌不存在");
        }
    }

    /**
     * 校验签约主体
     */
    private void checkAdvertisingSubject(BusinessParam param) {
        if (param.getAdvertisingSubjectId() != null) {
            AdvertisingSubjectEntity subject = advertisingSubjectService.lambdaQuery()
                    .eq(AdvertisingSubjectEntity::getId, param.getAdvertisingSubjectId())
                    .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .one();
            if (subject == null) {
                throw new BusinessException("签约主体不存在");
            }
            if (!subject.getBrandId().equals(param.getBrandId())) {
                throw new BusinessException("签约主体不属于该品牌");
            }

            // 一次性查询该签约主体关联的所有商机数据
            List<BusinessEntity> relatedBusinesses = businessService.lambdaQuery()
                    .eq(BusinessEntity::getAdvertisingSubjectId, param.getAdvertisingSubjectId())
                    .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .list();

            if (CollectionUtils.isNotEmpty(relatedBusinesses)) {
                // 商机是否存在保护期内
                boolean isOccupied = relatedBusinesses.stream()
                        .anyMatch(business ->
                                BusinessStatusEnum.ACTIVE.getCode().equals(business.getStatus())
                                        && BooleFlagEnum.NO.getCode().equals(business.getAdvertisingReleaseFlag()));

                // 商机是否休眠
                boolean isDormant = relatedBusinesses.stream()
                        .anyMatch(business -> BusinessStatusEnum.DORMANT.getCode().equals(business.getStatus()));

                // 检查是否有占用情况
                if (isOccupied || isDormant) {
                    throw new BusinessException("该签约主体已被跟进占用，无法绑定！");
                }

                // 获取商机ID列表
                List<Integer> businessIds = relatedBusinesses.stream()
                        .map(BusinessEntity::getId)
                        .toList();

                // 检查这些商机是否有待定状态的释放改期记录
                boolean hasPendingReschedule = releaseRescheduleService.lambdaQuery()
                        .in(ReleaseRescheduleEntity::getBusinessId, businessIds)
                        .eq(ReleaseRescheduleEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                        .eq(ReleaseRescheduleEntity::getExecuteStatus, ReleaseRescheduleExecuteStatusEnum.PENDING.getCode())
                        .exists();

                if (hasPendingReschedule) {
                    throw new BusinessException("该签约主体关联的商机存在待审批的释放改期申请，无法绑定！");
                }
            }
        }
    }

    /**
     * 校验商机重复
     */
    private void checkDuplicate(BusinessParam param) {
        LambdaQueryChainWrapper<BusinessEntity> duplicateCheck = businessService.lambdaQuery()
                .eq(BusinessEntity::getOwnerId, param.getOwnerId())
                .eq(BusinessEntity::getStatus, BusinessStatusEnum.ACTIVE.getCode())
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());
        if (param.getAdvertisingSubjectId() != null) {
            duplicateCheck = duplicateCheck
                    .eq(BusinessEntity::getAdvertisingSubjectId, param.getAdvertisingSubjectId());
        } else {
            duplicateCheck = duplicateCheck
                    .eq(BusinessEntity::getBrandId, param.getBrandId())
                    .isNull(BusinessEntity::getAdvertisingSubjectId);
        }
        if (duplicateCheck.exists()) {
            throw new BusinessException("请勿重复创建相同商机");
        }
    }

    /**
     * 生成商机编码
     *
     * @param i 次数
     */
    private String getBusinessCode(int i) {
        if (i == 0) {
            throw new BusinessException("商机编码生成失败，请重试！");
        }
        String resultCode = codeGenerator.generateBusinessCode();
        //查询是否已存在
        boolean exists = businessService.lambdaQuery().eq(BusinessEntity::getCode, resultCode).exists();
        if (exists) {
            return getBusinessCode(i - 1);
        }
        return resultCode;
    }

    /**
     * 获取商机进度统计
     *
     * @return 统计结果
     */
    @AutoTranslate
    public BusinessStatisticsVO getProgressStatistics() {
        // 一次性查询所有需要统计的数据
        List<BusinessEntity> businessList = businessService.lambdaQuery()
                .select(BusinessEntity::getProgress, BusinessEntity::getStatus, BusinessEntity::getAdvertisingReleaseFlag)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();

        //保护中列表
        List<BusinessEntity> releaseFlagBusinessList = businessList.stream()
                .filter(businessEntity -> businessEntity.getAdvertisingReleaseFlag().equals(BooleFlagEnum.NO.getCode())
                        && businessEntity.getStatus().equals(BusinessStatusEnum.ACTIVE.getCode()))
                .toList();

        //活跃列表
        List<BusinessEntity> activeBusinessList = businessList.stream()
                .filter(businessEntity -> businessEntity.getStatus()
                        .equals(BusinessStatusEnum.ACTIVE.getCode()))
                .toList();
        //休眠列表
        List<BusinessEntity> dormantBusinessList = businessList.stream()
                .filter(businessEntity -> businessEntity.getStatus()
                        .equals(BusinessStatusEnum.DORMANT.getCode()))
                .toList();

        //处理数据
        Map<String, Long> progressCount = releaseFlagBusinessList.stream()
                .collect(Collectors.groupingBy(
                        BusinessEntity::getProgress,
                        Collectors.counting()
                ));

        Map<Integer, Long> releaseCount = activeBusinessList.stream()
                .collect(Collectors.groupingBy(
                        BusinessEntity::getAdvertisingReleaseFlag,
                        Collectors.counting()
                ));

        // 创建返回结果
        BusinessStatisticsVO result = new BusinessStatisticsVO();

        // 1. 构建状态统计列表
        List<BusinessStatisticsVO.StatItem> statusList = new ArrayList<>();

        // 保护中商机
        BusinessStatisticsVO.StatItem protectedItem = new BusinessStatisticsVO.StatItem();
        protectedItem.setName("保护中");
        protectedItem.setDictCode(BooleFlagEnum.NO.getCode().toString());
        protectedItem.setCount(releaseCount.getOrDefault(BooleFlagEnum.NO.getCode(), 0L).intValue());
        statusList.add(protectedItem);

        // 已休眠商机
        BusinessStatisticsVO.StatItem dormantItem = new BusinessStatisticsVO.StatItem();
        dormantItem.setDictCode(BusinessStatusEnum.DORMANT.getCode());
        dormantItem.setCount(dormantBusinessList.size());
        statusList.add(dormantItem);

        // 已释放商机
        BusinessStatisticsVO.StatItem releasedItem = new BusinessStatisticsVO.StatItem();
        releasedItem.setName("已释放");
        releasedItem.setDictCode(BooleFlagEnum.YES.getCode().toString());
        releasedItem.setCount(releaseCount.getOrDefault(BooleFlagEnum.YES.getCode(), 0L).intValue());
        statusList.add(releasedItem);


        // 2. 构建进度统计列表
        List<BusinessStatisticsVO.StatItem> progressList = new ArrayList<>();

        // 初步接洽
        BusinessStatisticsVO.StatItem initialItem = new BusinessStatisticsVO.StatItem();
        initialItem.setDictCode(BusinessProgressEnum.INITIAL.getCode());
        initialItem.setCount(progressCount.getOrDefault(BusinessProgressEnum.INITIAL.getCode(), 0L).intValue());
        progressList.add(initialItem);

        // 高意向
        BusinessStatisticsVO.StatItem highIntentionItem = new BusinessStatisticsVO.StatItem();
        highIntentionItem.setDictCode(BusinessProgressEnum.FOLLOWING.getCode());
        highIntentionItem.setCount(progressCount.getOrDefault(BusinessProgressEnum.FOLLOWING.getCode(), 0L).intValue());
        progressList.add(highIntentionItem);

        // 商务推进
        BusinessStatisticsVO.StatItem contractItem = new BusinessStatisticsVO.StatItem();
        contractItem.setDictCode(BusinessProgressEnum.CONTRACT_PUSHING.getCode());
        contractItem.setCount(progressCount.getOrDefault(BusinessProgressEnum.CONTRACT_PUSHING.getCode(), 0L).intValue());
        progressList.add(contractItem);

        // 成交
        BusinessStatisticsVO.StatItem signedItem = new BusinessStatisticsVO.StatItem();
        signedItem.setDictCode(BusinessProgressEnum.SIGNED.getCode());
        signedItem.setCount(progressCount.getOrDefault(BusinessProgressEnum.SIGNED.getCode(), 0L).intValue());
        progressList.add(signedItem);

        // 设置结果
        result.setStatusList(statusList);
        result.setProgressList(progressList);

        return result;
    }

    /**
     * 延期签约主体
     *
     * @param id    商机ID
     * @param param 延期天数，如果为null则表示永久
     * @return true: 延期成功
     */
    public boolean extendAdvertisingSubject(Integer id, BusinessRequestParam param) {
        // 校验商机是否存在
        BusinessEntity business = businessService.lambdaQuery()
                .eq(BusinessEntity::getId, id)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        if (business == null) {
            throw new BusinessException("商机不存在!");
        }

        if (business.getAdvertisingSubjectId() == null) {
            throw new BusinessException("该商机尚未绑定签约主体");
        }

        boolean isOccupied = businessService.lambdaQuery()
                .eq(BusinessEntity::getAdvertisingSubjectId, business.getAdvertisingSubjectId())
                .eq(BusinessEntity::getStatus, BusinessStatusEnum.ACTIVE.getCode())
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.NO.getCode())
                .ne(BusinessEntity::getId, id)
                .exists();
        if (isOccupied) {
            throw new BusinessException("该签约主体已被跟进占用，无法延期！");
        }
        // 计算新的释放日期
        LocalDate newReleaseDate = getReleaseDate(param, business);
        return businessService.lambdaUpdate()
                .set(BusinessEntity::getAdvertisingReleaseDate, newReleaseDate)
                .set(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getId, id)
                .update();
    }

    /**
     * 释放签约主体
     *
     * @param id 商机ID
     * @return true: 释放成功
     */
    public boolean releaseAdvertisingSubject(Integer id) {
        // 校验商机是否存在
        BusinessEntity business = businessService.lambdaQuery()
                .eq(BusinessEntity::getId, id)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        if (business == null) {
            throw new BusinessException("商机不存在!");
        }

        if (business.getAdvertisingSubjectId() == null) {
            throw new BusinessException("该商机尚未绑定签约主体!");
        }
        // 立即释放签约主体
        return businessService.lambdaUpdate()
                .set(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.YES.getCode())
                .set(BusinessEntity::getAdvertisingReleaseDate, LocalDate.now())
                .eq(BusinessEntity::getId, id)
                .update();
    }

    /**
     * 根据用户查询商机ID列表
     * 商机为有效商机（保护期内）
     * 用户权限为自己及下属
     *
     * @return 商机id列表
     */
    public Set<Integer> getBusinessIdListByUser() {
        // 获取数据返回
        List<BusinessEntity> businessEntities = businessService.lambdaQuery()
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getStatus, BusinessStatusEnum.ACTIVE.getCode())
                .eq(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.NO.getCode())
                .list();
        if (CollectionUtil.isEmpty(businessEntities)) {
            return Collections.emptySet();
        }
        return businessEntities
                .stream()
                .map(BusinessEntity::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 根据签约主体ID获取保护中的商机详情
     *
     * @param advertisingSubjectId 签约主体ID
     * @return 商机详情
     */
    @AutoTranslate
    public List<BusinessAdvertisingSubjectVO> getDetailByAdvertisingSubjectId(Integer advertisingSubjectId) {
        List<BusinessEntity> entityList = businessService.lambdaQuery()
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getAdvertisingSubjectId, advertisingSubjectId)
                .list();

        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        List<BusinessAdvertisingSubjectVO> resultList = BusinessConvert.INSTANCE.toAdvertisingSubjectVOList(entityList);
        // 品牌行业代码查询
        Set<Integer> brandIds = entityList.stream()
                .map(BusinessEntity::getBrandId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Integer, BrandEntity> brandMap = !brandIds.isEmpty()
                ? brandService.lambdaQuery()
                .select(BrandEntity::getId, BrandEntity::getIndustryCode)
                .in(BrandEntity::getId, brandIds)
                .list()
                .stream()
                .collect(Collectors.toMap(BrandEntity::getId, b -> b))
                : Collections.emptyMap();

        // 设置行业代码到VO
        resultList.forEach(vo -> Optional.ofNullable(vo.getBrandId())
                .map(brandMap::get)
                .ifPresent(brand -> vo.setIndustryCode(brand.getIndustryCode())));

        return resultList;
    }

    /**
     * 查询商机是否在保护中
     *
     * @param id 商机ID
     * @return true: 商机在保护中, false: 商机不在保护中
     */
    public Boolean isBusinessProtected(Integer id) {
        return CollUtil.isNotEmpty(getBusinessProtected(List.of(id)));
    }

    /**
     * 休眠商机判定
     *
     * @param param 判定参数
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean judgeBusiness(BusinessJudgeParam param) {
        // 校验商机是否存在
        BusinessEntity business = businessService.lambdaQuery()
                .eq(BusinessEntity::getId, param.getBusinessId())
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .last("limit 1")
                .one();
        if (business == null) {
            throw new BusinessException("商机不存在!");
        }

        String oldStatus = business.getStatus();
        String newStatus = BusinessStatusEnum.ACTIVE.getCode();

        // 根据操作类型执行不同逻辑
        if (param.isRelease()) {
            // 释放公海逻辑：将商机释放状态改为是，商机状态改为活跃
            businessService.lambdaUpdate()
                    .set(BusinessEntity::getStatus, newStatus)
                    .set(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.YES.getCode())
                    .set(BusinessEntity::getUpdateTime, LocalDateTime.now())
                    .set(BusinessEntity::getOperator, UserThreadLocal.getUserId())
                    .eq(BusinessEntity::getId, param.getBusinessId())
                    .update();
        } else {
            // 延期逻辑：增加延期天数，商机状态改为活跃，商机释放状态不修改
            if (param.getDelayDays() == null || !(1 <= param.getDelayDays() && param.getDelayDays() <= 90)) {
                throw new BusinessException("延期天数必须在1-90天内");
            }

            // 计算新的释放日期
            LocalDate baseDate = business.getAdvertisingReleaseDate();
            if (baseDate == null || baseDate.isBefore(LocalDate.now())) {
                baseDate = LocalDate.now();
            }
            LocalDate newReleaseDate = baseDate.plusDays(param.getDelayDays());

            businessService.lambdaUpdate()
                    .set(BusinessEntity::getStatus, newStatus)
                    .set(BusinessEntity::getAdvertisingReleaseDate, newReleaseDate)
                    .set(BusinessEntity::getUpdateTime, LocalDateTime.now())
                    .set(BusinessEntity::getOperator, UserThreadLocal.getUserId())
                    .eq(BusinessEntity::getId, param.getBusinessId())
                    .update();

            //创建延期记录
            createDelayRecord(business, param.getDelayDays(), newReleaseDate);
        }

        // 记录状态变更
        businessStatusChangeLogService.saveBusinessStatusChange(
                BusinessChangeStatusEnum.STATUS,
                business,
                newStatus,
                "从" + oldStatus + "变更为" + newStatus
        );

        return Boolean.TRUE;
    }

    /**
     * 获取商机联系人列表
     *
     * @return 商机联系人列表
     */
    @AutoTranslate
    public List<BusinessContactVO> getContactList(Integer id) {
        return businessContactService.getContactsByBusinessId(id);
    }

    /**
     * 获取商机跟进记录列表
     *
     * @return 商机跟进记录列表
     */
    @AutoTranslate
    public List<BusinessFollowVO> getFollowList(Integer id) {
        return businessFollowService.getFollowsByBusinessId(id);
    }


    /**
     * 获取商机保护中的商机ID列表
     *
     * @return 商机保护中的商机ID列表
     */
    public List<Integer> getBusinessProtected(Collection<Integer> ids) {

        // 校验商机是否存在
        List<BusinessEntity> businesList = businessService.lambdaQuery()
                .select(BusinessEntity::getId, BusinessEntity::getAdvertisingSubjectId,
                        BusinessEntity::getAdvertisingReleaseFlag, BusinessEntity::getStatus)
                .in(BusinessEntity::getId, ids)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();

        if (CollectionUtil.isEmpty(businesList)) {
            return Collections.emptyList();
        }

        // 判断是否在保护中
        // 1. 必须是活跃状态
        // 2. 必须有绑定签约主体
        // 3. 必须是未释放状态
        return businesList.stream().filter(business -> BusinessStatusEnum.ACTIVE.getCode().equals(business.getStatus())
                        && business.getAdvertisingSubjectId() != null
                        && BooleFlagEnum.NO.getCode().equals(business.getAdvertisingReleaseFlag()))
                .map(BusinessEntity::getId)
                .toList();
    }

    /**
     * 创建延期记录
     *
     * @param business       商机实体
     * @param delayDays      延期天数
     * @param newReleaseDate 新的释放日期
     */
    private void createDelayRecord(BusinessEntity business, Integer delayDays, LocalDate newReleaseDate) {
        ReleaseRescheduleEntity record = new ReleaseRescheduleEntity();

        // 设置基本信息
        record.setBusinessId(business.getId());
        record.setAssignTime(business.getAssignTime().toLocalDate());
        record.setAdvertisingReleaseDate(business.getAdvertisingReleaseDate());
        record.setProgress(business.getProgress());
        record.setApplyDelayDate(newReleaseDate);
        record.setRemark("休眠池延期操作，延期" + delayDays + "天");

        // 设置执行状态为已执行
        record.setExecuteStatus(ReleaseRescheduleExecuteStatusEnum.ALREADY_EXECUTE.getCode());

        // 设置判定人为当前操作人
        record.setJudgeUserId(UserThreadLocal.getUserId());

        // 设置状态变更时间
        LocalDateTime now = LocalDateTime.now();
        record.setStatusChangeTime(now);

        // 设置删除标记
        record.setDeleteFlag(BooleFlagEnum.NO.getCode());

        // 查询商机分配方式
        String assignWay = getBusinessAssignWay(business.getId());
        record.setAssignWay(assignWay);

        // 保存延期记录
        releaseRescheduleService.save(record);

        log.info("创建延期记录成功，商机ID：{}，延期天数：{}，新释放日期：{}",
                business.getId(), delayDays, newReleaseDate);
    }

    /**
     * 获取商机分配方式
     *
     * @param businessId 商机ID
     * @return 分配方式代码
     */
    private String getBusinessAssignWay(Integer businessId) {
        // 查询最新的转移记录
        List<TransferRecordEntity> recordList = transferRecordService.lambdaQuery()
                .select(TransferRecordEntity::getTransferReason)
                .eq(TransferRecordEntity::getBizId, businessId)
                .eq(TransferRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(TransferRecordEntity::getCreateTime)
                .last("limit 1")
                .list();

        TransferRecordEntity recordEntity = recordList.isEmpty() ? null : recordList.get(0);

        log.info("查询商机分配方式，商机ID：{}，转移记录：{}", businessId, JSONUtil.toJsonStr(recordEntity));

        if (Objects.isNull(recordEntity)) {
            return ReleaseRescheduleAssignWayEnum.BUILD_BY_ONESELF.getCode();
        } else {
            if (Objects.equals(TransferReasonEnum.ON_JOB.getCode(), recordEntity.getTransferReason())) {
                return ReleaseRescheduleAssignWayEnum.ON_JOB_CHANGE.getCode();
            } else if (Objects.equals(TransferReasonEnum.RESIGNATION.getCode(), recordEntity.getTransferReason())) {
                return ReleaseRescheduleAssignWayEnum.OUT_JOB_HAND_OVER.getCode();
            } else {
                return ReleaseRescheduleAssignWayEnum.BUILD_BY_ONESELF.getCode();
            }
        }
    }
}
