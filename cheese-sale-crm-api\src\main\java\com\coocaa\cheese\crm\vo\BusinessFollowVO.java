package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 商机跟进记录信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessFollowVO", description = "商机跟进记录信息VO")
public class BusinessFollowVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "商机ID", type = "Integer", example = "1")
    private Integer businessId;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "跟进时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime followTime;

    @Schema(description = "跟进主题(字典0072)", type = "String", example = "0072-1")
    @TransField(type = TransTypes.DICT)
    private String followSubject;
    private String followSubjectName;

    @Schema(description = "是否有效沟通 [0:否, 1:是]", type = "Integer", example = "1")
    private Integer effectiveCommunicationFlag;

    @Schema(description = "沟通进展", type = "String", example = "客户反馈积极", maxLength = 200)
    private String communicationProgress;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "创建时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "创建人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer creator;
    private String creatorName;

    @Schema(description = "操作人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer operator;

    @Schema(description = "操作人姓名", type = "String", example = "张三")
    private String operatorName;
} 