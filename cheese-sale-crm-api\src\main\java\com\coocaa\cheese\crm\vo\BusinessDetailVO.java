package com.coocaa.cheese.crm.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.DesensitizeSerializer;
import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 商机详情VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(name = "BusinessDetailVO", description = "商机详情BusinessDetailVO")
public class BusinessDetailVO extends BusinessVO {

    @Schema(description = "是否TOP客户 [0:否, 1:是]", type = "Integer", example = "1")
    private Integer topFlag;
    private Integer advertisingSubjectTopFlag;

    @Schema(description = "是否公海保护 [0:否, 1:是]", type = "Integer", example = "1")
    private Integer protectionPeriodFlag;

    @Schema(description = "签约主体公司ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;

    @Schema(description = "品牌持有人", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY)
    private Integer brandCompanyId;
    private String brandCompanyName;

    @Schema(description = "行业编码", type = "String", example = "001")
    @TransField(type = TransTypes.INDUSTRY)
    private String industryCode;
    private String industryName;

    @Schema(description = "统一社会信用代码", type = "String", example = "91330000XXX")
    @JSONField(serializeUsing = DesensitizeSerializer.class)
    private String creditCode;

    @Schema(description = "主体类型", type = "String", example = "1")
    @TransField(type = TransTypes.DICT)
    private String subjectType;
    private String subjectTypeName;

    @Schema(description = "产品线", type = "String", example = "1")
    private String productLineNames;

    @Schema(description = "产品线", type = "String", example = "1")
    private List<String> productLineList;

}