package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *主体按钮VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-19
 */
@Data
public class SubjectButtonVO {

    @Schema(description = "商机id", type = "Integer")
    private Integer businessId;

    @Schema(description = "自己的商机列表", type = "Integer")
    @TransField
    private List<BusinessVO> businessVOs;

    @Schema(description = "按钮类型(1:PK挑战,2:创建保护,3:创建商机,4:查看商机,5:打捞)", type = "Integer", example = "1")
    private Integer type;

    @Schema(description = "提示信息", type = "String")
    private String message;
}


