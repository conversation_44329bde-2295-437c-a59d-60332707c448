package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 商机保护期VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessProtectionVO", description = "商机保护期VO")
public class BusinessProtectionVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "控制目标类型(字典0076)", type = "String", example = "0076-1")
    @TransField(type = TransTypes.DICT)
    private String targetType;
    private String targetTypeName;

    @Schema(description = "控制目标ID", type = "String", example = "1")
    private String targetId;

    @Schema(description = "控制对象名称", type = "String", example = "Benz")
    private String targetName;

    @Schema(description = "绑定签约主体(初始绑定)", type = "Integer", example = "30")
    private Integer bindAdvertisingSubject;

    @Schema(description = "建立联系(首次有效跟进)", type = "Integer", example = "15")
    private Integer establishConnection;

    @Schema(description = "合同申请(首次合同审批通过)", type = "Integer", example = "7")
    private Integer applyContract;

    @Schema(description = "合同签约(合同签约完成)", type = "Integer", example = "90")
    private Integer signContract;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "操作人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer operator;
    private String operatorName;
} 