
package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 释放改期记录表
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
@Data
@TableName("sale_crm_release_reschedule")
public class ReleaseRescheduleEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商机ID
     */
    private Integer businessId;

    /**
     * 商机分配方式(字典0150)
     */
    private String assignWay;

    /**
     * 商机分配日期
     */
    private LocalDate assignTime;

    /**
     * 申请时的主体释放日期
     */
    private LocalDate advertisingReleaseDate;

    /**
     * 申请时的商机进度(字典0073)
     */
    private String progress;

    /**
     * 申请延期至的日期
     */
    private LocalDate applyDelayDate;

    /**
     * 情况说明
     */
    private String remark;

    /**
     * 执行状态(字典0151)
     */
    private String executeStatus;

    /**
     * 失败原因(字典0176)
     */
    private String failReason;

    /**
     * 判定人
     */
    private Integer judgeUserId;

    /**
     * 状态变更时间
     */
    private LocalDateTime statusChangeTime;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}