package com.coocaa.cheese.crm.rpc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 资源VO类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-10
 */
@Data
public class ResourceVO {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "父ID")
    private Integer parentId;

    @Schema(description = "资源名称")
    private String name;

    @Schema(description = "资源编码")
    private String code;

    @Schema(description = "资源类型 [1:系统, 2:菜单, 3:内部, 4:数据]")
    private Integer type;

    @Schema(description = "资源地址")
    private String uri;

    @Schema(description = "资源排序")
    private Integer rank;

    @Schema(description = "资源状态 [false:禁用, true:启用]")
    private Boolean status;

    @Schema(description = "下级资源")
    private List<ResourceVO> children;

    @Schema(description = "层级 (系统:1级)")
    private Integer level;

    @Schema(description = "平台字典")
    private String platform;

    @Schema(description = "平台字典名称")
    private String platformName;

    @Schema(description = "是否选中[true:选中, false:未选中]")
    private Boolean checked;

    @Schema(description = "叶子节点数量")
    private Integer innerTotalCount;

    @Schema(description = "叶子节点选中数量")
    private Integer innerCheckedCount;

    @Schema(description = "已经选择的资源")
    private List<Integer> resourceIds;


}
