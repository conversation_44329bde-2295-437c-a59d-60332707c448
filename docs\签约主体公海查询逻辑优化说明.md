# 签约主体公海查询逻辑优化说明

## 优化背景

原来的NOT EXISTS逻辑在XML中实现，查询复杂且不易维护。现在将复杂的业务逻辑移到Service层，使用MyBatis-Plus进行程序化查询，提高代码可读性和维护性。

## 优化内容

### 1. XML查询简化

**原来的XML查询**：
```xml
<where>
    AND cas.delete_flag = 0
    AND cas.effective_status = 1
    AND cas.protection_period_flag = 0
    AND NOT EXISTS (
        SELECT 1 FROM sale_crm_business scbb
        WHERE cas.id = scbb.advertising_subject_id
        AND scbb.delete_flag = 0
        AND scbb.owner_id = #{currentUserId}
    )
    <!-- 搜索条件 -->
</where>
```

**优化后的XML查询**：
```xml
<where>
    AND cas.delete_flag = 0
    AND cas.effective_status = 1
    AND cas.protection_period_flag = 0
    <!-- 搜索条件 -->
</where>
```

### 2. Service层逻辑处理

将复杂的排除逻辑移到Java代码中，分为两个查询步骤：

#### 步骤1：查询基础数据
```java
// 先查询所有符合基础条件的签约主体
IPage<AdvertisingSubjectEntity> pageSubjects = advertisingSubjectService.publicSeaPageList(page, queryDto);
```

#### 步骤2：获取排除列表
```java
// 获取需要排除的签约主体ID列表
Set<Integer> excludeSubjectIds = getExcludeSubjectIds(currentUserId);
```

#### 步骤3：过滤结果
```java
// 过滤掉需要排除的签约主体
List<AdvertisingSubjectEntity> filteredRecords = pageSubjects.getRecords().stream()
        .filter(subject -> !excludeSubjectIds.contains(subject.getId()))
        .toList();
```

## 核心业务逻辑

### getExcludeSubjectIds方法

该方法实现两个查询逻辑，并将结果合并去重：

```java
private Set<Integer> getExcludeSubjectIds(Integer currentUserId) {
    Set<Integer> excludeIds = new HashSet<>();
    
    // 逻辑1：查询主体在商机表中存在保护期的数据
    List<Integer> protectedSubjectIds = businessService.lambdaQuery()
            .select(BusinessEntity::getAdvertisingSubjectId)
            .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
            .eq(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.NO.getCode()) // 保护中
            .isNotNull(BusinessEntity::getAdvertisingSubjectId)
            .list()
            .stream()
            .map(BusinessEntity::getAdvertisingSubjectId)
            .distinct()
            .toList();
    
    excludeIds.addAll(protectedSubjectIds);
    
    // 逻辑2：查询主体在商机表中存在当前用户的数据
    List<Integer> currentUserSubjectIds = businessService.lambdaQuery()
            .select(BusinessEntity::getAdvertisingSubjectId)
            .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
            .eq(BusinessEntity::getOwnerId, currentUserId)
            .isNotNull(BusinessEntity::getAdvertisingSubjectId)
            .list()
            .stream()
            .map(BusinessEntity::getAdvertisingSubjectId)
            .distinct()
            .toList();
    
    excludeIds.addAll(currentUserSubjectIds);
    
    return excludeIds;
}
```

### 两个查询逻辑说明

#### 逻辑1：保护期数据查询
- **目的**：排除在保护期内的签约主体
- **条件**：
  - `delete_flag = 0`（未删除）
  - `advertising_release_flag = 0`（保护中，未释放）
  - `advertising_subject_id IS NOT NULL`（有关联签约主体）

#### 逻辑2：当前用户数据查询
- **目的**：排除当前用户已有商机的签约主体
- **条件**：
  - `delete_flag = 0`（未删除）
  - `owner_id = 当前用户ID`（归属当前用户）
  - `advertising_subject_id IS NOT NULL`（有关联签约主体）

### 合并去重逻辑
使用`Set<Integer>`自动去重，确保同一个签约主体ID只出现一次。

## 优化优势

### 1. 代码可读性提升
- XML中的复杂SQL简化为基础查询
- 业务逻辑在Java代码中清晰表达
- 每个查询步骤都有明确的注释说明

### 2. 维护性提升
- 业务逻辑修改只需要修改Java代码
- 可以方便地添加日志和调试信息
- 单元测试更容易编写

### 3. 性能优化
- 分步查询可以更好地利用数据库索引
- 可以根据需要添加缓存策略
- 内存中的过滤操作效率较高

### 4. 扩展性提升
- 可以方便地添加新的排除逻辑
- 支持更复杂的业务规则
- 便于添加监控和统计

## 接口参数变更

### Mapper接口
```java
// 移除currentUserId参数
IPage<AdvertisingSubjectEntity> publicSeaPageList(
    @Param("page") IPage<AdvertisingSubjectEntity> page, 
    @Param("condition") AdvertisingSubjectQueryDTO condition
);
```

### Service接口
```java
// 移除currentUserId参数
IPage<AdvertisingSubjectEntity> publicSeaPageList(
    IPage<AdvertisingSubjectEntity> page, 
    AdvertisingSubjectQueryDTO condition
);
```

## 日志记录

添加了详细的日志记录，便于调试和监控：

```java
log.info("公海查询排除签约主体ID，当前用户：{}，保护期主体数：{}，当前用户主体数：{}，合并去重后总数：{}", 
        currentUserId, protectedSubjectIds.size(), currentUserSubjectIds.size(), excludeIds.size());
```

## 测试验证

提供了单元测试`AdvertisingSubjectPublicSeaServiceTest`来验证：
- 排除逻辑的正确性
- 去重功能的有效性
- 查询参数的正确传递

## 注意事项

1. **性能考虑**：如果签约主体数量很大，建议添加分页处理
2. **缓存策略**：可以考虑对排除列表进行短期缓存
3. **监控告警**：建议添加查询耗时监控
4. **数据一致性**：确保商机数据的实时性

## 总结

通过将复杂的NOT EXISTS逻辑从XML移到Service层，实现了：
- ✅ 代码可读性和维护性提升
- ✅ 业务逻辑更加清晰
- ✅ 支持更灵活的扩展
- ✅ 便于单元测试和调试
- ✅ 保持了原有的功能完整性
