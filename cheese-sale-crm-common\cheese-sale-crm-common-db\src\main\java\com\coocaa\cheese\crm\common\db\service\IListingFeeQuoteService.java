package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.entity.ListingFeeQuoteEntity;

import java.util.List;

/**
 * 刊例费报价 服务类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
public interface IListingFeeQuoteService extends IService<ListingFeeQuoteEntity> {
    /**
     * 根据变动记录id获取刊例明细
     * @param recordId 变动记录id
     * @return 刊例明细
     */
    List<ListingFeeQuoteEntity> getListingFeeQuoteList(Integer recordId);
} 