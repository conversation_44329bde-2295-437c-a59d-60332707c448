package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商机保护期
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName("sale_crm_business_protection")
public class BusinessProtectionEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 控制目标类型
     */
    private String targetType;

    /**
     * 控制目标ID
     */
    private String targetId;

    /**
     * 控制目标名称
     */
    private String targetName;

    /**
     * 绑定签约主体
     */
    private Integer bindAdvertisingSubject;

    /**
     * 首次有效跟进
     */
    private Integer establishConnection;

    /**
     * 首次合同审批通过
     */
    private Integer applyContract;

    /**
     * 合同签约完成
     */
    private Integer signContract;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
