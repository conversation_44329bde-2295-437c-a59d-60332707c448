package com.coocaa.cheese.crm.rpc.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-09
 */
@Data
public class UserVO {
    private Integer id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 工号
     */
    private String wno;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户类型 [1:内部用户, 2:外部代理商]
     */
    private Integer type;

    /**
     * 用户部门列表
     */
    @JsonProperty("userDepartments")
    private List<SimpleDepartmentVO> departments;

    @Schema(description = "渠道信息", type = "List")
    private List<SysChannelVO> channels;
}
