package com.coocaa.cheese.crm.listener.strategy;

import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.bean.TransferParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.OperateLogEntity;
import com.coocaa.cheese.crm.common.db.entity.PkChallengeRecordEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IOperateLogService;
import com.coocaa.cheese.crm.common.db.service.IPkChallengeRecordService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.BusinessStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.ExecuteStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveOpinionTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.OperateLogDataTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.OperateTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.common.tools.enums.TransferBizTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.TransferReasonEnum;
import com.coocaa.cheese.crm.kafka.producer.CrmKafkaProducer;
import com.coocaa.cheese.crm.listener.event.InnerApproveEvent;
import com.coocaa.cheese.crm.listener.strategy.approval.ApprovalStrategy;
import com.coocaa.cheese.crm.listener.strategy.approval.StationTargetAware;
import com.coocaa.cheese.crm.service.BusinessProtectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * pk挑战策略实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-24
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PkChallengeStrategy implements ApprovalStrategy, StationTargetAware {

    private static final int DEFAULT_FOREVER_DAYS = 9999;
    private final IPkChallengeRecordService pkChallengeRecordService;
    private final IBusinessService businessService;
    private final IOperateLogService operateLogService;
    private final CrmKafkaProducer crmKafkaProducer;
    private final BusinessProtectionService businessProtectionService;
    private final IInnerApproveService innerApproveService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(InnerApproveEvent event) {
        log.info("pk挑战审批通知信息:{}", JSONUtil.toJsonStr(event));
        Long id = event.getId();
        if (Objects.isNull(id)) {
            throw new BusinessException("pk挑战记录id为空");
        }
        PkChallengeRecordEntity entity = Optional.ofNullable(pkChallengeRecordService.lambdaQuery()
                        .eq(PkChallengeRecordEntity::getId, id)
                        .one())
                .orElseThrow(() -> new BusinessException("pk挑战记录不存在"));
        log.info("pk挑战审批通知信息:{}", JSONUtil.toJsonStr(entity));
        if (InnerApproveOpinionTypeEnum.AGREE.equals(event.getOpinionTypeEnum())
                && (StringUtils.isNotBlank(event.getInnerTaskOperateVO().getApprovalStatus())
                && InnerApproveStatusEnum.ALREADY_FINISH.getCode().equals(event.getInnerTaskOperateVO().getApprovalStatus()))) {
            // 审批通过
            agree(entity);
        } else if (InnerApproveOpinionTypeEnum.REJECT.equals(event.getOpinionTypeEnum())) {
            // 审批不通过
            reject(entity);
        }
    }

    /**
     * 审批通过
     *
     * @param entity pk挑战记录对象
     */
    public void agree(PkChallengeRecordEntity entity) {
        Integer businessId = entity.getBusinessId();
        BusinessEntity businessEntity = Optional.ofNullable(businessService.lambdaQuery()
                        .eq(BusinessEntity::getId, businessId)
                        .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                        .one())
                .orElseThrow(() -> new BusinessException("商机不存在"));
        Integer oldOwner = businessEntity.getOwnerId();
        String oldOwnerName = businessEntity.getOwnerName();
        String oldDepartment = businessEntity.getDepartmentId();
        String oldDepartmentName = businessEntity.getDepartmentName();
        String oldStatus = businessEntity.getStatus();
        Integer oldAdvertisingReleaseFlag = businessEntity.getAdvertisingReleaseFlag();
        businessEntity.setOwnerId(entity.getCreator());
        businessEntity.setOwnerName(entity.getCreatorName());
        businessEntity.setDepartmentId(entity.getChallengerDepartment());
        businessEntity.setDepartmentName(entity.getDepartmentName());
        Integer extendDays = businessProtectionService.getProtectDays(businessEntity.getBrandId(),
                businessEntity.getDepartmentId(), businessEntity.getChannelId(), businessEntity.getProgress());
        if (Objects.nonNull(extendDays) && extendDays > 0) {
            if (Objects.equals(extendDays, DEFAULT_FOREVER_DAYS)) {
                businessEntity.setAdvertisingReleaseDate(Constants.FOREVER_DATE);
            } else {
                businessEntity.setAdvertisingReleaseDate(LocalDate.now().plusDays(extendDays));
            }
        }
        businessEntity.setStatus(BusinessStatusEnum.ACTIVE.getCode());
        businessEntity.setAdvertisingReleaseFlag(BooleFlagEnum.NO.getCode());
        businessEntity.setUpdateTime(LocalDateTime.now());

        // 更新商机
        businessService.updateById(businessEntity);

        String content = String.format(
                "商机归属人由: %s(%d) 变更为: %s(%d)；归属部门由: %s(%s) 变更为: %s(%s);商机状态由: %s 变更为: %s;主体时间延长%d天;商机释放状态由: %s 变更为: %s。",
                oldOwnerName, oldOwner, entity.getCreatorName(), entity.getCreator(),
                oldDepartmentName, oldDepartment, entity.getDepartmentName(), entity.getChallengerDepartment(),
                oldStatus, BusinessStatusEnum.ACTIVE.getCode(), extendDays, oldAdvertisingReleaseFlag,
                BooleFlagEnum.NO.getCode());
        // 商机变更记录
        saveOperateLog(businessId, content);

        // 更新pk挑战记录状态
        pkChallengeRecordService.lambdaUpdate()
                .set(PkChallengeRecordEntity::getExecuteStatus, ExecuteStatusEnum.ALREADY_EXECUTE.getCode())
                .eq(PkChallengeRecordEntity::getId, entity.getId())
                .update();

        // 修改站内审批业务关联表
        updateInnerApprove(entity.getId(), InnerApproveOpinionTypeEnum.AGREE);

        // 发送消息
        TransferParam transferParam = getTransferParam(entity, businessId);
        crmKafkaProducer.sendBusinessOwnerChange(transferParam);
    }

    private TransferParam getTransferParam(PkChallengeRecordEntity entity, Integer businessId) {
        TransferParam transferParam = new TransferParam();
        transferParam.setBizType(TransferBizTypeEnum.BUSINESS.getCode());
        transferParam.setTransferReason(TransferReasonEnum.PK_CHALLENGE.getCode());
        transferParam.setBizIds(Collections.singletonList(businessId));
        transferParam.setTargetDepartmentId(entity.getChallengerDepartment());
        transferParam.setTargetDepartmentName(entity.getDepartmentName());
        transferParam.setTargetOwnerId(entity.getCreator());
        transferParam.setTargetOwnerName(entity.getCreatorName());
        return transferParam;
    }

    private void updateInnerApprove(Long id, InnerApproveOpinionTypeEnum innerApproveOpinionTypeEnum) {

        innerApproveService.lambdaUpdate()
                .set(InnerApproveEntity::getResult, innerApproveOpinionTypeEnum.getCode())
                .set(InnerApproveEntity::getCloseTime, LocalDateTime.now())
                .eq(InnerApproveEntity::getBizId, id)
                .update();
    }

    /**
     * 保存操作日志
     *
     * @param businessId 商机id
     * @param content    变更内容
     */
    private void saveOperateLog(Integer businessId, String content) {
        OperateLogEntity operateLogEntity = new OperateLogEntity();
        operateLogEntity.setType(OperateLogDataTypeEnum.BUSINESS.getCode());
        operateLogEntity.setBizId(Long.valueOf(businessId));
        operateLogEntity.setContent(content);
        operateLogEntity.setOperateType(OperateTypeEnum.OWNER_DEPARTMENT_CHANGE.getCode());
        operateLogEntity.setOperateTime(LocalDateTime.now());
        // 保存变更记录
        operateLogService.save(operateLogEntity);
    }

    /**
     * 审批不通过
     *
     * @param entity pk挑战记录对象
     */
    public void reject(PkChallengeRecordEntity entity) {
        // 更新pk挑战记录状态
        pkChallengeRecordService.lambdaUpdate()
                .set(PkChallengeRecordEntity::getExecuteStatus, ExecuteStatusEnum.ALREADY_CANCEL.getCode())
                .eq(PkChallengeRecordEntity::getId, entity.getId())
                .update();

        // 修改站内审批业务关联表
        updateInnerApprove(entity.getId(), InnerApproveOpinionTypeEnum.REJECT);
    }

    @Override
    public StationTargetEnum getTargetEnum() {
        return StationTargetEnum.PK_CHALLENGE;
    }
}
