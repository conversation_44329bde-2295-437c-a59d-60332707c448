package com.coocaa.cheese.crm.vo;

import com.coocaa.cheese.crm.common.tools.enums.ProgressTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 商机进度VO
 * <AUTHOR>
 * @since 2025/5/13
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessProgressVO", description = "商机进度VO")
public class BusinessProgressVO {

    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "商机ID", type = "Integer", example = "1")
    private Integer businessId;

    @Schema(description = "进度状态(0：无意向，1：有意向)", type = "Integer", example = "1")
    private Integer progressStatus;

    @Schema(description = "进度类型", type = "String", example = "INTENTION")
    private ProgressTypeEnum progressType;

    @Schema(description = "细节描述", type = "String", example = "客户有意向")
    private String detailDesc;

    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "预算金额", type = "Integer", example = "100")
    private Integer budgetAmount;
}
