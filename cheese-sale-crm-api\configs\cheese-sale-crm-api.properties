######################### actuator\u672A\u6388\u6743\u8BBF\u95EE\u6CC4\u6F0F\uFF0C\u7981\u7528\u5168\u90E8\u63A5\u53E3
endpoints.enabled=false
springdoc.api-docs.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html

######################### \u767E\u5EA6\u5730\u56FE #########################
baidu.map.ak=NPIFXEY8e6X2ieDLJ6IhqMtiM1uTBb1J

######################### kafka配置 #########################
# Kafka服务器地址
spring.kafka.bootstrap-servers=172.20.150.128:9092,172.20.150.129:9092,172.20.150.130:9092

# 生产者配置
# 发生错误后，消息重发的次数
spring.kafka.producer.retries=0
# 批次大小，当多个消息发送到同一分区时，生产者会把它们放在同一批次。该参数指定批次内存大小
spring.kafka.producer.batch-size=16384
# 生产者内存缓冲区大小
spring.kafka.producer.buffer-memory=33554432
# 键的序列化方式
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
# 值的序列化方式
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
# 应答级别(acks):
# 0-生产者不等待应答
# 1-等待leader副本应答
# all-等待所有副本应答
spring.kafka.producer.acks=1

# 消费者配置
# 消费者组ID
spring.kafka.consumer.group-id=cheese-sale-crm
# 是否自动提交偏移量
spring.kafka.consumer.enable-auto-commit=false
# 自动提交间隔时间
spring.kafka.consumer.auto-commit-interval=1S
# 键的反序列化方式
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
# 值的反序列化方式
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
# 当分区没有初始偏移量或当前偏移量不存在时的处理方式
# latest: 从最新记录开始读取
# earliest: 从起始位置读取
spring.kafka.consumer.auto-offset-reset=latest
# 轮询超时时间(毫秒)
spring.kafka.consumer.poll-timeout=40000
# 批量消费并发数
spring.kafka.consumer.batch-concurrency=10
# 批量消费时，每次轮询最大记录数
spring.kafka.consumer.batch-max-poll-records=1500
# 单次消费最大记录数
spring.kafka.consumer.max-poll-records=150

######################### COS\u914D\u7F6E #########################
cos.secret-id=AKIDWt8B4TkqpfMtJWVHrNirpdOyeKuPo8nJ
cos.secret-key=PgqphNtS1Qkw2ysfIk063qStBJEwY1iM
cos.region=ap-guangzhou
cos.connection-timeout=5000
cos.socket-timeout=10000
cos.bucket-name=logs-iot-1330579985
cos.domain=https://logs-iot.coocaa.com

# \u817E\u8BAF\u4E91cos\u914D\u7F6E
tx.cos.secret-id=AKIDWt8B4TkqpfMtJWVHrNirpdOyeKuPo8nJ
tx.cos.secret-key=PgqphNtS1Qkw2ysfIk063qStBJEwY1iM
tx.cos.bucket=logs-iot-1330579985
# \u6839\u8DEF\u5F84
tx.cos.project=sale-crm
tx.cos.region=ap-guangzhou
tx.cos.bucket-host=https://logs-iot.coocaa.com

######################### JTW Token #########################
jwt.secret=devyour_secret_aakey_must_be_aaat_least_256axa_bits_long_for_hs256_algorithm
jwt.exp=3600

######################### \u52A0\u89E3\u5BC6 #########################
rsa.publicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDoUcrDn7Ri5csvyiSfdlEhJjXv4PNLrJ2BE9vUbNHtuF7K/OsTMnUu5HUzoj2cvKJ/ZdvoF00gwDlUEjyXyPv5kQ425QRwNdmEA+10WRzZqt8Iz8n/gUzd7jynXvExgDfKkB2Cm3xZdQLn7y1zTWi272kU+IYXhBDp+Y19s/wFMQIDAQAB
rsa.privateKey=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBANdaZB4xWCSFh0g66nli0HRY/FPaxLyXBE/gZ3wZgiFTZvfxuzp/mD9mbE0/96XFCLOm5MQq1z1vXmkRbEgz9rkgpERrECAvE8RGIVuPh5p2RSDsil58dQmjR6WYh+1gbjrPp+FN0L7fL6jxuLHNhggEs8Ly1+0+wAiKJH3ku1X9AgMBAAECgYAa1Hsmu9UUb8JkAFsYFnSMGk4NkHL0YAg8f2PFNVReHIr2lxpRdnYcaAFUOoT7FSo5X7CetAuu7sIfavlHoZm6SkpkZg+DRGDhkW6ECQNY637bQMbsOcicHXDNAJEGFS1+D2Crb+hpc4oDAkp9YHWFp69e4R3yLU9gV3omiRn7LQJBAPKDDPqTXAGp4We5nFqqFh2+GVN5dSoFJDB9mhFN+mkVug4ylFCsa9Zx+jHMPjU/TIZyruBWLNbjkwAT/VmLs08CQQDjVKVRk2SlrizqYS3uDrZkAoHLGbi7yhpfCg3+XkRqdQbrsxqeBUX2GkB4dCjBilujuHxOjdA7KlIFRIRUXT7zAkBSxSfI3kKozrKAFHmo+CRUQm2ib/HtRZP0eOn8vgCZnTDDZ7/3SOIFbyFVncAdrF0SBlJpfwAO1910SV5PEb9BAkEAzj2gEkc3W3yOcUEjC0O7wzXnDRzT1UMA89JKaciaJz7uvW1rK4u7MhAVB3LS+uuMQheAZ0APd23opbDIoUVLcwJAIqwLGCgBuHUqCe1wZosUVMISPqOxGtT4uRo5yz/Bln2S2JUhlclXmvP0YWixFHH4lyJOYszUVPrvsBbE4t14ag==

######################### xxl-job #########################
xxl.job.admin.addresses=http://*************:8081/
# \u6267\u884C\u5668\u540D\u79F0
xxl.job.executor.appname=cheese-sale-crm-api
# \u6267\u884C\u5668ip
xxl.job.executor.ip=
#\u6267\u884C\u5668\u7AEF\u53E3\u53F7
xxl.job.executor.port=9881
### xxl-job log path
xxl.job.executor.logpath=~/data/temp/temp/
#xxl.job.executor.logpath=/data/log/task-job/
### \u65E5\u5FD7\u4FDD\u5B58\u5929\u6570
xxl.job.executor.logretentiondays=30
#\u5E7F\u544Axxl-job\u7684token
xxl.job.accessToken=ad-cobb-schedule-token


######################### \u98DE\u4E66\u5BA1\u6279 #########################
contract.approval.appId=cli_a7e0236962bd500e
contract.approval.appSecret=WSkGwKEQdAubnGn2ZlXXCglRfHlEFgsX
contract.approval.approvalCode=CE5E98A3-2F3E-4CED-9F21-C79DFC0A2621
contract.approval.detail.prefix=http://dev-meth-cshimedia.coocaa.com/contract-detail?id=
contract.approval.radio.yes=m52apibk-xhvft93uoyk-0
contract.approval.radio.no=m52apibk-vxqzfrsvsd-0
contract.approval.radio.normal.yes=m52aq2si-j2a686kb98d-0
contract.approval.radio.normal.no=m52aq2si-5t2dyjzw60u-0

######################### excel\u6A21\u677F #########################
template.excel.supplier=''
