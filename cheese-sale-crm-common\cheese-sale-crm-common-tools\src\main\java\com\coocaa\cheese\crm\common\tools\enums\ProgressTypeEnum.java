package com.coocaa.cheese.crm.common.tools.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 跟进进度类型枚举
 * <AUTHOR>
 * @date 2025/5/14
 */
@Getter
@AllArgsConstructor
public enum ProgressTypeEnum {

    /**
     * 意向
     */
    INTENTION("INTENTION", "意向"),

    /**
     * 预算
     */
    BUDGET("BUDGET", "预算"),

    /**
     * 需求
     */
    REQUIREMENT("REQUIREMENT", "需求");

    /**
     * 枚举值
     */
    private final String value;

    /**
     * 枚举描述
     */
    private final String description;


    /**
     * 根据value获取枚举
     * @param value 枚举值
     * @return 对应的枚举
     */
    public static ProgressTypeEnum getByValue(String value) {
        for (ProgressTypeEnum type : ProgressTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断value是否有效
     * @param value 待校验的值
     * @return true-有效，false-无效
     */
    public static boolean isValid(String value) {
        return getByValue(value) != null;
    }
}
