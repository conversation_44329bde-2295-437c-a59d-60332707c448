package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02
 */
@Data
@Schema(description = "方案转销售入参")
public class PlanUnfreezeParam {

    @Schema(description = "机构账户ID", type = "Integer", example = "1")
    @NotNull(message = "机构账户ID不能为空")
    private Integer institutionId;

    @NotNull(message = "方案id不能为空")
    @Schema(description = "方案id", type = "Integer", example = "8")
    private Integer planId;
}
