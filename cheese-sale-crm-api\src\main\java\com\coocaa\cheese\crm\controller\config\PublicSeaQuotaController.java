package com.coocaa.cheese.crm.controller.config;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.PublicSeaQuotaParam;
import com.coocaa.cheese.crm.service.PublicSeaQuotaService;
import com.coocaa.cheese.crm.vo.PublicSeaQuotaVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 公海配额管理
 *
 * <AUTHOR>
 * @since 2025-04-30
 */

@Slf4j
@RestController
@RequestMapping("/public-sea-quota")
@Tag(name = "公海配额管理", description = "公海配额管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PublicSeaQuotaController {

    private final PublicSeaQuotaService publicSeaQuotaService;

    @Operation(summary = "公海配额详情")
    @Parameter(name = "id", description = "公海配额详情", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<PublicSeaQuotaVO> getById(@PathVariable Integer id) {
        return ResultTemplate.success(publicSeaQuotaService.getById(id));
    }

    @Operation(summary = "保存公海配额")
    @PostMapping
    public ResultTemplate<Boolean> create(@RequestBody @Validated PublicSeaQuotaParam param) {
        return ResultTemplate.success(publicSeaQuotaService.createOrUpdate(null, param));
    }

    @Operation(summary = "更新公海配额")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> update(@PathVariable("id") Integer id, @RequestBody @Validated PublicSeaQuotaParam param) {
        return ResultTemplate.success(publicSeaQuotaService.createOrUpdate(id, param));
    }

    @Operation(summary = "删除公海配额")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> delete(@PathVariable("id") Integer id) {
        return ResultTemplate.success(publicSeaQuotaService.deleteById(id));
    }

    /**
     * 公海配额列表
     */
    @Operation(summary = "公海配额列表")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<PublicSeaQuotaVO>> pageList(@RequestBody PageRequestVO<String> pageRequestVO) {
        return ResultTemplate.success(publicSeaQuotaService.pageList(pageRequestVO));
    }

}
