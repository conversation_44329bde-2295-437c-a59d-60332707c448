@startuml
title 外部系统资金解冻流程

actor "外部系统(售卖平台)" as ExternalSystem
participant "外部接口\n/api/external-project-fund/*" as ExternalApiController
participant "资金账户管理" as AccountFundController
participant "资金账户Service" as FundService
database "机构资金变动记录表\ninstitution_fund_change_record" as ChangeRecordDB
database "机构资金账户表\ninstitution_fund_account" as FundAccountDB

ExternalSystem -> ExternalApiController: POST /api/external-project-fund/unfreeze
note right
请求参数：
- 方案ID
- 机构账户ID
end note

ExternalApiController -> AccountFundController: 调用内部解冻接口

AccountFundController -> ChangeRecordDB: 查询资金变动记录
note right
查询条件：
- 方案ID
- 机构账户ID
- 包括人工创建的记录
end note
ChangeRecordDB --> AccountFundController: 返回资金变动记录列表

alt 未找到资金变动记录
    AccountFundController --> ExternalApiController: 返回错误信息"未找到需要解冻的记录"
    ExternalApiController --> ExternalSystem: 返回解冻失败结果
else 找到资金变动记录
    loop 对每条资金变动记录
        AccountFundController -> ChangeRecordDB: 更新变动类型为"解冻"
        note right: 将变动类型设置为"解冻"
    end

    AccountFundController -> FundService: 计算解冻总金额
    note right: 解冻总金额 = ∑变动记录的变动金额

    AccountFundController -> FundAccountDB: 更新账户冻结金额
    note right: 减少累计冻结金额

    AccountFundController --> ExternalApiController: 返回解冻成功结果
    ExternalApiController --> ExternalSystem: 返回解冻成功结果
end

@enduml