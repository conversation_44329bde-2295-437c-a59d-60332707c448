package com.coocaa.cheese.crm.kafka.config;

import com.coocaa.cheese.crm.kafka.constant.KafkaConstants;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Kafka Topic 配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-05
 */
@Configuration
public class KafkaConfig {

    /**
     * 商机归属人变更Topic配置
     * 分区数：6
     * 副本数：2
     */
    @Bean(name = "businessOwnerChangeTopic")
    public NewTopic businessOwnerChangeTopic() {
        return new NewTopic(KafkaConstants.TOPIC_BUSINESS_OWNER_CHANGE, 6, (short) 2);
    }

    /**
     * 主体变更Topic配置
     * 分区数：6
     * 副本数：2
     */
    @Bean(name = "advertisingSubjectChangeTopic")
    public NewTopic advertisingSubjectChangeTopic() {
        return new NewTopic(KafkaConstants.TOPIC_ADVERTISING_SUBJECT_CHANGE, 6, (short) 2);
    }

}
