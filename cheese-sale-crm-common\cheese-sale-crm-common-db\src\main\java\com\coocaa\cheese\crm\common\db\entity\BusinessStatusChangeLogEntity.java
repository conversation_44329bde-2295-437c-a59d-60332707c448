package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商机状态变更记录
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Data
@TableName("sale_crm_business_status_change_log")
public class BusinessStatusChangeLogEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 数据类型 (字典0077)
     */
    private String type;

    /**
     * 业务ID (商机,...)
     */
    private Integer bizId;

    /**
     * 业务编码 (商机,...)
     */
    private String bizCode;

    /**
     * 业务状态(字典0072)
     */
    private String status;

    /**
     * 状态变更时间
     */
    private LocalDateTime changeTime;

    /**
     * 状态变更操作人
     */
    private Integer operator;

    /**
     * 状态变更操作人工号
     */
    private String operatorWno;

    /**
     * 状态变更操作人姓名
     */
    private String operatorName;

    /**
     * 补充内容
     */
    private String content;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
