package com.coocaa.cheese.crm.controller;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.service.CosService;
import com.coocaa.cheese.crm.vo.CosVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * COS功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-22
 */
@RestController
@RequestMapping("/cos")
@Tag(name = "COS相关功能")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CosController {
    private final CosService cosService;

    /**
     * 获取临时密钥
     */
    @Operation(summary = "获取临时密钥")
    @GetMapping("/temp/key")
    public ResultTemplate<CosVO> getTempKey() {
        return ResultTemplate.success(cosService.getTempKey());
    }
}
