@startuml BusinessService商机创建流程

skinparam backgroundColor white
skinparam handwritten false

title BusinessService商机创建流程

start

:接收商机创建参数(BusinessParam);
note right
包含:
- 商机基本信息
- 联系人信息
- 签约主体信息
end note

:参数校验;
note right
- 必填字段检查
- 数据格式校验
end note

:数据归属校验;
note right
方法: checkOwnership()
- 检查用户是否登录
- 如果未指定归属人，设为当前用户
- 如果指定归属人，校验是否有权限
- 校验部门权限
end note

:渠道校验;
note right
方法: checkChannel()
- 检查渠道是否存在
end note

:品牌校验;
note right
方法: checkBrand()
- 检查品牌是否存在
end note

:签约主体校验;
note right
方法: checkAdvertisingSubject()
如果绑定了签约主体:
- 检查主体是否存在
- 检查主体是否属于该品牌
- 检查主体是否被其他商机占用
end note

:商机重复校验;
note right
方法: checkDuplicate()
检查条件:
- 相同归属人
- 活跃状态
- 未删除
- 相同主体或相同品牌(无主体)
end note

:创建商机基础信息;
note right
- 生成商机编码(getBusinessCode())
- 设置初始进度(INITIAL)
- 设置活跃状态(ACTIVE)
- 设置分配时间(当前时间)
- 设置部门信息
- 设置创建人信息
end note

if (是否绑定签约主体?) then (yes)
  :设置签约主体信息;
  note right
  - 设置绑定日期(当前日期)
  - 计算保护期(businessProtectionService.getProtectDays())
  - 设置释放日期(当前日期+保护期)
  - 设置绑定标识(YES)
  - 设置释放标识(NO)
  end note
endif

:保存商机信息;
note right
方法: businessService.save()
事务开始
end note

:创建联系人;
note right
方法: businessContactService.batchCreateContacts()
- 批量创建联系人
- 校验手机号重复
- 最多10个联系人
- 必须至少1个联系人
end note

:创建首次跟进记录;
note right
方法: businessFollowService.createFirstFollow()
- 记录首次跟进内容
- 设置跟进人为商机归属人
- 保存进度变更记录
end note

:创建状态初始化记录;
note right
方法: businessStatusChangeLogService.saveBusinessStatusChange()
- 记录商机状态变更为活跃
- 保存商机状态变更记录
end note

:提交事务;

stop

@enduml