package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.util.AesUtils;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.BusinessContactParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessContactEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessContactService;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.convert.BusinessContactConvert;
import com.coocaa.cheese.crm.vo.BusinessContactVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 商机联系人管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessContactService {
    private final IBusinessContactService businessContactService;

    /**
     * 联系人详情
     *
     * @param id 联系人ID
     * @return 联系人详情
     */
    @AutoTranslate
    public BusinessContactVO getContactDetail(Integer id) {
        return Optional.ofNullable(businessContactService.getById(id))
                .map(BusinessContactConvert.INSTANCE::toVo)
                .orElse(null);
    }

    /**
     * 批量创建联系人
     */
    public void batchCreateContacts(List<BusinessContactParam> contacts, Integer businessId) {
        if (CollectionUtil.isEmpty(contacts)) {
            return;
        }
        // 校验数量限制
        if (contacts.size() > 10) {
            throw new BusinessException("最多只能添加10个联系人");
        }
        // 检查手机号重复
        Set<String> mobileSet = new HashSet<>();
        for (BusinessContactParam contact : contacts) {
            if (!mobileSet.add(contact.getMobile())) {
                throw new BusinessException("联系人手机号重复");
            }
        }
        // 批量创建
        List<BusinessContactEntity> entities = BusinessContactConvert.INSTANCE.toList(contacts);
        entities.forEach(entity -> {
            entity.setBusinessId(businessId);
            entity.setDeleteFlag(BooleFlagEnum.NO.getCode());
        });
        businessContactService.saveBatch(entities);
    }

    /**
     * 创建或更新联系人
     *
     * @param contact 联系人信息
     * @return true: 创建成功
     */
    public boolean createOrUpdateContact(Integer id, BusinessContactParam contact) {
        // 检查联系人是否手机号重复
        boolean mobileExists = businessContactService.lambdaQuery()
                .eq(BusinessContactEntity::getMobile, AesUtils.encryptHex(contact.getMobile()))
                .eq(BusinessContactEntity::getBusinessId, contact.getBusinessId())
                .eq(BusinessContactEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .ne(Objects.nonNull(id), BusinessContactEntity::getId, id)
                .exists();

        if (mobileExists) {
            throw new BusinessException("联系人手机号重复");
        }

        BusinessContactEntity entity = BusinessContactConvert.INSTANCE.toEntity(contact);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());
        entity.setId(id);
        return id == null ? businessContactService.save(entity) : businessContactService.updateById(entity);
    }

    /**
     * 删除联系人
     *
     * @param id 联系人ID
     * @return true: 删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContact(Integer id) {
        // 获取联系人信息并校验
        BusinessContactEntity contact = Optional.ofNullable(businessContactService.getById(id))
                .orElseThrow(() -> new BusinessException("联系人不存在"));

        // 检查是否为商机的最后一个联系人
        long contactCount = businessContactService.lambdaQuery()
                .eq(BusinessContactEntity::getBusinessId, contact.getBusinessId())
                .eq(BusinessContactEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .count();

        if (contactCount <= 1) {
            throw new BusinessException("该商机下只有一条联系人，不能删除");
        }

        // 软删除联系人
        return businessContactService.lambdaUpdate()
                .set(BusinessContactEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(BusinessContactEntity::getId, id)
                .update();
    }

    /**
     * 获取商机的联系人列表
     */
    @AutoTranslate
    public List<BusinessContactVO> getContactsByBusinessId(Integer businessId) {
        return businessContactService.lambdaQuery()
                .eq(BusinessContactEntity::getBusinessId, businessId)
                .eq(BusinessContactEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(BusinessContactEntity::getCreateTime)
                .list()
                .stream()
                .map(BusinessContactConvert.INSTANCE::toVo)
                .collect(Collectors.toList());
    }
} 