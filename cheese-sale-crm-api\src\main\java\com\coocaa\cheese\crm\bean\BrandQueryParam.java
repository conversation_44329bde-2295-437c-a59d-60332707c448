package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 品牌管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
public class BrandQueryParam {

    @Schema(description = "品牌名称", type = "String", example = "梅赛德斯")
    private String name;

    @Schema(description = "二级行业编码", type = "String", example = "001-02")
    private String industryCode;

    @Schema(description = "持有公司名称", type = "String", example = "梅赛德斯")
    private String companyName;

    @Schema(description = "生效状态(1:生效,0:不生效)", type = "Integer", example = "1")
    private Integer effectiveStatus;

    @Schema(description = "创建人", type = "Integer", example = "0")
    private Integer creator;

    @Schema(description = "部门", type = "Integer", example = "0")
    private String departmentId;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "创建开始日期", type = "String", example = "2025-01-01")
    private LocalDate createStartDate;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "创建结束日期", type = "String", example = "2025-12-31")
    private LocalDate createEndDate;
}
