package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.BrandTagEntity;
import com.coocaa.cheese.crm.common.db.mapper.BrandTagMapper;
import com.coocaa.cheese.crm.common.db.service.IBrandTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 品牌标签信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BrandTagServiceImpl extends ServiceImpl<BrandTagMapper, BrandTagEntity> implements IBrandTagService {

}
