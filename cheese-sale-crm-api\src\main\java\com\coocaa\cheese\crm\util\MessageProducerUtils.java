package com.coocaa.cheese.crm.util;

import com.coocaa.ad.common.core.redission.MessageProducer;
import com.coocaa.cheese.crm.common.tools.enums.DelayQueueEnum;
import com.coocaa.cheese.crm.service.ConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class MessageProducerUtils {
    private final MessageProducer messageProducer;
    private final ConfigService configService;


    /**
     * 发送消息
     * 也可以重载方法，支持直接传内容
     */
    public void sendMessage(DelayQueueEnum queueEnum, String content) {
        TimeUnit timeUnit = TimeUnit.valueOf(configService.getConfig(queueEnum.getTimeUnit()).getValue());
        long time = Long.parseLong(configService.getConfig(queueEnum.getDelay()).getValue());
        messageProducer.sendMessage(queueEnum.getQueueName(), queueEnum.getMessageType(), content, timeUnit, time);
    }
}