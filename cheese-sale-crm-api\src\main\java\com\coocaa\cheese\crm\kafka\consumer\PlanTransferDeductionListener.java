package com.coocaa.cheese.crm.kafka.consumer;

import com.alibaba.fastjson2.JSONObject;
import com.coocaa.ad.common.annotation.ChainedTransactional;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.util.BigDecimalUtils;
import com.coocaa.cheese.crm.common.db.entity.InstitutionFundAccountEntity;
import com.coocaa.cheese.crm.common.db.entity.InstitutionFundChangeRecordEntity;
import com.coocaa.cheese.crm.common.db.entity.ListingFeeQuoteEntity;
import com.coocaa.cheese.crm.common.db.service.IInstitutionFundAccountService;
import com.coocaa.cheese.crm.common.db.service.IInstitutionFundChangeRecordService;
import com.coocaa.cheese.crm.common.db.service.IListingFeeQuoteService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.EvidenceBillingTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.FundChangeTypeEnum;
import com.coocaa.cheese.crm.convert.PlanTransferDeductionConvert;
import com.coocaa.cheese.crm.kafka.constant.KafkaConstants;
import com.coocaa.cheese.crm.mongo.PointFeeDocument;
import com.coocaa.cheese.crm.mongo.service.PointFeeService;
import com.coocaa.cheese.crm.rpc.FeignMonitorRpc;
import com.coocaa.cheese.crm.vo.PlanTransferDeductionPointVO;
import com.coocaa.cheese.crm.vo.PlanTransferDeductionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 方案转扣费监听器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-07
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PlanTransferDeductionListener {
    private final PointFeeService pointFeeService;
    private final IInstitutionFundChangeRecordService institutionFundChangeRecordService;
    private final IListingFeeQuoteService listingFeeQuoteService;
    private final IInstitutionFundAccountService institutionFundAccountService;
    private final FeignMonitorRpc feignMonitorRpc;
    private final PlanTransferDeductionConvert planTransferDeductionConvert;

    private static final String PASSIVE_POINT = "被动加点";
    private static final String ADJUST_POINT = "调剂点位";
    private static final String BILLING_POINT = "计费点位";

    /**
     * 方案转扣费监听器
     */
    @ChainedTransactional
    @KafkaListener(topics = KafkaConstants.TOPIC_PLAN_TRANSFER_DEDUCTION,
            groupId = KafkaConstants.ID_PLAN_TRANSFER_DEDUCTION,
            containerFactory = KafkaConstants.KAFKA_LISTENER_CONTAINER_FACTORY_ONE)
    public void getMessage(ConsumerRecord<String, String> record, Acknowledgment ack) {
        log.info("方案转扣费监听器收到消息:{}", record);
        if (Objects.isNull(record) || StringUtils.isBlank(record.value())) {
            return;
        }
        try {
            // 解析数据
            PlanTransferDeductionVO msg = JSONObject.parseObject(record.value(), PlanTransferDeductionVO.class);
            LocalDate startDate = msg.getStartDate();
            LocalDate endDate = msg.getEndDate();
            Integer planId = msg.getPlanId();
            if (Objects.isNull(planId) || Objects.isNull(startDate) || Objects.isNull(endDate)) {
                log.warn("方案冻结转扣费：方案信息缺失, 忽略处理:{}", planId);
                return;
            }

            // 根据【方案ID】+【变动原因】=刊例费 + 【变动类型】=冻结 + 【创建方式】=系统，找到对应的《机构资金变动记录》，及分城市的《刊例费账单》
            InstitutionFundChangeRecordEntity planTransferDeductionRecord = institutionFundChangeRecordService.getPlanTransferDeductionRecord(planId);
            if (null == planTransferDeductionRecord) {
                log.warn("方案冻结转扣费：未找到方案冻结记录{}", planId);
                return;
            }
            // 获取方案的刊例账单
            List<ListingFeeQuoteEntity> listingFeeQuoteList = listingFeeQuoteService.getListingFeeQuoteList(planTransferDeductionRecord.getId());
            if (CollectionUtils.isEmpty(listingFeeQuoteList)) {
                log.warn("方案冻结转扣费：未找到方案对应的账单{}", planId);
                return;
            }

            // 把刊例账单按照城市分组，每个城市一条数据  下面备用
            Map<Integer, ListingFeeQuoteEntity> byCityId = listingFeeQuoteList.stream()
                    .collect(Collectors.toMap(
                            ListingFeeQuoteEntity::getCityId,
                            listingFeeQuote -> listingFeeQuote,
                            (existing, replacement) -> existing
                    ));

            // 获取计费方式
            String billingType = listingFeeQuoteList.get(0).getBillingType();

            int coefficient;
            if (EvidenceBillingTypeEnum.WEEK.getCode().equals(billingType)) {
                coefficient = 7;
            } else {
                coefficient = 1;
            }
            // 每个城市的点位数据  用于下面每天拿数据的时候累计扣除每天的计费点位  用于计算占位费
            Map<Integer, Integer> cityOccupy = byCityId.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().getBillingPointCount() * entry.getValue().getBillingCycle() * coefficient)
                    );


            // 循环按天获取每天的该方案的点位监播数据
            for (LocalDate date = startDate; date.isBefore(endDate) || date.isEqual(endDate); date = date.plusDays(1)) {
                // feign调用 根据方案id和日期获取当日的点位数据
                List<PlanTransferDeductionPointVO> daysPointList =
                        feignMonitorRpc.getPlanMonitorPointPlayDetails(date.format(DateTimeFormatter.ISO_DATE), planId).getData();
                if (CollectionUtils.isEmpty(daysPointList)) {
                    continue;
                }
                // 处理每一天的监播数据
                List<PointFeeDocument> oneDaySaveList = dealEveryDay(daysPointList, byCityId, cityOccupy, date, billingType);
                // 每日的数据直接入库 避免内存消耗过大
                LocalDateTime now = LocalDateTime.now();
                oneDaySaveList.forEach(one -> one.setCreateTime(now));
                pointFeeService.savePointFees(oneDaySaveList);
            }
            // 如果计费方式为按周计费 则需计算占位费用 维护刊例账单
            if (EvidenceBillingTypeEnum.WEEK.getCode().equals(billingType)) {
                // 计算占位费用
                for (Integer cityId : cityOccupy.keySet()) {
                    ListingFeeQuoteEntity listingFee = byCityId.get(cityId);
                    Integer occupyPointCount = cityOccupy.get(cityId);
                    // 计算占位费用
                    BigDecimal occupyFee = BigDecimal.valueOf(occupyPointCount)
                            .multiply(listingFee.getApplicableDiscount())
                            .multiply(Constants.ZERO_POINT_ONE)
                            .multiply(listingFee.getPointUnitPrice())
                            .divide(Constants.BIG_DECIMAL_SEVEN, 2, RoundingMode.DOWN);
                    listingFee.setOccupyAmount(occupyFee);
                }
            }
            Collection<ListingFeeQuoteEntity> cityChanges = byCityId.values();
            // 累加实际消耗和占位费用 作为本次的变动费用 初始化总金额为0
            BigDecimal changeAmount = BigDecimal.ZERO;

            // 遍历每个城市的账单，累加计费金额和占位费用
            for (ListingFeeQuoteEntity listingFee : cityChanges) {
                changeAmount = changeAmount.add(listingFee.getActualAmount()).add(listingFee.getOccupyAmount());
            }
            BigDecimal freezeAmount = planTransferDeductionRecord.getChangeAmount();
            if (BigDecimalUtils.gt(changeAmount, freezeAmount)) {
                log.warn("方案冻结转扣费：变动金额超过冻结金额{}", planId);
                // 回滚上面插入mongo的数据
                throw new BusinessException("变动金额超过冻结金额");
            }
            // 维护资金变动记录
            planTransferDeductionRecord.setChangeAmount(changeAmount);
            planTransferDeductionRecord.setChangeType(FundChangeTypeEnum.DECREASE.getCode());

            // 维护资金帐户余额
            InstitutionFundAccountEntity fundAccount = institutionFundAccountService.getById(planTransferDeductionRecord.getFundAccountId());
            fundAccount.setBalance(fundAccount.getBalance().subtract(changeAmount));
            fundAccount.setTotalFrozen(fundAccount.getTotalFrozen().subtract(freezeAmount));
            fundAccount.setTotalConsumption(fundAccount.getTotalConsumption().add(changeAmount));

            listingFeeQuoteService.updateBatchById(cityChanges);
            institutionFundChangeRecordService.updateById(planTransferDeductionRecord);
            institutionFundAccountService.updateById(fundAccount);
        } catch (Exception e) {
            log.error("方案冻结转扣费：监播消息处理失败，接收消息为：{}", record.value());
        } finally {
            // 提交偏移量 防止一直执行
            ack.acknowledge();
        }
    }

    /**
     * 计算点位单价
     * 如果是按周计费  单价为周单价 / 7
     * 如果是按天计费  单价为天单价
     */
    private BigDecimal getUniPrice(String billingType, BigDecimal price) {
        // 计算点位单价
        if (EvidenceBillingTypeEnum.WEEK.getCode().equals(billingType)) {
            // 如果计费方式为按周，则每日单价为周单价 / 7
            return price.divide(Constants.BIG_DECIMAL_SEVEN, 2, RoundingMode.DOWN);
        } else {
            // 其他计费方式，直接取点位单价
            return price.setScale(2, RoundingMode.DOWN);
        }
    }

    /**
     * 计算每天的点位数据
     *
     * @param daysPointList 当天的点位数据
     * @param byCityId      城市账单列表
     * @param cityOccupy    城市占位数据
     * @param playDate      播放日期
     * @param billingType   计费方式
     * @return 每天的城市点位存储数据
     */
    private List<PointFeeDocument> dealEveryDay(List<PlanTransferDeductionPointVO> daysPointList,
                                                Map<Integer, ListingFeeQuoteEntity> byCityId,
                                                Map<Integer, Integer> cityOccupy,
                                                LocalDate playDate,
                                                String billingType) {
        List<PointFeeDocument> pointFeeDocuments = planTransferDeductionConvert.pointVoToDocument(daysPointList);
        // 点位也根据城市分组 每天计算
        Map<Integer, List<PointFeeDocument>> pointMap = pointFeeDocuments.stream().collect(Collectors.groupingBy(PointFeeDocument::getCityId));
        for (Integer cityId : pointMap.keySet()) {
            List<PointFeeDocument> pointFeeDocumentList = pointMap.get(cityId);
            if (CollectionUtils.isEmpty(pointFeeDocumentList)) {
                continue;
            }
            // 获取每个城市的刊例账单  累加每个点位每天的消耗金额
            ListingFeeQuoteEntity listingFee = byCityId.get(cityId);
            // 获取计费点位 点位单价 适用折扣
            Integer billingPointCount = listingFee.getBillingPointCount();
            BigDecimal applicableDiscount = listingFee.getApplicableDiscount();

            // 被动加点的点位标识不变  其余点位按照实际播放次数 从大到小排序 取前面（计费点位个数）用于计费   如有剩余点位  标识为调剂点位
            pointFeeDocumentList.sort(Comparator.comparing(PointFeeDocument::getPlayCount).reversed());
            // 当天计费点位的数量
            int i = 0;
            for (PointFeeDocument pointFeeDocument : pointFeeDocumentList) {
                // 被动加点不算钱 也不算计费点位
                if (pointFeeDocument.getPointMark().equals(PASSIVE_POINT)) {
                    pointFeeDocument.setActualConsumption(BigDecimal.ZERO);
                    continue;
                }
                // 如果移除时间不为空  且在当前这天以及这天之后的 计费金额为空 其他正常收费 这种可以理解成占位点位
                if (null != pointFeeDocument.getKickTime()
                        && (pointFeeDocument.getKickTime().toLocalDate().isBefore(playDate) || pointFeeDocument.getKickTime().toLocalDate().isEqual(playDate))) {
                    pointFeeDocument.setActualConsumption(null);
                    continue;
                }
                // 计费点位
                if (i < billingPointCount) {
                    // 取达成率，默认100
                    BigDecimal achievementRate = Constants.BIG_DECIMAL_ONE_HUNDRED;
                    BigDecimal onePointPrice;
                    if (EvidenceBillingTypeEnum.WEEK.getCode().equals(billingType)) {
                        // 计算点位单价 向下取整
                        // 计费方式为按周的话 每日单价为  周单价/7
                        onePointPrice = listingFee.getPointUnitPrice()
                                .multiply(achievementRate).multiply(Constants.ZERO_POINT_ZERO_ONE)
                                .multiply(applicableDiscount).multiply(Constants.ZERO_POINT_ONE)
                                .divide(Constants.BIG_DECIMAL_SEVEN, 2, RoundingMode.DOWN);
                    } else {
                        onePointPrice = listingFee.getPointUnitPrice()
                                .multiply(achievementRate).multiply(Constants.ZERO_POINT_ZERO_ONE)
                                .multiply(applicableDiscount).multiply(Constants.ZERO_POINT_ONE)
                                .setScale(2, RoundingMode.DOWN);
                    }
                    pointFeeDocument.setActualConsumption(onePointPrice);
                    pointFeeDocument.setPointMark(BILLING_POINT);
                    // 累加每天的计费金额
                    listingFee.setActualAmount(listingFee.getActualAmount().add(pointFeeDocument.getActualConsumption()));
                    i++;
                } else {
                    // 计费点位数量已经超过预期的计费点位 则设置为调剂点位 不收费
                    pointFeeDocument.setPointMark(ADJUST_POINT);
                    pointFeeDocument.setActualConsumption(BigDecimal.ZERO);
                }
            }
            // 累计扣除城市每天的计费点位
            Integer i1 = cityOccupy.get(cityId);
            cityOccupy.put(cityId, i1 - i);
        }
        return pointFeeDocuments;
    }
}
