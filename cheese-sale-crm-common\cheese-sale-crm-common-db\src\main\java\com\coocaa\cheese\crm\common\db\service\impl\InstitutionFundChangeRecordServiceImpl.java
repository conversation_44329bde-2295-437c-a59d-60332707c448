package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.db.entity.InstitutionFundChangeRecordEntity;
import com.coocaa.cheese.crm.common.db.mapper.InstitutionFundChangeRecordMapper;
import com.coocaa.cheese.crm.common.db.service.IInstitutionFundChangeRecordService;
import com.coocaa.cheese.crm.common.tools.enums.ChangeReasonEnum;
import com.coocaa.cheese.crm.common.tools.enums.ChangeTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 机构资金变动记录 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionFundChangeRecordServiceImpl
        extends ServiceImpl<InstitutionFundChangeRecordMapper, InstitutionFundChangeRecordEntity>
        implements IInstitutionFundChangeRecordService {

    @Override
    public IPage<InstitutionFundChangeRecordEntity> pageList(IPage<InstitutionFundChangeRecordEntity> page, Object condition) {
        return baseMapper.pageList(page, condition);
    }

    @Override
    public InstitutionFundChangeRecordEntity getPlanTransferDeductionRecord(Integer planId) {
        return this.lambdaQuery()
                .eq(InstitutionFundChangeRecordEntity::getPlanId, planId)
                .eq(InstitutionFundChangeRecordEntity::getChangeReason, ChangeReasonEnum.LISTING_FEE.getCode())
                .eq(InstitutionFundChangeRecordEntity::getChangeType, ChangeTypeEnum.FREEZE.getCode())
                .eq(InstitutionFundChangeRecordEntity::getCreationMethod, BooleFlagEnum.NO.getCode())
                .eq(InstitutionFundChangeRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode()).one();
    }
}