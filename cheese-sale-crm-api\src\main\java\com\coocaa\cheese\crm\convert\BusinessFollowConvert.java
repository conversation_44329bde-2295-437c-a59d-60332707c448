package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.BusinessFollowParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessFollowEntity;
import com.coocaa.cheese.crm.vo.BusinessFollowVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商机跟进记录信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BusinessFollowConvert extends PageableConvert<BusinessFollowEntity, BusinessFollowVO> {
    BusinessFollowConvert INSTANCE = Mappers.getMapper(BusinessFollowConvert.class);

    /**
     * Entity 转 VO
     */
    BusinessFollowVO toVo(BusinessFollowEntity entity);

    /**
     * VO 转 Entity
     */
    BusinessFollowEntity toEntity(BusinessFollowVO vo);

    /**
     * Param 转 Entity
     */
    BusinessFollowEntity toEntity(BusinessFollowParam param);
} 