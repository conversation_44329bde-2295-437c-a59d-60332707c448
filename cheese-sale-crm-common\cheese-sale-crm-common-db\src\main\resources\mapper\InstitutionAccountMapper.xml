<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.InstitutionAccountMapper">
    <!-- 按条件查询机构账户列表 -->
    <select id="pageList" resultType="com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity">
        SELECT ia.id, ia.account_code, ia.company_id, ia.auth_start_date, ia.auth_end_date, 
               ia.account_status, ia.disable_reason, ia.parent_id, ia.owner_id, ia.owner_name,
               ia.department_id, ia.department_name, ia.owner_department_id, ia.owner_department_name,
               ia.creator, ia.create_time, ia.operator, ia.update_time
        FROM sale_crm_institution_account ia
        <include refid="joinSql"/>
        <include refid="whereSql"/>
        ORDER BY ia.update_time DESC
    </select>

    <!-- 根据条件统计机构账户数量 -->
    <select id="pageList_COUNT" resultType="java.lang.Integer">
        SELECT count(1)
        FROM sale_crm_institution_account ia
        <include refid="joinSql"/>
        <include refid="whereSql"/>
    </select>

    <!-- 机构账户列表查询的公共部分提取出来 -->
    <sql id="joinSql">
        <if test="condition.companyName != null and condition.companyName != ''">
            LEFT JOIN sale_comm_company scc ON ia.company_id = scc.id
        </if>
    </sql>

    <sql id="whereSql">
        <where>
            AND ia.delete_flag = 0
            <if test="condition.accountCode != null and condition.accountCode != ''">
                AND ia.account_code LIKE CONCAT('%',#{condition.accountCode},'%')
            </if>
            <if test="condition.accountStatus != null and condition.accountStatus != ''">
                AND ia.account_status = #{condition.accountStatus}
            </if>
            <if test="condition.ownerId != null">
                AND ia.owner_id = #{condition.ownerId}
            </if>
            <if test="condition.ownerName != null and condition.ownerName != ''">
                AND ia.owner_name LIKE CONCAT('%',#{condition.ownerName},'%')
            </if>
            <if test="condition.companyName != null and condition.companyName != ''">
                AND scc.name LIKE CONCAT('%',#{condition.companyName},'%')
            </if>
            <if test="condition.departmentId != null and condition.departmentId != ''">
                AND ia.department_id = #{condition.departmentId}
            </if>
            <if test="condition.ownerDepartmentId != null and condition.ownerDepartmentId != ''">
                AND ia.owner_department_id = #{condition.ownerDepartmentId}
            </if>
        </where>
    </sql>
</mapper> 