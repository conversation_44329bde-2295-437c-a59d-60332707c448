package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.cheese.crm.common.db.bean.ProductLineUnionDTO;
import com.coocaa.cheese.crm.common.db.bean.SignSubjectProductLineDTO;
import com.coocaa.cheese.crm.common.db.entity.ProductLineEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品线表接口
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
public interface ProductLineMapper extends BaseMapper<ProductLineEntity> {

    /**
     * 根据品牌ID获取产品线名称列表
     */
    List<String> getProductLineNameList(@Param("brandId") Integer brandId);

    /*
     * <AUTHOR>
     * @Description 查询该签约主体绑定的产品线
     * @Date 2025/7/3
     * @Param [signSubjectId]
     * @return java.util.List<java.lang.String>
     **/
    List<String> queryProductLineBySignSubjectId(Integer signSubjectId);

    /*
     * <AUTHOR>
     * @Description 查询该品牌下的产品线
     * @Date 2025/6/20
     * @Param [brandId]
     * @return java.util.List<com.coocaa.cheese.crm.common.db.bean.ProductLineUnionDTO>
     **/
    List<ProductLineUnionDTO> queryInBrandUnionProductLine(Integer brandId);

    /*
     * <AUTHOR>
     * @Description 查询该品牌外的产品线
     * @Date 2025/6/20
     * @Param [companyIds]
     * @return java.util.List<com.coocaa.cheese.crm.common.db.bean.ProductLineUnionDTO>
     **/
    List<ProductLineUnionDTO> queryOutBrandUnionProductLine(@Param("ids") List<Integer> companyIds);

    /*
     * <AUTHOR>
     * @Description 查询该签约主体绑定的产品线数据
     * @Date 2025/6/26
     * @Param [bizIds]
     * @return java.util.List<com.coocaa.cheese.crm.common.db.bean.SignSubjectProductLineDTO>
     **/
    List<SignSubjectProductLineDTO> querySignSubjectProductLine(@Param("ids") List<Integer> bizIds);
}