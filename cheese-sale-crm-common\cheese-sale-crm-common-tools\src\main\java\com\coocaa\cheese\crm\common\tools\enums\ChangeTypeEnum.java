package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 变动类型枚举
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Getter
@AllArgsConstructor
public enum ChangeTypeEnum implements IEnumType<String> {
    /**
     * 增加
     */
    INCREASE("0111-1", "增加"),

    /**
     * 减少
     */
    DECREASE("0111-2", "减少"),

    /**
     * 冻结中
     */
    FREEZE("0111-3", "冻结中"),

    /**
     * 解冻
     */
    UNFREEZE("0111-4", "解冻");

    private final String code;
    private final String desc;

    private static final Map<String, ChangeTypeEnum> BY_CODE_MAP =
            Arrays.stream(ChangeTypeEnum.values())
                    .collect(Collectors.toMap(ChangeTypeEnum::getCode, item -> item));

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * 将代码转成枚举
     */
    public static ChangeTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code         代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static ChangeTypeEnum parse(String code, ChangeTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ChangeTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 