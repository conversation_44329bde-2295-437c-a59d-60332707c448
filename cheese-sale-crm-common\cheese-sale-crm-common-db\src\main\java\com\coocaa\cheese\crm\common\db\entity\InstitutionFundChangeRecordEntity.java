package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 机构资金变动记录表
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@TableName("sale_crm_institution_fund_change_record")
public class InstitutionFundChangeRecordEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 资金账户ID
     */
    private Integer fundAccountId;

    /**
     * 变动金额
     */
    private BigDecimal changeAmount;

    /**
     * 变动类型 (字典0111)
     */
    private String changeType;

    /**
     * 变动原因 (字典0112)
     */
    private String changeReason;

    /**
     * 变动备注
     */
    private String description;

    /**
     * 变动凭据URL
     */
    private String voucherUrl;

    /**
     * 变动调整凭据URL
     */
    private String adjustmentVoucherUrl;

    /**
     * 投放方案ID
     */
    private Integer planId;

    /**
     * 创建方式 [0:系统, 1:人工]
     */
    private Integer creationMethod;

    /**
     * 删除标记[0:未删除, 1:已删除]
     */
    private Integer deleteFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 