package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.util.AesUtils;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.BankParam;
import com.coocaa.cheese.crm.bean.BankQueryParam;
import com.coocaa.cheese.crm.common.db.entity.BankEntity;
import com.coocaa.cheese.crm.common.db.service.IBankService;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.convert.BankConvert;
import com.coocaa.cheese.crm.vo.BankVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 银行管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BankService {
    private final IBankService bankService;

    /**
     * 根据公司ID查银行列表
     *
     * @param companyId 公司ID
     * @param bankName  银行名称
     * @return 银行列表
     */
    public List<CodeNameVO> listByCompanyId(Integer companyId, String bankName) {
        Objects.requireNonNull(companyId, "公司ID不能为空");
        List<BankEntity> banks = bankService.lambdaQuery()
                .select(BankEntity::getId, BankEntity::getCompanyId, BankEntity::getName, BankEntity::getAccount)
                .eq(BankEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BankEntity::getCompanyId, companyId)
                .like(StringUtils.isNotBlank(bankName), BankEntity::getName, StringUtils.trimToNull(bankName))
                .orderByDesc(BankEntity::getUpdateTime)
                .list();
        if (CollectionUtils.isEmpty(banks)) {
            return Collections.emptyList();
        }

        return banks.stream()
                .map(bank -> CodeNameVO.builder().id(bank.getId()).name(bank.getName()).code(bank.getAccount()).build())
                .toList();
    }


    /**
     * 根据银行ID查询列表
     *
     * @param ids 银行ID列表
     * @return 银行列表
     */
    public List<CodeNameVO> listByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        Set<Integer> uniqueIds = ids.stream().filter(Objects::nonNull).filter(id -> id > 0).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(uniqueIds)) {
            return Collections.emptyList();
        }

        return bankService.lambdaQuery()
                .select(BankEntity::getId, BankEntity::getName, BankEntity::getAccount)
                .eq(BankEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(BankEntity::getId, uniqueIds)
                .list().stream()
                .map(bank -> CodeNameVO.builder().id(bank.getId()).name(bank.getName()).code(bank.getAccount()).build())
                .toList();
    }


    /**
     * 银行列表(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的银行列表
     */
    @AutoTranslate
    public PageResponseVO<BankVO> pageList(@RequestBody PageRequestVO<BankQueryParam> pageRequest) {
        IPage<BankEntity> pageBanks = bankService.pageList(getPage(pageRequest),
                BankConvert.INSTANCE.toDto(pageRequest.getQuery()));
        return BankConvert.INSTANCE.toPageResponse(pageBanks);
    }

    /**
     * 银行详情
     *
     * @param id 银行ID
     * @return 银行详情
     */
    @AutoTranslate
    public BankVO getDetail(Integer id) {
        return Optional.ofNullable(bankService.getById(id))
                .map(BankConvert.INSTANCE::toVo)
                .orElse(null);
    }

    /**
     * 银行新增或修改
     *
     * @param id    银行ID (为空表示新增)
     * @param param 银行信息
     * @return true: 析增或修改成功
     */
    public boolean createOrUpdate(Integer id, BankParam param) {
        // 检查是否已存在
        if (isExist(id, param.getAccount())) {
            throw new BusinessException("帐号与已有数据重复");
        }

        // 转换实体
        BankEntity entity = BankConvert.INSTANCE.toEntity(param);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());

        // 新增或修改
        if (Objects.isNull(id)) {
            return bankService.save(entity);
        } else {
            entity.setId(id);
            return bankService.updateById(entity);
        }
    }

    /**
     * 银行删除
     *
     * @param id 银行ID
     * @return true: 删除成功
     */
    public boolean delete(Integer id) {
        // 检查是否存在
        BankEntity entity = Optional.ofNullable(bankService.getById(id))
                .orElseThrow(() -> new BusinessException("银行不存在，删除失败"));

        return bankService.lambdaUpdate()
                .set(BankEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(BankEntity::getId, entity.getId())
                .update();
    }

    /**
     * 检查账号是否重复
     *
     * @param bankId  银行ID, 修改时排除自己
     * @param account 银行账号
     * @return true: 重复
     */
    private boolean isExist(Integer bankId, String account) {
        if (StringUtils.isBlank(account)) {
            return false;
        }

        // 检查账号重复
        return bankService.lambdaQuery()
                .eq(BankEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BankEntity::getAccount, AesUtils.encryptHex(account.trim()))
                .ne(Objects.nonNull(bankId), BankEntity::getId, bankId)
                .exists();
    }

    /**
     * 获取分页对象
     */
    private Page<BankEntity> getPage(PageRequestVO<?> pageRequest) {
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE)
        );
    }
}
