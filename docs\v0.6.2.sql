-- 联系人中增加邮箱的公司地址
ALTER TABLE `sale_crm_business_contact`
    ADD COLUMN `email` VARCHAR (300) NULL COMMENT '联调邮箱(加密存储)' AFTER `decision_influence`,
    ADD COLUMN `company_address` VARCHAR (300) NULL COMMENT '公司地址(加密存储)' AFTER `email`;

-- 创建公海配额表
CREATE TABLE `sale_comm_public_sea_quota`
(
    `id`              INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`         INT(11) UNSIGNED NOT NULL COMMENT '用户ID',
    `user_name`       VARCHAR(10) NOT NULL COMMENT '用户名称',
    `monthly_quota`   INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '每月配额',
    `daily_quota`     INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '每日配额',
    `temporary_quota` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '临时配额',
    `monthly_used`    INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '本月已用',
    `daily_used`      INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '本日已用',
    `temp_used`       INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '临额已用',
    `create_time`     DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator`         INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `operator`        INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `delete_flag`     TINYINT(1) UNSIGNED  NOT NULL DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB COMMENT ='签约主体公海配额表';

-- 品牌新增是否生效和生效时间
ALTER TABLE `sale_comm_brand`
    ADD COLUMN `effective_status` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '生效状态：0-否，1-是',
    ADD COLUMN `effective_time`   DATETIME DEFAULT NULL COMMENT '生效时间（首次从"否"变"是"时记录）',
    MODIFY COLUMN `company_id` INT(11) UNSIGNED DEFAULT '0' COMMENT '所属公司ID';

-- 主体新增是否生效、生效时间和是否公海保护区
ALTER TABLE `sale_comm_advertising_subject`
    ADD COLUMN `effective_status` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '生效状态：0-否，1-是',
    ADD COLUMN `effective_time`   DATETIME DEFAULT NULL COMMENT '生效时间（首次从"否"变"是"时记录）',
    ADD COLUMN `protection_period_flag` tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '是否公海保护期 [0:否, 1:是]';

-- 更新主体历史数据
UPDATE sale_comm_advertising_subject
SET effective_time   = create_time,
    effective_status = 1;

-- 更新品牌历史数据
UPDATE sale_comm_brand
SET effective_time   = create_time,
    effective_status = 1;

INSERT INTO sale_comm_config
    (name, parent_code, code, value, ext1, ext2, ext3, description, status)
VALUES ('延迟队列-公海主体-时间单位', 'subject', 'subject_time_unit', 'HOURS', '', '', '', '', 1);
INSERT INTO sale_comm_config
    (name, parent_code, code, value, ext1, ext2, ext3, description, status)
VALUES ('延迟队列-公海主体-时间', 'subject', 'subject_time', '48', '', '', '', '', 1);