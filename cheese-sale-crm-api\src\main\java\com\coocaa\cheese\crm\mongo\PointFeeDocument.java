package com.coocaa.cheese.crm.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 点位费用对象
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "sale_crm_point_fee")
public class PointFeeDocument {

    @Id
    private String id;

    /**
     * 方案id
     */
    private Integer planId;
    /**
     * 点位编码
     */
    private String pointCode;
    /**
     * 广告播放次数
     */
    private Integer playCount;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区县id
     */
    private Integer districtId;
    /**
     * 区县名称
     */
    private String districtName;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目地址
     */
    private String addressDetail;
    /**
     * 播放日期
     */
    private LocalDate playDate;

    /**
     * 预计播放次数
     */
    private Integer prePlayCount;
    /**
     * 达成率
     */
    private BigDecimal achievementRate;
    /**
     * 点位选择状态
     */
    private String pointSelectStatusName;
    /**
     * 点位选择时间 (时间戳)
     */
    private String selectTime;

    /**
     * 点位剔除时间
     */
    private LocalDateTime kickTime;

    /**
     * 点位标识 “被动加点”
     */
    private String pointMark;

    /**
     * 该点位当日的实际消费
     */
    private BigDecimal actualConsumption;

    private LocalDateTime createTime;
} 