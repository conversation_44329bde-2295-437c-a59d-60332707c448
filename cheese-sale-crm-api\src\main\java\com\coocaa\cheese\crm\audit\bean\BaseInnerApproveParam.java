package com.coocaa.cheese.crm.audit.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 查询站内审批任务列表
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@Schema(name = "BaseInnerApproveParam", description = "查询站内审批任务列表")
public class BaseInnerApproveParam {

    @NotNull
    @Schema(description = "待审任务:false 已审记录 true")
    private Boolean examine = Boolean.FALSE;

    @Schema(description = "申请人")
    private String instanceUserName;
}
