package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Accessors(chain = true)
@Schema(name = "InnerApproveReleaseApplyPageVO", description = "站内延期申请审批申请列表分页返回参数")
public class InnerApproveReleaseApplyPageVO {

    @Schema(description = "释放改期记录id")
    private Integer id;

    @Schema(description = "审批任务名称")
    private String approvalName;

    @Schema(description = "品牌")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY, target = "advertisingSubjectName")
    private Integer companyId;
    private String advertisingSubjectName;

    @Schema(description = "原主体释放日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate advertisingReleaseDate;

    @Schema(description = "申请延期至的日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate applyDelayDate;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime instanceCreateTime;

    @Schema(description = "完成时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime endTime;

    @Schema(description = "审批结果，字典(0138)")
    @TransField(type = TransTypes.DICT)
    private String approvalResult;
    private String approvalResultName;

    @Schema(description = "执行状态(字典0151)")
    @TransField(type = TransTypes.DICT)
    private String executeStatus;
    private String executeStatusName;

    @Schema(description = "失败原因(字典0176)")
    @TransField(type = TransTypes.DICT)
    private String failReason;
    private String failReasonName;
}
