package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.cheese.crm.common.db.entity.ProductLineSignSubjectEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品线签约主体关系表接口
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
public interface ProductLineSignSubjectMapper extends BaseMapper<ProductLineSignSubjectEntity> {

    /**
     * 获取产品线名称列表
     */
    List<String> getProductLineNameList(@Param("id") Integer id);
}