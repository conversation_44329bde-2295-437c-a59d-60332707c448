package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.BrandParam;
import com.coocaa.cheese.crm.bean.BrandReportH5Param;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandTagEntity;
import com.coocaa.cheese.crm.vo.BrandTagVO;
import com.coocaa.cheese.crm.vo.BrandVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 公品牌信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BrandConvert extends PageableConvert<BrandEntity, BrandVO> {
    BrandConvert INSTANCE = Mappers.getMapper(BrandConvert.class);

    /**
     * Entity 转 VO
     */
    BrandVO toVo(BrandEntity entity);

    /**
     * Entity 转 VO
     */
    BrandTagVO toVo(BrandTagEntity entity);

    /**
     * VO 转 Entity
     */
    BrandEntity toEntity(BrandVO vo);

    /**
     * VO 转 Entity
     */
    BrandEntity toEntity(BrandParam param);

    /**
     * BrandReportH5Param 转  BrandParam
     */
    BrandParam toParam(BrandReportH5Param param);

    /**
     * BrandReportH5Param 转  Entity
     */
    BrandEntity toEntity(BrandReportH5Param param);
}
