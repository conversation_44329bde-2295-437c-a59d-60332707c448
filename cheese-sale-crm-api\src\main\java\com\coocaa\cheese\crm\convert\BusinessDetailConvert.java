package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.vo.BusinessDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商机信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BusinessDetailConvert extends PageableConvert<BusinessEntity, BusinessDetailVO> {
    BusinessDetailConvert INSTANCE = Mappers.getMapper(BusinessDetailConvert.class);

    /**
     * Entity 转 VO
     */
    BusinessDetailVO toVo(BusinessEntity entity);
} 