package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 转移记录视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "转移记录视图对象")
public class TransferRecordVO {
    @Schema(description = "记录ID")
    private Integer id;

    @Schema(description = "业务类型")
    private String bizType;

    @Schema(description = "业务ID")
    private Integer bizId;

    @Schema(description = "来源归属人ID")
    @TransField(type = TransTypes.USER)
    private Integer sourceOwnerId;
    private String sourceOwnerName;

    @Schema(description = "来源部门ID")
    @TransField(type = TransTypes.DEPARTMENT)
    private String sourceDepartmentId;
    private String sourceDepartmentName;

    @Schema(description = "目标归属人ID")
    @TransField(type = TransTypes.USER)
    private Integer targetOwnerId;
    private String targetOwnerName;

    @Schema(description = "目标部门ID")
    @TransField(type = TransTypes.DEPARTMENT)
    private String targetDepartmentId;
    private String targetDepartmentName;

    @Schema(description = "转移原因 (0075)")
    @TransField(type = TransTypes.DICT)
    private String transferReason;
    private String transferReasonName;

    @Schema(description = "备注说明")
    private String description;

    @Schema(description = "转移时间")
    private LocalDateTime transferTime;

    @Schema(description = "操作人ID")
    @TransField(type = TransTypes.USER)
    private Integer operator;
    private String operatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
} 