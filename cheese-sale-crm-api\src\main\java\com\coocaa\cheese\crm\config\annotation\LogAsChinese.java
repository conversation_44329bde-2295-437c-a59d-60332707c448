package com.coocaa.cheese.crm.config.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 将字段名转换为中文
 *使用方法：配合@Schema注解使用,并且Schema注解的description属性不能为空，否则不生效
 * 将@Schema注解的description属性值作为字段名
 * <AUTHOR>
 * @since 2025/4/2
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogAsChinese {
}
