package com.coocaa.cheese.crm.common.tools.util;

import java.util.TreeMap;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Response;

/*
 * COS工具类
 */
@Component
public class CosUtils {

    private static String secretId;
    private static String secretKey;
    private static String bucket;
    private static String region;

    @Value("${cos.secret-id}")
    public void setSecretId(String secretId) {
        CosUtils.secretId = secretId;
    }

    @Value("${cos.secret-key}")
    public void setSecretKey(String secretKey) {
        CosUtils.secretKey = secretKey;
    }

    @Value("${cos.bucket-name}")
    public void setBucket(String bucket) {
        CosUtils.bucket = bucket;
    }

    @Value("${cos.region}")
    public void setRegion(String region) {
        CosUtils.region = region;
    }

    // 获取cos临时密钥
    public static Response getCosSts() {
        TreeMap<String, Object> config = new TreeMap<String, Object>();

        try {
			 // 云 api 密钥 SecretId
             config.put("secretId", secretId);
             // 云 api 密钥 SecretKey
             config.put("secretKey", secretKey);

            // 临时密钥有效时长，单位是秒
            config.put("durationSeconds", 1800);

            // bucket
            config.put("bucket", bucket);
            // 地域
            config.put("region", region);

            // 可以通过 allowPrefixes 指定前缀数组
            config.put("allowPrefixes", new String[] {
                    "cheese-resource",
                    "cheese-resource/*"
            });

             // 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
            String[] allowActions = new String[] {
                     // 简单上传
                    "name/cos:PutObject",
                    "name/cos:PostObject",
                    // 分片上传
                    "name/cos:InitiateMultipartUpload",
                    "name/cos:ListMultipartUploads",
                    "name/cos:ListParts",
                    "name/cos:UploadPart",
                    "name/cos:CompleteMultipartUpload"
            };
            config.put("allowActions", allowActions);

            return CosStsClient.getCredential(config);
        } catch (Exception e) {
        	e.printStackTrace();
            throw new IllegalArgumentException("no valid secret");
        }
    }
}
