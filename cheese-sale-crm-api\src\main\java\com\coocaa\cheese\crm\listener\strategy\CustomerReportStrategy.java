package com.coocaa.cheese.crm.listener.strategy;

import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandTagEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineSignSubjectEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBrandService;
import com.coocaa.cheese.crm.common.db.service.IBrandTagService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import com.coocaa.cheese.crm.common.db.service.IProductLineSignSubjectService;
import com.coocaa.cheese.crm.common.tools.enums.DelayQueueEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveOpinionTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.listener.event.InnerApproveEvent;
import com.coocaa.cheese.crm.listener.strategy.approval.ApprovalStrategy;
import com.coocaa.cheese.crm.listener.strategy.approval.StationTargetAware;
import com.coocaa.cheese.crm.service.AdvertisingSubjectService;
import com.coocaa.cheese.crm.util.MessageProducerUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 主体报备审批策略实现
 *
 * <AUTHOR>
 * @since 2025/5/7
 */

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CustomerReportStrategy implements ApprovalStrategy, StationTargetAware {

    private final IBrandService brandService;
    private final IProductLineService productLineService;
    private final IInnerApproveService innerApproveService;
    private final MessageProducerUtils messageProducerUtils;
    private final AdvertisingSubjectService advertisingSubject;
    private final IAdvertisingSubjectService advertisingSubjectService;
    private final IProductLineSignSubjectService productLineSignSubjectService;
    private final IBrandTagService brandTagService;

    @Override
    public void execute(InnerApproveEvent event) {
        log.info("主体报备审批通知信息:{}", JSONUtil.toJsonStr(event));
        if (event.getId() == null) {
            throw new BusinessException("主体id为空");
        }
        Integer advertisingSubjectId = event.getId().intValue();

        // 校验广告主体和品牌信息是否有效
        AdvertisingSubjectEntity subjectEntity = fetchAndValidateAdvertisingSubject(advertisingSubjectId);

        // 校验品牌信息是否有效
        BrandEntity brandEntity = fetchAndValidateBrand(subjectEntity.getBrandId());

        // 审批通过且已完成，更新主体和品牌的状态；审批不通过，删除数据
        if (InnerApproveOpinionTypeEnum.AGREE.equals(event.getOpinionTypeEnum())
                && (StringUtils.isNotBlank(event.getInnerTaskOperateVO().getApprovalStatus())
                && InnerApproveStatusEnum.ALREADY_FINISH.getCode().equals(event.getInnerTaskOperateVO().getApprovalStatus()))) {
            // 审批通过，更新主体和品牌的状态
            update(subjectEntity, brandEntity);
        } else if (InnerApproveOpinionTypeEnum.REJECT.equals(event.getOpinionTypeEnum())) {
            // 审批不通过，删除数据
            delete(subjectEntity, brandEntity);
        }

    }

    /**
     * 根据品牌id获取品牌信息
     *
     * @param brandId 品牌id
     * @return 品牌信息
     */
    private BrandEntity fetchAndValidateBrand(Integer brandId) {
        BrandEntity brandEntity = brandService.lambdaQuery()
                .eq(BrandEntity::getId, brandId)
                .eq(BrandEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .last("limit 1")
                .one();
        if (brandEntity == null) {
            throw new BusinessException("品牌id:" + brandId + "不存在");
        }
        return brandEntity;
    }

    /**
     * 根据主体id获取广告主体信息
     *
     * @param advertisingSubjectId 主体id
     * @return 广告主体信息
     */
    private AdvertisingSubjectEntity fetchAndValidateAdvertisingSubject(Integer advertisingSubjectId) {
        AdvertisingSubjectEntity entity = advertisingSubjectService.lambdaQuery()
                .eq(AdvertisingSubjectEntity::getId, advertisingSubjectId)
                .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .last("limit 1")
                .one();
        if (entity == null) {
            throw new BusinessException("主体id:" + advertisingSubjectId + "不存在");
        }
        return entity;
    }

    /**
     * 更新签约主体和品牌信息
     *
     * @param subjectEntity 签约主体信息
     * @param brandEntity   品牌信息
     */
    private void update(AdvertisingSubjectEntity subjectEntity, BrandEntity brandEntity) {
        LocalDateTime effectiveTime = subjectEntity.getEffectiveTime();
        // 如果签约主体生效状态为未生效并且生效状态第一次由否变成是，才更新生效时间
        if (effectiveTime == null && BooleFlagEnum.NO.getCode().equals(subjectEntity.getEffectiveStatus())) {
            subjectEntity.setEffectiveTime(LocalDateTime.now());
        }
        subjectEntity.setEffectiveStatus(BooleFlagEnum.YES.getCode());
        if (!Objects.equals(brandEntity.getEffectiveStatus(), BooleFlagEnum.YES.getCode())) {
            LocalDateTime brandEffectiveTime = brandEntity.getEffectiveTime();
            // 如果品牌生效状态为未生效并且生效状态第一次由否变成是，才更新生效时间
            if (brandEffectiveTime == null) {
                brandEntity.setEffectiveTime(LocalDateTime.now());
            }
            brandEntity.setEffectiveStatus(BooleFlagEnum.YES.getCode());
            brandService.updateById(brandEntity);
        }

        advertisingSubjectService.updateById(subjectEntity);

        //修改站内审批业务关联表
        updateInnerApprove(subjectEntity.getId(), InnerApproveOpinionTypeEnum.AGREE);

        // 发送变更消息
        advertisingSubject.sendChangeMessage("add", subjectEntity);

        // 发送延期队列消息
        messageProducerUtils.sendMessage(DelayQueueEnum.ADVERTISING_SUBJECT_QUEUE, subjectEntity.getId().toString());
    }

    /**
     * 删除签约主体和品牌信息
     *
     * @param subjectEntity 签约主体信息
     * @param brandEntity   品牌信息
     */
    private void delete(AdvertisingSubjectEntity subjectEntity, BrandEntity brandEntity) {
        // 如果签约主体生效状态为未生效，才删除数据
        if (!Objects.equals(subjectEntity.getEffectiveStatus(), BooleFlagEnum.YES.getCode())) {
            subjectEntity.setDeleteFlag(BooleFlagEnum.YES.getCode());
            advertisingSubjectService.updateById(subjectEntity);
            deleteProductLineSignSubject(subjectEntity);
        }

        // 如果品牌生效状态为未生效，才删除数据
        if (!Objects.equals(brandEntity.getEffectiveStatus(), BooleFlagEnum.YES.getCode())) {
            brandEntity.setDeleteFlag(BooleFlagEnum.YES.getCode());
            brandService.updateById(brandEntity);
            deleteProductLine(brandEntity);
            // 删除品牌别名
            brandTagService.lambdaUpdate().eq(BrandTagEntity::getBrandId, brandEntity.getId()).remove();
        }

        //修改站内审批业务关联表
        updateInnerApprove(subjectEntity.getId(), InnerApproveOpinionTypeEnum.REJECT);
    }

    private void deleteProductLineSignSubject(AdvertisingSubjectEntity subjectEntity) {

        // 若发生签约主体删除，需同步逻辑删除签约主体关联的  《产品线签约主体关系表》，若主体关联的产品线没有关联其它签约主体，则同步删除产品线
        //查询该签约主体关联的产品线ID
        List<ProductLineSignSubjectEntity> list = productLineSignSubjectService.lambdaQuery()
                .eq(ProductLineSignSubjectEntity::getSignSubjectId, subjectEntity.getId())
                .eq(ProductLineSignSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
        log.info("查询该签约主体关联的产品线ID：{}", JSONUtil.toJsonStr(list));
        //同步逻辑删除签约主体关联的  《产品线签约主体关系表》
        productLineSignSubjectService.lambdaUpdate()
                .set(ProductLineSignSubjectEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(ProductLineSignSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(ProductLineSignSubjectEntity::getSignSubjectId, subjectEntity.getId())
                .update();
        //若主体关联的产品线没有关联其它签约主体，则同步删除产品线
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> lineIdList = list.stream().map(ProductLineSignSubjectEntity::getProductLineId).distinct().toList();
            List<ProductLineSignSubjectEntity> paramList = productLineSignSubjectService.lambdaQuery()
                    .in(ProductLineSignSubjectEntity::getProductLineId, lineIdList)
                    .ne(ProductLineSignSubjectEntity::getSignSubjectId, subjectEntity.getId())
                    .list();
            if (CollectionUtils.isNotEmpty(paramList)) {
                lineIdList = new ArrayList<>(lineIdList);
                lineIdList.removeAll(paramList.stream().map(ProductLineSignSubjectEntity::getProductLineId).toList());
            }
            log.info("该产品线每天关联其他签约主体：{}", JSONUtil.toJsonStr(lineIdList));
            productLineService.lambdaUpdate()
                    .set(ProductLineEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                    .in(ProductLineEntity::getId, lineIdList)
                    .update();
        }
    }

    private void updateInnerApprove(Integer id, InnerApproveOpinionTypeEnum innerApproveOpinionTypeEnum) {

        innerApproveService.lambdaUpdate()
                .set(InnerApproveEntity::getResult, innerApproveOpinionTypeEnum.getCode())
                .set(InnerApproveEntity::getCloseTime, LocalDateTime.now())
                .eq(InnerApproveEntity::getBizId, id.longValue())
                .update();
    }

    private void deleteProductLine(BrandEntity brandEntity) {

        //若发生品牌删除，需同步逻辑删除品牌关联的《产品线数据表》
        productLineService.lambdaUpdate()
                .set(ProductLineEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(ProductLineEntity::getBrandId, brandEntity.getId())
                .update();
    }

    @Override
    public StationTargetEnum getTargetEnum() {
        return StationTargetEnum.CUSTOMER_REPORT;
    }
}
