package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.mapper.InnerApproveMapper;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 站内审批业务关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InnerApproveServiceImpl extends ServiceImpl<InnerApproveMapper, InnerApproveEntity> implements IInnerApproveService {

    @Override
    public String queryInstanceCodeById(Long id, String bizType) {
        return getBaseMapper().queryInstanceCodeById(id, bizType);
    }

    @Override
    public List<InnerApproveEntity> queryBizId(Set<String> codes, String bizType) {
        return getBaseMapper().queryBizId(codes, bizType);
    }
}
