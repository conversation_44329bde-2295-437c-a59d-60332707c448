<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.ReleaseRescheduleMapper">

    <select id="queryInnerApproveDetail" resultType="com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveDetailDTO">
        SELECT
          scrr.id,
          scb.brand_id,
          scas.company_id,
          scrr.assign_way,
          scb.channel_id,
          scrr.assign_time,
          scrr.advertising_release_date,
          scrr.apply_delay_date,
          scrr.progress,
          scrr.remark,
          scrr.execute_status,
          scrr.fail_reason
         FROM sale_crm_release_reschedule scrr
         LEFT JOIN sale_crm_business scb ON scrr.business_id = scb.id AND scb.delete_flag = '0'
         LEFT JOIN sale_comm_advertising_subject scas ON scb.advertising_subject_id = scas.id AND scas.delete_flag = 0
        WHERE scrr.id = #{id}
          AND scrr.delete_flag = '0'
    </select>

    <select id="queryInnerBusiness" resultType="com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveTaskPageDTO">
        SELECT
          scrr.id,
          scrr.advertising_release_date,
          scrr.apply_delay_date,
          scb.brand_id,
          scas.company_id,
          scrr.execute_status,
          scrr.fail_reason
         FROM sale_crm_release_reschedule scrr
         LEFT JOIN sale_crm_business scb ON scrr.business_id = scb.id
         LEFT JOIN sale_comm_advertising_subject scas ON scb.advertising_subject_id = scas.id
        WHERE scrr.id IN
        <foreach collection="bizIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
          AND scrr.delete_flag = 0
    </select>

    <select id="queryReleaseHistory" resultType="com.coocaa.cheese.crm.common.db.bean.InnerReleaseHistoryDTO">
        SELECT
          scrr.create_time,
          scia.creator,
          scia.department_id,
          scb.owner_id  AS businessOwnerId,
          scb.department_id  AS businessDepartmentId,
          scrr.advertising_release_date,
          scrr.apply_delay_date,
          scrr.judge_user_id,
          scrr.status_change_time
          FROM sale_crm_release_reschedule scrr
          LEFT JOIN sale_crm_inner_approval scia ON scrr.id = scia.biz_id AND scia.type = '0145-2'
          LEFT JOIN sale_crm_business scb ON scrr.business_id = scb.id
         WHERE scrr.id != #{id}
           AND scrr.business_id = #{businessId}
           AND scrr.execute_status = '0151-2'
           AND scrr.status_change_time &lt;= #{createTime}
           AND scrr.delete_flag = 0
         ORDER BY scrr.status_change_time DESC
    </select>
</mapper>