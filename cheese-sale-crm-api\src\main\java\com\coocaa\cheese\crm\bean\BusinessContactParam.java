package com.coocaa.cheese.crm.bean;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.EncryptDeserializer;
import com.coocaa.ad.common.validation.EnumType;
import com.coocaa.cheese.crm.common.tools.enums.DecisionAttitudeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 商机联系人管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
@Schema(name = "BusinessContactParam", description = "商机联系人参数")
public class BusinessContactParam {

    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "商机ID", type = "Integer", example = "1")
    private Integer businessId;

    @NotBlank(message = "姓名不能为空")
    @Size(max = 20, message = "姓名不能超过20字")
    @Schema(description = "姓名", type = "String", example = "张三", maxLength = 20)
    private String name;

    @NotBlank(message = "电话不能为空")
    @Size(max = 50, message = "电话不能超过50字")
    @Schema(description = "电话(加密存储)", type = "String", example = "138****8000", maxLength = 50)
    @JSONField(deserializeUsing = EncryptDeserializer.class)
    private String mobile;

    @NotNull(message = "性别不能为空")
    @Schema(description = "性别：[1-男;2-女]", type = "Integer", example = "1")
    private Integer gender;

    @Size(max = 20, message = "部门不能超过20字")
    @Schema(description = "部门", type = "String", example = "销售部", maxLength = 20)
    private String department;

    @NotBlank(message = "职务不能为空")
    @Size(max = 20, message = "职务不能超过20字")
    @Schema(description = "职务", type = "String", example = "销售经理", maxLength = 20)
    private String position;

    @NotBlank(message = "决策态度不能为空")
    @Schema(description = "决策态度(字典0070)", type = "String", example = "0070-1")
    @EnumType(message = "决策态度不正确", value = DecisionAttitudeEnum.class)
    private String decisionAttitude;

    @NotNull(message = "决策影响力不能为空")
    @Schema(description = "决策影响力", type = "Integer", example = "3")
    private Integer decisionInfluence;

    @NotBlank(message = "邮箱不能为空")
    @Schema(description = "邮箱(加密传输)", type = "String", example = "<EMAIL>")
    @JSONField(deserializeUsing = EncryptDeserializer.class)
    private String email;

    @NotBlank(message = "公司地址不能为空")
    @Size(max = 200, message = "公司地址不能超过200字")
    @Schema(description = "公司地址(加密存储)", type = "String", example = "广东省深圳市", maxLength = 200)
    private String companyAddress;

    @Size(max = 4, message = "生日月日不能超过4字")
    @Schema(description = "生日(月日)", type = "String", example = "0619", maxLength = 4)
    private String birthdayMonthDay;

    @Size(max = 4, message = "生日(年)不能超过4字")
    @Schema(description = "生日(年)", type = "Integer", example = "1990", maxLength = 4)
    private String birthdayYear;

    @Schema(description = "年龄段(字典0071)", type = "String", example = "0071-1")
    private String ageGroup;

    @Size(max = 200, message = "收件地址不能超过200字")
    @Schema(description = "收件地址(加密存储)", type = "String", example = "广东省深圳市", maxLength = 200)
    private String address;

    @Size(max = 40, message = "兴趣爱好不能超过40字")
    @Schema(description = "兴趣爱好", type = "String", example = "打高尔夫", maxLength = 40)
    private String hobbies;

    @Size(max = 40, message = "教育信息不能超过40字")
    @Schema(description = "教育信息", type = "String", example = "本科", maxLength = 40)
    private String education;

    @Size(max = 40, message = "家庭信息不能超过40字")
    @Schema(description = "家庭信息", type = "String", example = "已婚", maxLength = 40)
    private String familyInfo;

    @Size(max = 40, message = "人际信息不能超过40字")
    @Schema(description = "人际信息", type = "String", example = "社交活跃", maxLength = 40)
    private String socialInfo;

    @Size(max = 40, message = "事业信息不能超过40字")
    @Schema(description = "事业信息", type = "String", example = "创业者", maxLength = 40)
    private String careerInfo;

    @Size(max = 40, message = "生活信息不能超过40字")
    @Schema(description = "生活信息", type = "String", example = "品质生活", maxLength = 40)
    private String lifeInfo;

    @Size(max = 40, message = "信用价值观信息不能超过40字")
    @Schema(description = "信用价值观信息", type = "String", example = "诚信为本", maxLength = 40)
    private String creditValues;

    @Size(max = 200, message = "名片文件地址不能超过200字")
    @Schema(description = "名片文件地址", type = "String", example = "http://xxx.com/card.jpg", maxLength = 200)
    private String businessCardUrl;

    @Schema(description = "删除标记 [0:否, 1:是]", type = "Integer", example = "0", hidden = true)
    private Integer deleteFlag;
}
