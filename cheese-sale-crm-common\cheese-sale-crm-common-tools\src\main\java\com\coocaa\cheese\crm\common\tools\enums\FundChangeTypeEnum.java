package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售-资金变动类型枚举(字典0111)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-28
 */
@Getter
@AllArgsConstructor
public enum FundChangeTypeEnum implements IEnumType<String> {
    INCREASE("0111-1", "增"),
    DECREASE("0111-2", "减"),
    FREEZE("0111-3", "冻结中"),
    UNFREEZE("0111-4", "解冻");

    private final String code;
    private final String desc;

    private static final Map<String, FundChangeTypeEnum> BY_CODE_MAP =
            Arrays.stream(FundChangeTypeEnum.values())
                    .collect(Collectors.toMap(FundChangeTypeEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static FundChangeTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code         代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static FundChangeTypeEnum parse(String code, FundChangeTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(FundChangeTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 