package com.coocaa.cheese.crm.bean;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.EncryptDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 公司管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
public class CompanyParam {
    @NotBlank(message = "公司名称不能为空")
    @Size(max = 40, message = "公司名称不能超过{max}个字符")
    @Schema(description = "公司名称", type = "String", example = "创维集团")
    private String name;

    @NotBlank(message = "统一社会信用代码不能为空")
    @Schema(description = "统一社会信用代码", type = "String", example = "加密传输")
    @JSONField(deserializeUsing = EncryptDeserializer.class)
    private String code;

    @Schema(description = "营业执照附件地址", type = "String", example = "https://www.baidu.com/img/bd_logo1.png")
    private String licenseUrl;

    @Schema(description = "删除标记 [0:否, 1:是]", type = "Integer", example = "1", hidden = true)
    private Integer deleteFlag;

    @Schema(description = "公司名称精确查找", type = "String", example = "华为")
    private String searchName;
}
