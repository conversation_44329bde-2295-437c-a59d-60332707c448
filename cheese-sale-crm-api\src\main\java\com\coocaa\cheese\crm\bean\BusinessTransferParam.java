package com.coocaa.cheese.crm.bean;

import com.coocaa.ad.common.validation.EnumType;
import com.coocaa.cheese.crm.common.tools.enums.TransferReasonEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 商机转移
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
@Schema(name = "BusinessTransferParam", description = "商机转移参数")
public class BusinessTransferParam {

    @NotEmpty(message = "商机ID列表不能为空")
    @Schema(description = "商机ID列表", type = "List<Integer>", example = "[1,2,3]")
    private List<Integer> businessIds;

    @NotNull(message = "目标归属人不能为空")
    @Schema(description = "目标归属人", type = "Integer", example = "1")
    private Integer targetOwnerId;

    @Schema(description = "目标归属人名称", type = "Integer", example = "1")
    private String targetOwnerName;

    @Schema(description = "目标管理部门", type = "Integer", example = "1")
    private String targetDepartmentId;

    @Schema(description = "目标管理部门名称", type = "Integer", example = "1")
    private String targetDepartmentName;

    @NotBlank(message = "转移原因不能为空")
    @Schema(description = "转移原因(字典0075)", type = "String", example = "0075-1")
    @EnumType(message = "转移原因不正确", value = TransferReasonEnum.class)
    private String transferReason;

    @Size(max = 200, message = "备注说明不能超过200字")
    @Schema(description = "备注说明", type = "String", example = "工作调动", maxLength = 200)
    private String description;
} 