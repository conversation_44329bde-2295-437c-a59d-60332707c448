package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.BusinessProtectionParam;
import com.coocaa.cheese.crm.bean.BusinessProtectionQueryParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessProtectionEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessProtectionService;
import com.coocaa.cheese.crm.common.tools.constant.BusinessConstants;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessProgressEnum;
import com.coocaa.cheese.crm.common.tools.enums.ControlTargetTypeEnum;
import com.coocaa.cheese.crm.convert.BusinessProtectionConvert;
import com.coocaa.cheese.crm.vo.BusinessProtectionVO;
import com.coocaa.cheese.crm.vo.ConfigVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 商机保护期配置服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessProtectionService {
    private final IBusinessProtectionService businessProtectionService;
    private final ConfigService configService;

    /**
     * 商机保护期配置列表(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的商机保护期配置列表
     */
    @AutoTranslate
    public PageResponseVO<BusinessProtectionVO> pageList(@RequestBody PageRequestVO<BusinessProtectionQueryParam> pageRequest) {
        String targetType = pageRequest.getQuery().getTargetType();
        String targetId = pageRequest.getQuery().getTargetId();

        LambdaQueryWrapper<BusinessProtectionEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByDesc(BusinessProtectionEntity::getUpdateTime);
        queryWrapper.eq(BusinessProtectionEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());
        queryWrapper.eq(StringUtils.isNotBlank(targetType), BusinessProtectionEntity::getTargetType, targetType);
        queryWrapper.eq(StringUtils.isNotBlank(targetId), BusinessProtectionEntity::getTargetId, targetId);
        return BusinessProtectionConvert.INSTANCE.toPageResponse(
                businessProtectionService.page(getPage(pageRequest), queryWrapper));
    }

    /**
     * 商机保护期配置详情
     *
     * @param id 商机保护期配置ID
     * @return 商机保护期配置详情
     */
    public BusinessProtectionVO getDetail(Integer id) {
        return Optional.ofNullable(businessProtectionService.getById(id))
                .map(BusinessProtectionConvert.INSTANCE::toVo).orElse(null);
    }

    /**
     * 商机保护期配置新增或修改
     *
     * @param id    商机保护期配置ID (为空表示新增)
     * @param param 商机保护期配置信息
     * @return true: 析增或修改成功
     */
    public boolean createOrUpdate(Integer id, BusinessProtectionParam param) {
        // 检查是否已存在
        if (isExist(id, param)) {
            throw new BusinessException("该控制对象已存在配置，无需重复提交");
        }

        // 转换实体
        BusinessProtectionEntity entity = BusinessProtectionConvert.INSTANCE.toEntity(param);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());

        // 新增或修改
        if (Objects.isNull(id)) {
            return businessProtectionService.save(entity);
        } else {
            entity.setId(id);
            return businessProtectionService.updateById(entity);
        }
    }

    /**
     * 商机保护期配置删除
     *
     * @param id 商机保护期配置ID
     * @return true: 删除成功
     */
    public boolean delete(Integer id) {
        // 检查商机保护期配置是否存在
        if (!businessProtectionService.lambdaQuery().eq(BusinessProtectionEntity::getId, id).exists()) {
            throw new BusinessException("商机保护期配置不存在，删除失败");
        }

        return businessProtectionService.lambdaUpdate()
                .set(BusinessProtectionEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(BusinessProtectionEntity::getId, id)
                .update();
    }

    /**
     * 商机保护期配置是否重复
     *
     * @param id    商机保护期配置ID, 修改时排除自己
     * @param param 投放参数
     * @return true: 重复
     */
    private boolean isExist(Integer id, BusinessProtectionParam param) {
        if (Objects.isNull(param.getTargetType()) && StringUtils.isNotBlank(param.getTargetId())) {
            return false;
        }

        LambdaQueryWrapper<BusinessProtectionEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BusinessProtectionEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());
        queryWrapper.ne(Objects.nonNull(id), BusinessProtectionEntity::getId, id);
        queryWrapper.eq(Objects.nonNull(param.getTargetId()), BusinessProtectionEntity::getTargetId, param.getTargetId());
        queryWrapper.eq(Objects.nonNull(param.getTargetType()), BusinessProtectionEntity::getTargetType, param.getTargetType());
        return businessProtectionService.exists(queryWrapper);
    }

    /**
     * 获取分页对象
     */
    private Page<BusinessProtectionEntity> getPage(PageRequestVO<?> pageRequest) {
        // 分页查询列表，自定义统计SQL
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));
    }

    /**
     * 获取保护期天数
     * 根据品牌/部门/渠道匹配保护期规则,取最小天数,若无匹配则返回10天
     *
     * @param brandId      品牌ID
     * @param departmentId 部门ID
     * @param channelId    渠道ID
     * @param progress     商机阶段
     * @return 保护期天数
     */
    public Integer getProtectDays(Integer brandId, String departmentId, Integer channelId, String progress) {
        ConfigVO configVO = configService.getConfig(BusinessConstants.BUSINESS_PROTECTION_DAY_KEY);
        // 构建查询条件
        LambdaQueryWrapper<BusinessProtectionEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BusinessProtectionEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());

        // 构建OR条件组合
        queryWrapper.and(wrapper -> {
            // 品牌规则
            if (brandId != null) {
                wrapper.or(w -> w
                        .eq(BusinessProtectionEntity::getTargetId, brandId.toString())
                        .eq(BusinessProtectionEntity::getTargetType, ControlTargetTypeEnum.BY_BRAND.getCode())
                );
            }
            // 部门规则
            if (StringUtils.isNotBlank(departmentId)) {
                wrapper.or(w -> w
                        .eq(BusinessProtectionEntity::getTargetId, departmentId)
                        .eq(BusinessProtectionEntity::getTargetType, ControlTargetTypeEnum.BY_DEPARTMENT.getCode())
                );
            }
            // 渠道规则
            if (channelId != null) {
                wrapper.or(w -> w
                        .eq(BusinessProtectionEntity::getTargetId, channelId.toString())
                        .eq(BusinessProtectionEntity::getTargetType, ControlTargetTypeEnum.BY_CHANNEL.getCode())
                );
            }
        });

        // 4. 如果没有匹配的规则,返回默认值
        List<BusinessProtectionEntity> rules = businessProtectionService.list(queryWrapper);
        // 如果没有命中规则
        if (rules.isEmpty()) {
            // 并且是初始，则返回7天
            BusinessProgressEnum stage = BusinessProgressEnum.parse(progress);
            if (stage == null) {
                return null;
            }
            return switch (stage) {
                case INITIAL -> Integer.parseInt(configVO.getValue());
                case FOLLOWING -> Integer.parseInt(configVO.getExt1());
                case CONTRACT_PUSHING -> Integer.parseInt(configVO.getExt2());
                case SIGNED -> Integer.parseInt(configVO.getExt3());
            };
        }
        // 5. 根据阶段获取对应的保护期天数
        return rules.stream()
                .map(rule -> getStageProtectDays(rule, progress))
                .filter(Objects::nonNull)
                .min(Integer::compareTo).orElse(BigDecimal.ZERO.intValue());
    }

    /**
     * 获取规则中对应阶段的保护期天数
     */
    private Integer getStageProtectDays(BusinessProtectionEntity rule, String progress) {
        if (progress == null) {
            return null;
        }
        BusinessProgressEnum stage = BusinessProgressEnum.parse(progress);
        if (stage == null) {
            return null;
        }
        return switch (stage) {
            case INITIAL -> rule.getBindAdvertisingSubject();
            case FOLLOWING -> rule.getEstablishConnection();
            case CONTRACT_PUSHING -> rule.getApplyContract();
            case SIGNED -> rule.getSignContract();
        };
    }
}
