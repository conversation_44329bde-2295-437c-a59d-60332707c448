package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 应用类型(字典0125)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-26
 */
@Getter
@AllArgsConstructor
public enum MessageModuleEnum implements IEnumType<String> {

    CUSTOMER_SEA("0125-1", "客户公海"),
    BUSINESS_RELEASE_RESCHEDULE("0125-2", "商机延期申请"),
    BRAND_ADVERTISING_REPORT("0125-3", "品牌\\投放主体报备");

    private final String code;
    private final String desc;

    private static final Map<String, MessageModuleEnum> BY_CODE_MAP =
            Arrays.stream(MessageModuleEnum.values())
                    .collect(Collectors.toMap(MessageModuleEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static MessageModuleEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static MessageModuleEnum parse(String code, MessageModuleEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(MessageModuleEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 