package com.coocaa.cheese.crm.controller.base;

import com.alibaba.excel.util.StringUtils;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.bean.CompanyParam;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.service.CompanyService;
import com.coocaa.cheese.crm.vo.CompanyVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 企业(公司)管理
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@RestController
@RequestMapping("/companies")
@Tag(name = "公司/企业管理", description = "公司/企业管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CompanyController extends BaseController {
    private final CompanyService companyService;

    /**
     * 企业列表(分页)
     */
    @Operation(summary = "企业列表(分页)")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<CompanyVO>> pageList(@RequestBody PageRequestVO<CompanyParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(CompanyParam::new));
        return ResultTemplate.success(companyService.pageList(pageRequest));
    }

    /**
     * 企业下拉列表
     */
    @Operation(summary = "企业下拉列表")
    @GetMapping("/dropdown")
    public ResultTemplate<List<CodeNameVO>> listCompanies(@RequestParam(name = "name", required = false) String name) {
        return ResultTemplate.success(companyService.listCompanies(name));
    }

    /**
     * 根据ID批量查询列表
     */
    @Operation(summary = "根据ID批量查询列表")
    @PostMapping("/list/ids")
    public ResultTemplate<List<CodeNameVO>> listByIds(@RequestBody List<Integer> ids) {
        return ResultTemplate.success(companyService.listByIds(ids));
    }

    /**
     * 天眼查公司
     */
    @Operation(summary = "天眼查公司")
    @GetMapping("/tianyancha")
    public ResultTemplate<List<CodeNameVO>> listTianyanchaCompanies(
            @RequestParam(name = "name") String name,
            @RequestParam(name = "pageSize", required = false, defaultValue = "20") Integer pageSize) {
        return ResultTemplate.success(companyService.listTianyanchaCompanies(name, 1, pageSize));
    }

    /**
     * 企业详情
     */
    @Operation(summary = "企业详情")
    @Parameter(name = "id", description = "企业ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<CompanyVO> getDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(companyService.getDetail(id));
    }

    /**
     * 企业创建
     */
    @Operation(summary = "创建企业")
    @PostMapping
    public ResultTemplate<Boolean> create(@RequestBody @Validated CompanyParam company) {
        return ResultTemplate.success(companyService.createOrUpdate(null, company));
    }

    /**
     * 企业修改
     */
    @Operation(summary = "企业修改")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> update(@PathVariable("id") Integer id, @RequestBody CompanyParam company) {
        if (StringUtils.isBlank(company.getName()) && StringUtils.isBlank(company.getCode())) {
            return ResultTemplate.fail("企业名称、统一社会信用代码不能同时为空");
        }
        return ResultTemplate.success(companyService.createOrUpdate(id, company));
    }

    /**
     * 企业删除
     */
    @Operation(summary = "企业删除")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> delete(@PathVariable("id") Integer id) {
        return ResultTemplate.success(companyService.delete(id));
    }
}
