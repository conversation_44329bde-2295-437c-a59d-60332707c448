package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.bean.ProductLineH5Param;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineSignSubjectEntity;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import com.coocaa.cheese.crm.common.db.service.IProductLineSignSubjectService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 产品线表 业务实现类
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ProductLineService {

    private final IProductLineService productLineService;
    private final IProductLineSignSubjectService productLineSignSubjectService;


    /**
     * 批量获取签约主体所绑定的产品线名称
     */
    public Map<Integer, String> getProductLineBySubjectId(List<Integer> subjectIds) {
        if (CollectionUtils.isEmpty(subjectIds)) {
            return Map.of();
        }
        subjectIds = subjectIds.stream().distinct().toList();
        // 查询所有签约主体关联的产品线记录
        List<ProductLineSignSubjectEntity> signSubjectEntities = productLineSignSubjectService.lambdaQuery()
                .in(ProductLineSignSubjectEntity::getSignSubjectId, subjectIds)
                .list();

        if (CollectionUtils.isEmpty(signSubjectEntities)) {
            return subjectIds.stream().collect(Collectors.toMap(id -> id, id -> StringUtils.EMPTY));
        }

        // 按签约主体ID分组，获取每个主体对应的产品线ID集合
        Map<Integer, List<Long>> subjectToLineIds = signSubjectEntities.stream()
                .collect(Collectors.groupingBy(
                        ProductLineSignSubjectEntity::getSignSubjectId,
                        Collectors.mapping(ProductLineSignSubjectEntity::getProductLineId, Collectors.toList())
                ));

        // 提取所有不同的产品线ID
        Set<Long> allLineIds = subjectToLineIds.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());

        // 查询所有产品线名称
        List<ProductLineEntity> productLineEntities = productLineService.listByIds(allLineIds);
        Map<Long, String> lineIdToName = productLineEntities.stream()
                .collect(Collectors.toMap(ProductLineEntity::getId, ProductLineEntity::getName));

        // 构建最终结果：主体ID -> 产品线名称字符串
        return subjectToLineIds.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(lineIdToName::get)
                                .filter(name -> name != null && !name.isEmpty())
                                .distinct()
                                .collect(Collectors.joining(Constants.COMMA))
                ));
    }

    /**
     * 获取签约主体所绑定的产品线名称
     */
    public String getProductLineBySubjectId(Integer subjectId) {
        // 获取该签约主体绑定的所有产品线ID
        List<Long> lineIds = productLineSignSubjectService.lambdaQuery()
                .eq(ProductLineSignSubjectEntity::getSignSubjectId, subjectId)
                .list().stream()
                .map(ProductLineSignSubjectEntity::getProductLineId)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(lineIds)) {
            return StringUtils.EMPTY;
        }
        // 查询产品线名称
        List<String> lineNames = productLineService.listByIds(lineIds).stream()
                .map(ProductLineEntity::getName).toList();
        return String.join(Constants.COMMA, lineNames);
    }

    /**
     * 获取品牌所绑定的产品线名称
     */
    public String getProductLineByBrandId(Integer brandId) {
        // 获取该签约主体绑定的所有产品线ID
        List<String> lineNames = productLineService.lambdaQuery()
                .eq(ProductLineEntity::getBrandId, brandId)
                .list().stream()
                .map(ProductLineEntity::getName)
                .distinct()
                .toList();
        return String.join(Constants.COMMA, lineNames);
    }

    /**
     * 创建产品线
     */
    @Transactional(rollbackFor = Exception.class)
    public void reportSaveProductLine(ProductLineH5Param productLineH5Param, AdvertisingSubjectEntity advertisingSubjectEntity) {

        log.info("创建产品线param:{}, entity:{}", JSONUtil.toJsonStr(productLineH5Param), JSONUtil.toJsonStr(advertisingSubjectEntity));
        List<String> newNames = productLineH5Param.getNameStr();
        List<String> oldNames = productLineH5Param.getNameStr();
        //查询该品牌下已有的产品线
        List<ProductLineEntity> productLineEntities = productLineService.lambdaQuery()
                .eq(ProductLineEntity::getBrandId, advertisingSubjectEntity.getBrandId())
                .list();
        log.info("查询该品牌下已有的产品线:{}", JSONUtil.toJsonStr(productLineEntities));
        if (CollectionUtils.isNotEmpty(productLineEntities)) {
            // 获取已有产品线名称集合
            Set<String> existingNames = productLineEntities.stream()
                    .map(ProductLineEntity::getName)
                    .collect(Collectors.toSet());
            // 获取参数中的产品线名称列表
            List<String> inputNames = productLineH5Param.getNameStr();
            // 找出不重复的元素（即输入中存在但数据库不存在的名称）
            newNames = inputNames.stream()
                    .filter(name -> !existingNames.contains(name))
                    .distinct()
                    .collect(Collectors.toList());
        }
        log.info("新增的产品线名称:{}", JSONUtil.toJsonStr(newNames));
        if (CollectionUtil.isNotEmpty(newNames)) {
            List<ProductLineEntity> entitiesToSave = newNames.stream()
                    .map(name -> {
                        ProductLineEntity entity = new ProductLineEntity();
                        entity.setName(name);
                        entity.setBrandId(advertisingSubjectEntity.getBrandId());
                        return entity;
                    })
                    .collect(Collectors.toList());

            boolean saveProductLine = productLineService.saveBatch(entitiesToSave);
            //继续保存产品线签约主体关系表
            List<ProductLineSignSubjectEntity> productLineSignSubjectEntities = entitiesToSave.stream()
                    .map(entity -> {
                        ProductLineSignSubjectEntity signSubjectEntity = new ProductLineSignSubjectEntity();
                        signSubjectEntity.setProductLineId(entity.getId());
                        signSubjectEntity.setSignSubjectId(advertisingSubjectEntity.getId());
                        return signSubjectEntity;
                    }).collect(Collectors.toList());
            boolean saveProductLineSignSubject = productLineSignSubjectService.saveBatch(productLineSignSubjectEntities);
            if (!saveProductLine || !saveProductLineSignSubject) {
                throw new BusinessException("保存产品线失败");
            }
        }
        //重复的产品线也需要保存进产品线签约主体关系表
        oldNames.removeAll(newNames);
        log.info("重复的产品线名称:{}", JSONUtil.toJsonStr(oldNames));
        if (CollectionUtil.isNotEmpty(oldNames)) {
            List<ProductLineEntity> productLineRepeatEntities = productLineService.lambdaQuery()
                    .eq(ProductLineEntity::getBrandId, advertisingSubjectEntity.getBrandId())
                    .in(ProductLineEntity::getName, oldNames)
                    .list();
            List<ProductLineSignSubjectEntity> productLineSignSubjectRepeatEntities = productLineRepeatEntities.stream()
                    .map(entity -> {
                        ProductLineSignSubjectEntity signSubjectEntity = new ProductLineSignSubjectEntity();
                        signSubjectEntity.setProductLineId(entity.getId());
                        signSubjectEntity.setSignSubjectId(advertisingSubjectEntity.getId());
                        return signSubjectEntity;
                    }).collect(Collectors.toList());
            boolean success = productLineSignSubjectService.saveBatch(productLineSignSubjectRepeatEntities);
            if (!success) {
                throw new BusinessException("保存产品线失败");
            }
        }
    }
}