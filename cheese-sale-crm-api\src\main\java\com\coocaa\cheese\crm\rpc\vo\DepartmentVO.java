package com.coocaa.cheese.crm.rpc.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 部门信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-28
 */
@Data
public class DepartmentVO {
    /**
     * 部门ID
     */
    private Integer id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 飞书部门ID
     */
    @JsonProperty("openDepartmentId")
    private String departmentId;

    /**
     * 父部门ID
     */
    private String parentDepartmentId;

    /**
     * 是否被删除 [true:是, false:否]
     */
    @JsonProperty("status")
    private Boolean deleted;

    /**
     * 用户列表
     */
    private List<UserVO> userList;
}
