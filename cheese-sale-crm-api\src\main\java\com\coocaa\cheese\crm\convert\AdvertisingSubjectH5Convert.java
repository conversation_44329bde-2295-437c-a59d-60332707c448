package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.AdvertisingSubjectQueryH5Param;
import com.coocaa.cheese.crm.common.db.bean.AdvertisingSubjectQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.vo.AdvertisingSubjectH5VO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 签约主体H5信息转换
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Mapper
public interface AdvertisingSubjectH5Convert extends PageableConvert<AdvertisingSubjectEntity, AdvertisingSubjectH5VO> {
    AdvertisingSubjectH5Convert INSTANCE = Mappers.getMapper(AdvertisingSubjectH5Convert.class);

    /**
     * Entity 转 VO
     */
    AdvertisingSubjectH5VO toVo(AdvertisingSubjectEntity entity);

    /**
     * Entity 转 VO
     */
    AdvertisingSubjectQueryDTO toDto(AdvertisingSubjectQueryH5Param entity);
} 