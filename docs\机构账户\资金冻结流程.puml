@startuml
title 外部系统资金冻结流程

actor "外部系统(售卖平台)" as ExternalSystem
participant "外部接口\n/api/external-project-fund/*" as ExternalApiController
participant "资金账户管理" as AccountFundController
participant "折扣管理" as DiscountRuleController
participant "资金账户Service" as FundService
database "机构账户表\ninstitution_account" as InstitutionAccountDB
database "机构资金账户表\ninstitution_fund_account" as FundAccountDB
database "机构资金变动记录表\ninstitution_fund_change_record" as ChangeRecordDB
database "刊例费报价表\nlisting_fee_quote" as ListingFeeDB

ExternalSystem -> ExternalApiController: POST /api/external-project-fund/freeze
note right
请求参数：
- 方案ID
- 方案详情
- 方案名称
- 客户名称
- 机构账户ID
end note

ExternalApiController -> AccountFundController: 调用内部冻结接口

AccountFundController -> InstitutionAccountDB: 检查机构账户状态
note right: 查询账户状态是否为"停用"
InstitutionAccountDB --> AccountFundController: 返回账户状态

alt 账户状态为"停用"
    AccountFundController --> ExternalApiController: 返回错误信息"该机构帐户已停用"
    ExternalApiController --> ExternalSystem: 返回冻结失败结果
else 账户状态正常
    AccountFundController -> FundService: 分城市计算刊例费折前总价
    note right: 计算规则同销售工作台

    AccountFundController -> FundAccountDB: 获取资金账户信息
    note right: 根据机构账户ID获取资金账户ID和累计消耗
    FundAccountDB --> AccountFundController: 返回资金账户信息

    loop 对每个城市
        AccountFundController -> DiscountRuleController: 获取城市折扣策略
        note right: 根据投放城市和累计消耗查询适用折扣
        DiscountRuleController --> AccountFundController: 返回折扣值(找不到取默认值1.4)

        AccountFundController -> FundService: 计算城市刊例费折后总价
        note right: 折后总价 = 折前总价 × 折扣值
    end

    AccountFundController -> FundService: 计算总冻结金额
    note right: 总冻结金额 = ∑各城市刊例费折后总价

    AccountFundController -> FundAccountDB: 检查可用余额
    note right
    检查: 账户余额 - 累计冻结 ≥ ∑{各城市刊例费折前总价 × 各城市折扣值 × 0.1}
    end note
    FundAccountDB --> AccountFundController: 返回检查结果

    alt 可用余额不足
        AccountFundController --> ExternalApiController: 返回错误信息"资金不足：帐户可用余额【A】元，本次投放需冻结【B】元，还差【B-A】元"
        ExternalApiController --> ExternalSystem: 返回冻结失败结果
    else 可用余额充足
        AccountFundController -> FundAccountDB: 更新账户冻结金额
        note right: 增加累计冻结金额

        AccountFundController -> ChangeRecordDB: 创建资金变动记录
        note right
        创建记录：
        - 机构账户ID
        - 变动类型=冻结中
        - 变动原因=刊例费
        - 变动备注=方案名称/客户名称
        - 变动凭据=空
        - 投放方案ID=方案ID
        - 创建方式=系统
        - 操作人=空
        - 时间=创建时间
        end note

        loop 对每个城市
            AccountFundController -> ListingFeeDB: 创建刊例费报价记录
            note right
            创建记录：
            - 资金变动记录ID
            - 城市ID
            - 城市编码
            - 点位单价
            - 折扣值
            - 折前总价
            - 折后总价
            - 实际发生=折后总价
            end note
        end

        AccountFundController --> ExternalApiController: 返回冻结成功结果
        ExternalApiController --> ExternalSystem: 返回冻结成功结果
        note left: 售卖平台端的方案方可转销售成功
    end
end

@enduml