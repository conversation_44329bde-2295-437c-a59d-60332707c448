package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.cheese.crm.common.db.bean.AdvertisingSubjectQueryDTO;
import com.coocaa.cheese.crm.common.db.bean.DashboardCustomerReportSearchDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.SignSubjectApproveDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 签约主体 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface AdvertisingSubjectMapper extends BaseMapper<AdvertisingSubjectEntity> {
    /**
     * 按条件查询品牌列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 品牌列表
     */
    IPage<AdvertisingSubjectEntity> pageList(@Param("page") IPage<AdvertisingSubjectEntity> page,
                                             @Param("condition") AdvertisingSubjectQueryDTO condition);

    /**
     * 按条件查询品牌列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 品牌列表
     */
    IPage<AdvertisingSubjectEntity> h5PageList(@Param("page") IPage<AdvertisingSubjectEntity> page,
                                               @Param("condition") AdvertisingSubjectQueryDTO condition);


    /**
     * 站内审批查询签约主体信息
     */
    List<SignSubjectApproveDTO> queryInnerSubjectEntity(List<Integer> bizIds);


    /**
     * 站内审批根据签约主体id查询详情
     */
    InnerApproveDetailDTO queryInnerApproveDetail(Integer id);

    /**
     * 查询主体信息
     *
     * @param bizIds 签约主体id集合
     * @return 签约主体信息集合
     */
    List<SignSubjectApproveDTO> innerSubjectEntity(List<Integer> bizIds);

    /**
     * 查询签约主体公海列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 签约主体公海列表
     */
    IPage<AdvertisingSubjectEntity> publicSeaPageList(@Param("page") IPage<AdvertisingSubjectEntity> page,
                                                      @Param("condition") AdvertisingSubjectQueryDTO condition);

    /**
     * 获取签约主体公海列表
     */
    IPage<DashboardCustomerReportSearchDTO> customerReportPageList(@Param("page") IPage<DashboardCustomerReportSearchDTO> page,
                                                                   @Param("condition") String condition);
}
