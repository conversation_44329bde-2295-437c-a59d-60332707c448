package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "InnerExamineApproveVO", description = "站内审批返回数据")
public class InnerExamineApproveVO {

    @Schema(description = "节点ID")
    private Integer id;

    @Schema(description = "审批人")
    @TransField(type = TransTypes.USER)
    private Integer userId;
    private String userName;

    @Schema(description = "是否为审批节点，0：提交人节点，1：审批节点，2：结束节点")
    private Integer approvalFlag;

    @Schema(description = "任务状态(字典0139)")
    @TransField(type = TransTypes.DICT)
    private String nodeStatus;
    private String nodeStatusName;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime beginTime;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "审批时间")
    private LocalDateTime approvalTime;

    @Schema(description = "审批结果(字典0147)")
    @TransField(type = TransTypes.DICT)
    private String approvalResult;
    private String approvalResultName;

    @Schema(description = "审批意见")
    private String comment;
}
