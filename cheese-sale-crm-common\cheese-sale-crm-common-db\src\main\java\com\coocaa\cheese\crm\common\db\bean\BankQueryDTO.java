package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Data
@Accessors(chain = true)
public class BankQueryDTO {
    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行帐号(加密)
     */
    private String account;

    /**
     * 帐户类型(字典0078)
     */
    private String type;
}
