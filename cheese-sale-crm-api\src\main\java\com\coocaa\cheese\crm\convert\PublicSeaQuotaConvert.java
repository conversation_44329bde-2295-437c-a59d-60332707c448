package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.PublicSeaQuotaParam;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaQuotaEntity;
import com.coocaa.cheese.crm.vo.PublicSeaQuotaVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/4/30
 */
@Mapper
public interface PublicSeaQuotaConvert extends PageableConvert<PublicSeaQuotaEntity, PublicSeaQuotaVO> {

    PublicSeaQuotaConvert INSTANCE = Mappers.getMapper(PublicSeaQuotaConvert.class);

    /**
     * 将实体类转换为VO
     */
    PublicSeaQuotaVO toVo(PublicSeaQuotaEntity source);

    /**
     * 将VO转换为实体类
     */
    PublicSeaQuotaEntity toEntity(PublicSeaQuotaParam param);
}
