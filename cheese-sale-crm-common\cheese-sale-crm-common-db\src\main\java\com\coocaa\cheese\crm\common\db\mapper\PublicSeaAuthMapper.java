package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.cheese.crm.common.db.bean.PublicSeaAuthDTO;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaAuthEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 公海打捞授权表接口
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
public interface PublicSeaAuthMapper extends BaseMapper<PublicSeaAuthEntity> {

    /**
     * 按条件查询公海打捞授权表列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 公海打捞授权表列表
     */
    IPage<PublicSeaAuthEntity> pageList(@Param("page") IPage<PublicSeaAuthEntity> page,
                                        @Param("condition") PublicSeaAuthDTO condition);
}