@startuml DelayQueueMessageProcessing

skinparam backgroundColor white
skinparam handwritten false

title 延时队列消息处理流程

start

:收到延时消息处理请求;
note right
- 由定时任务触发
- 或手动触发接口
end note

:获取处理队列枚举;
note right
方法: processQueues(messageType)
- 如有指定消息类型，仅处理指定类型队列
- 否则处理所有队列类型
end note

repeat
  :获取队列;
  note right
  redisson.getQueue(queueEnum.getQueueName())
  end note

  :尝试获取队列中的消息;
  note right
  方法: queue.poll()
  - 如队列为空则返回null
  - 获取队头消息并从队列中移除
  end note

  if (消息为空?) then (是)
    :跳到下一个队列;
  else (否)
    :记录接收到的消息;
    note right
    记录队列名称、消息类型和内容
    end note

    :获取对应的消息处理器;
    note right
    方法: MessageHandlerFactory.getHandler(messageType)
    - 根据消息类型获取对应处理器
    - 如未找到则记录警告日志
    end note

    if (处理器存在?) then (是)
      :调用处理器处理消息;
      note right
      方法: handler.handle(message)
      - 处理器内部实现具体业务逻辑
      end note

      if (处理成功?) then (是)
        :记录处理成功日志;
      else (否)
        :记录处理失败日志;
        note right
        捕获异常但不重试
        记录详细异常信息
        end note
      endif
    else (否)
      :记录未找到处理器警告;
      note right
      "未找到消息处理器: " + messageType
      end note
    endif
  endif

repeat while (还有未处理的队列?) is (是)
->否;

stop

@enduml 