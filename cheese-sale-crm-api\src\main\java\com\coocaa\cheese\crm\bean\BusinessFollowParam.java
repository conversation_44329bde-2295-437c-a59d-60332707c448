package com.coocaa.cheese.crm.bean;

import com.coocaa.ad.common.validation.EnumType;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.FollowSubjectEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商机跟进记录管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
@Schema(name = "BusinessFollowParam", description = "商机跟进记录参数")
public class BusinessFollowParam {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @NotNull(message = "商机ID不能为空")
    @Schema(description = "商机ID", type = "Integer", example = "1")
    private Integer businessId;

    @NotNull(message = "跟进时间不能为空")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "跟进时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime followTime;

    @NotBlank(message = "跟进主题不能为空")
    @Schema(description = "跟进主题(字典0072)", type = "String", example = "0072-1")
    @EnumType(message = "跟进主题不正确", value = FollowSubjectEnum.class)
    private String followSubject;

    @NotNull(message = "是否有效沟通不能为空")
    @Schema(description = "是否有效沟通 [0:否, 1:是]", type = "Integer", example = "1")
    private Integer effectiveCommunicationFlag;

    @Size(max = 200, message = "沟通进展不能超过200字")
    @Schema(description = "沟通进展", type = "String", example = "客户反馈积极", maxLength = 200)
    private String communicationProgress;

    @Schema(description = "删除标记 [0:否, 1:是]", type = "Integer", example = "0", hidden = true)
    private Integer deleteFlag;
} 