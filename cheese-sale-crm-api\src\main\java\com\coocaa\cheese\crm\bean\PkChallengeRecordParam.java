package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.audit.bean.ApproveRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Pk挑战记录表参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */

@Data
@Schema(name = "PkChallengeRecordParam", description = "Pk挑战记录表参数")
public class PkChallengeRecordParam implements ApproveRequest {

    @NotNull(message = "主体ID不能为空")
    @Schema(description = "主体ID")
    private Integer advertisingSubjectId;

    @NotNull(message = "预算金额不能为空")
    @Schema(description = "预算金额")
    @Min(value = 10000, message = "预算金额不能小于10000")
    private Integer budgetAmount;

    @NotNull(message = "挑战期望不能为空")
    @Schema(description = "挑战期望")
    private String challengeExpectation;

    @NotNull(message = "商机ID不能为空")
    @Schema(description = "商机ID")
    private Integer businessId;

    @Schema(description = "情况说明", maxLength = 20)
    private String remark;
}
