package com.coocaa.cheese.crm.controller.approve;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.audit.service.AbstractApproveService;
import com.coocaa.cheese.crm.audit.template.BaseApproveTemplate;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.bean.PublicSeaAuthParam;
import com.coocaa.cheese.crm.service.PublicSeaAuthService;
import com.coocaa.cheese.crm.vo.BusinessHisVO;
import com.coocaa.cheese.crm.vo.InnerApprovePublicSeaAuthDetailVO;
import com.coocaa.cheese.crm.vo.InnerApprovePublicSeaAuthPageVO;
import com.coocaa.cheese.crm.vo.UserDeptInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 打捞记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Slf4j
@RestController
@RequestMapping("/public-sea-auths")
@Tag(name = "打捞申请", description = "打捞申请管理")
public class PublicSeaAuthController extends BaseApproveTemplate<InnerApprovePublicSeaAuthDetailVO, PublicSeaAuthParam,
        InnerApprovePublicSeaAuthPageVO, InnerApproveTaskQueryParam> {

    private final PublicSeaAuthService publicSeaAuthService;

    protected PublicSeaAuthController(@Qualifier("publicSeaAuthService") AbstractApproveService<InnerApprovePublicSeaAuthDetailVO,
            PublicSeaAuthParam, InnerApprovePublicSeaAuthPageVO, InnerApproveTaskQueryParam> abstractApproveService) {
        super(abstractApproveService);
        this.publicSeaAuthService = (PublicSeaAuthService) abstractApproveService;
    }

    @Operation(summary = "是否可以直接打捞")
    @Parameter(name = "bizId", description = "实际业务bizId", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{bizId}/can-business-create")
    public ResultTemplate<Boolean> canBusinessCreate(@PathVariable(name = "bizId") Integer bizId) {
        boolean result = publicSeaAuthService.canBusinessCreate(bizId);
        return ResultTemplate.success(result);
    }

    @Operation(summary = "打捞竞争者")
    @Parameter(name = "id", description = "审批id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}/competitors")
    public ResultTemplate<List<UserDeptInfoVO>> getCompetitors(@PathVariable(name = "id") Long id) {
        List<UserDeptInfoVO> competitors = publicSeaAuthService.getUserDeptInfos(id);
        return ResultTemplate.success(competitors);
    }

    @Operation(summary = "客户历史")
    @Parameter(name = "id", description = "审批业务id", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}/history")
    public ResultTemplate<List<BusinessHisVO>> getBusinessHistory(@PathVariable(name = "id") Long id) {
        List<BusinessHisVO> history = publicSeaAuthService.getBusinessHistoryById(id);
        return ResultTemplate.success(history);
    }
}