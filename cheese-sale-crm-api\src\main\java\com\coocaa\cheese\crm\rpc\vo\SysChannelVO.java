package com.coocaa.cheese.crm.rpc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
public class SysChannelVO {

    private Integer id;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称", type = "String", example = "渠道一")
    private String channelName;

    /**
     * 渠道描述
     */
    @Schema(description = "渠道描述", type = "String", example = "渠道描述")
    private String channelDesc;

    /**
     * 系统用户数
     */
    @Schema(description = "系统用户数", type = "Int", example = "1")
    private Integer systemUserCount;


    /**
     * 机构ID
     */
    @Schema(description = "机构ID", type = "Int", example = "1")
    private Integer agencyId;

    @Schema(description = "渠道来源,字典：0118", type = "String", example = "渠道描述")
    private String source;

    @Schema(description = "渠道来源", type = "String", example = "渠道描述")
    private String sourceName;
}
