package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.InstitutionContractParam;
import com.coocaa.cheese.crm.common.db.entity.InstitutionContractEntity;
import com.coocaa.cheese.crm.vo.InstitutionContractVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 机构合同信息转换
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Mapper
public interface InstitutionContractConvert  extends PageableConvert<InstitutionContractEntity, InstitutionContractVO> {
    InstitutionContractConvert INSTANCE = Mappers.getMapper(InstitutionContractConvert.class);

    /**
     * Entity 转 VO
     */
    InstitutionContractVO toVo(InstitutionContractEntity entity);

    /**
     * Entity列表 转 VO列表
     */
    List<InstitutionContractVO> entityToVO(List<InstitutionContractEntity> entityList);

    /**
     * Param 转 Entity
     */
    InstitutionContractEntity toEntity(InstitutionContractParam param);
} 