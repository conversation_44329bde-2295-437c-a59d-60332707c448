package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.bean.InnerProductApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineChangePageDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineDTO;
import com.coocaa.cheese.crm.common.db.entity.ProductLineChangeEntity;

import java.util.List;

/**
 * 产品线变更记录表服务接口
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
public interface IProductLineChangeService extends IService<ProductLineChangeEntity> {

    /*
     * <AUTHOR>
     * @Description 产品线变更审批分页查询数据
     * @Date 2025/6/20 
     * @Param [bizIds]
     * @return java.util.List<com.coocaa.cheese.crm.common.db.bean.ProductLineChangePageDTO>
     **/
    List<ProductLineChangePageDTO> queryInnerProductLineChange(List<Long> bizIds);

    /*
     * <AUTHOR>
     * @Description 产品线变更审批详情查询品牌+主体信息
     * @Date 2025/6/20 
     * @Param [id]
     * @return com.coocaa.cheese.crm.common.db.bean.InnerProductApproveDetailDTO
     **/
    InnerProductApproveDetailDTO queryInnerApproveDetail(Long id);

    /*
     * <AUTHOR>
     * @Description 根据签约主体查询产品线信息
     * @Date 2025/6/25 
     * @Param [id]
     * @return java.util.List<com.coocaa.cheese.crm.common.db.bean.ProductLineDTO>
     **/
    List<ProductLineDTO> queryProductLineBySign(Integer id);
}