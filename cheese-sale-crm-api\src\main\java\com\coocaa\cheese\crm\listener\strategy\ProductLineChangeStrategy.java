package com.coocaa.cheese.crm.listener.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineChangeEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineSignSubjectEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IProductLineChangeService;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import com.coocaa.cheese.crm.common.db.service.IProductLineSignSubjectService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveOpinionTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.ProductLineChangeExecuteStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.listener.event.InnerApproveEvent;
import com.coocaa.cheese.crm.listener.strategy.approval.ApprovalStrategy;
import com.coocaa.cheese.crm.listener.strategy.approval.StationTargetAware;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/7
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ProductLineChangeStrategy implements ApprovalStrategy, StationTargetAware {

    private final IBusinessService businessService;
    private final IProductLineService productLineService;
    private final IInnerApproveService innerApproveService;
    private final IProductLineChangeService productLineChangeService;
    private final IProductLineSignSubjectService productLineSignSubjectService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(InnerApproveEvent event) {

        log.info("产品线变更审批通知信息:{}", JSONUtil.toJsonStr(event));
        Long productLineChangeId = event.getId();
        if (Objects.isNull(productLineChangeId)) {
            throw new BusinessException("产品线变更id为空");
        }

        ProductLineChangeEntity productLineChangeEntity = Optional.ofNullable(productLineChangeService.lambdaQuery()
                .eq(ProductLineChangeEntity::getId, productLineChangeId)
                .eq(ProductLineChangeEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one()).orElseThrow(() -> new BusinessException("产品线变更记录不存在!"));
        log.info("产品线变更审批通知查询:{}", JSONUtil.toJsonStr(productLineChangeEntity));
        if (InnerApproveOpinionTypeEnum.AGREE.equals(event.getOpinionTypeEnum())
                && (StringUtils.isNotBlank(event.getInnerTaskOperateVO().getApprovalStatus())
                && InnerApproveStatusEnum.ALREADY_FINISH.getCode().equals(event.getInnerTaskOperateVO().getApprovalStatus()))) {
            // 审批通过
            agree(productLineChangeEntity);
        } else if (InnerApproveOpinionTypeEnum.REJECT.equals(event.getOpinionTypeEnum())) {
            // 审批不通过
            reject(productLineChangeEntity);
        }
    }

    public void agree(ProductLineChangeEntity productLineChangeEntity) {

        log.info("产品线变更审批通过:{}", JSONUtil.toJsonStr(productLineChangeEntity));
        //用《产品线变更记录》【商机ID】获取关联的【品牌ID】及【签约主体ID即原投放主体】
        Integer businessId = productLineChangeEntity.getBusinessId();
        BusinessEntity business = Optional.ofNullable(businessService.getById(businessId))
                .orElseThrow(() -> new BusinessException("关联的商机信息不存在!"));

        Integer brandId = business.getBrandId();
        Integer signSubjectId = business.getAdvertisingSubjectId();

        //解析（根据分隔符）变更后的产品线为各产品线名称，用【品牌ID】+【产品线名称】查找产品线数据，若有获取其【产品线ID】，若无则创建并获取【产品线ID】
        String afterProductLineStr = productLineChangeEntity.getAfterProductLine();
        if (Strings.isBlank(afterProductLineStr)) {
            throw new BusinessException("变更后产品线不能为空");
        }

        List<String> productLineNames = Arrays.stream(afterProductLineStr.split(Constants.COMMA))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .toList();

        List<ProductLineEntity> existingProductLines = productLineService.lambdaQuery()
                .eq(ProductLineEntity::getBrandId, brandId)
                .in(ProductLineEntity::getName, productLineNames)
                .eq(ProductLineEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();

        Map<String, Long> nameToIdMap = existingProductLines.stream()
                .collect(Collectors.toMap(ProductLineEntity::getName, ProductLineEntity::getId));

        List<String> productLinesToCreate = productLineNames.stream()
                .filter(name -> !nameToIdMap.containsKey(name))
                .collect(Collectors.toList());
        log.info("产品线变更审批通过已存在的产品线:{},需要新增的产品线:{}", JSONUtil.toJsonStr(existingProductLines), JSONUtil.toJsonStr(productLinesToCreate));
        if (CollectionUtil.isNotEmpty(productLinesToCreate)) {
            List<ProductLineEntity> newProductLines = productLinesToCreate.stream()
                    .map(name -> {
                        ProductLineEntity entity = new ProductLineEntity();
                        entity.setName(name);
                        entity.setBrandId(brandId);
                        return entity;
                    })
                    .toList();

            productLineService.saveBatch(newProductLines);

            newProductLines.forEach(pl -> nameToIdMap.put(pl.getName(), pl.getId()));
        }
        //根据上一步获取的【产品线ID】+【签约主体ID】检查是否存在《产品线签约主体关系表》记录，若无则创建，若有则继续保留该记录；
        //若该【签约主体ID】存在其他关联的【产品线ID】（不在2.2.2找到的产品线ID范围内），则删除《产品线签约主体关系表》记录
        List<Long> newProductLineIds = productLineNames.stream()
                .map(nameToIdMap::get)
                .collect(Collectors.toList());

        if (Objects.nonNull(signSubjectId)) {
            List<ProductLineSignSubjectEntity> existingRelations = productLineSignSubjectService.lambdaQuery()
                    .eq(ProductLineSignSubjectEntity::getSignSubjectId, signSubjectId)
                    .eq(ProductLineSignSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .list();

            Map<Boolean, List<ProductLineSignSubjectEntity>> partitionedRelations = existingRelations.stream()
                    .collect(Collectors.partitioningBy(
                            relation -> newProductLineIds.contains(relation.getProductLineId())
                    ));

            List<ProductLineSignSubjectEntity> relationsToDelete = partitionedRelations.get(false);
            List<Long> existingProductLineIds = partitionedRelations.get(true).stream()
                    .map(ProductLineSignSubjectEntity::getProductLineId)
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(relationsToDelete)) {
                List<Long> idsToDelete = relationsToDelete.stream()
                        .map(ProductLineSignSubjectEntity::getId)
                        .toList();

                productLineSignSubjectService.lambdaUpdate()
                        .set(ProductLineSignSubjectEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                        .in(ProductLineSignSubjectEntity::getId, idsToDelete)
                        .update();
            }

            List<Long> productLinesToRelate = newProductLineIds.stream()
                    .filter(id -> !existingProductLineIds.contains(id))
                    .toList();

            if (CollectionUtil.isNotEmpty(productLinesToRelate)) {
                List<ProductLineSignSubjectEntity> newRelations = productLinesToRelate.stream()
                        .map(productLineId -> {
                            ProductLineSignSubjectEntity relation = new ProductLineSignSubjectEntity();
                            relation.setProductLineId(productLineId);
                            relation.setSignSubjectId(signSubjectId);
                            return relation;
                        })
                        .toList();

                productLineSignSubjectService.saveBatch(newRelations);
            }
        }

        productLineChangeEntity.setExecuteStatus(ProductLineChangeExecuteStatusEnum.ALREADY_EXECUTE.getCode());
        productLineChangeEntity.setStatusChangeTime(LocalDateTime.now());
        productLineChangeService.updateById(productLineChangeEntity);
        //修改站内审批业务关联表
        updateInnerApprove(productLineChangeEntity.getId(), InnerApproveOpinionTypeEnum.AGREE);
    }

    public void reject(ProductLineChangeEntity productLineChangeEntity) {

        productLineChangeService.lambdaUpdate()
                .set(ProductLineChangeEntity::getExecuteStatus, ProductLineChangeExecuteStatusEnum.ALREADY_CANCEL.getCode())
                .set(ProductLineChangeEntity::getStatusChangeTime, LocalDateTime.now())
                .eq(ProductLineChangeEntity::getId, productLineChangeEntity.getId())
                .eq(ProductLineChangeEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .update();
        //修改站内审批业务关联表
        updateInnerApprove(productLineChangeEntity.getId(), InnerApproveOpinionTypeEnum.REJECT);
    }

    public void updateInnerApprove(Long id, InnerApproveOpinionTypeEnum innerApproveOpinionTypeEnum) {

        innerApproveService.lambdaUpdate()
                .set(InnerApproveEntity::getResult, innerApproveOpinionTypeEnum.getCode())
                .set(InnerApproveEntity::getCloseTime, LocalDateTime.now())
                .eq(InnerApproveEntity::getBizId, id)
                .update();
    }

    @Override
    public StationTargetEnum getTargetEnum() {
        return StationTargetEnum.PRODUCT_LINE_CHANGE;
    }
}
