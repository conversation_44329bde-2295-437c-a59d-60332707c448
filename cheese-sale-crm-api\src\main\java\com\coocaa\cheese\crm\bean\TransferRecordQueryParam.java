package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 转移记录查询参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "转移记录查询参数")
public class TransferRecordQueryParam {
    @Schema(description = "业务类型", type = "String", example = "0113-1")
    private String bizType;

    @Schema(description = "业务ID", type = "Integer", example = "1")
    private Integer bizId;

    @Schema(description = "来源归属人ID", type = "Integer", example = "101")
    private Integer sourceOwnerId;

    @Schema(description = "目标归属人ID", type = "Integer", example = "102")
    private Integer targetOwnerId;
} 