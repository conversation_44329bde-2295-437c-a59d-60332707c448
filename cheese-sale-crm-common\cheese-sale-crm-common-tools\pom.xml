<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.coocaa.ad</groupId>
        <artifactId>cheese-sale-crm-common</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>cheese-sale-crm-common-tools</artifactId>
    <name>cheese-sale-crm-common-tools</name>
    <version>1.0.0</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <!-- 工具类库 -->
        <!--Apache Commons Lang3-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <!--汉语拼音转换工具-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>
        <!--Joda Time日期处理-->
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>

        <!-- 邮件服务 -->
        <!--JavaMail API-->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>mailapi</artifactId>
        </dependency>
        <!--SMTP协议实现-->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>smtp</artifactId>
        </dependency>

        <!-- Office文档处理 -->
        <!--POI Excel处理-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <!--POI-TL Word模板引擎-->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
        </dependency>


        <!--Spring Boot配置处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!--连接池-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <!-- 腾讯云服务 -->
        <!--COS临时密钥-->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos-sts_api</artifactId>
        </dependency>
        <!--COS对象存储-->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
        </dependency>

        <!-- Excel处理 -->
        <!--EasyExcel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <!-- 第三方SDK -->
        <!--飞书开放平台SDK-->
        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
        </dependency>

        <!-- 自定义依赖 -->
        <dependency>
            <groupId>com.coocaa.ad</groupId>
            <artifactId>cheese-common-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coocaa.ad</groupId>
            <artifactId>cheese-common-data</artifactId>
        </dependency>
        <!-- 日志相关 -->
        <dependency>
            <groupId>com.coocaa.ad</groupId>
            <artifactId>logback-config</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>UTF-8</encoding>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
