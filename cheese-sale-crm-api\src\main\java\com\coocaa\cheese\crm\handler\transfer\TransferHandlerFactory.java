package com.coocaa.cheese.crm.handler.transfer;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 转移处理器工厂
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Component
public class TransferHandlerFactory implements ApplicationContextAware, InitializingBean {

    private ApplicationContext applicationContext;

    private static final Map<String, TransferHandler> HANDLERS = new ConcurrentHashMap<>();

    public static TransferHandler getHandler(String bizType) {
        return HANDLERS.get(bizType);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() {
        Map<String, TransferHandler> handlerMap = applicationContext.getBeansOfType(TransferHandler.class);
        for (TransferHandler handler : handlerMap.values()) {
            HANDLERS.put(handler.getTransferBizType().getCode(), handler);
        }
    }
} 