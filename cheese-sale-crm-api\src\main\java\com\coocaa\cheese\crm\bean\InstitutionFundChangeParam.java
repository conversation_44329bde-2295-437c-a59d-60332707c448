package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 机构资金变动参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构资金变动参数")
public class InstitutionFundChangeParam {

    @Schema(description = "资金账户编码", type = "String", example = "1")
    @NotNull(message = "资金账户编码不能为空")
    private String fundAccountCode;

    @Schema(description = "变动金额", type = "BigDecimal", example = "1000.00")
    @NotNull(message = "变动金额不能为空")
    @DecimalMin(value = "0.01", message = "变动金额不能小于0.01")
    @DecimalMax(value = "9999999.99", message = "变动金额不能大于9999999.99")
    private BigDecimal amount;

    @Schema(description = "变动类型 (字典0111)", type = "String", example = "0111-1")
    @NotBlank(message = "变动类型不能为空")
    private String changeType;

    @Schema(description = "变动原因 (字典0112)", type = "String", example = "0112-1")
    @NotBlank(message = "变动原因不能为空")
    private String changeReason;

    @Schema(description = "备注说明", type = "String", example = "客户充值")
    @Size(max = 100, message = "备注说明长度不能超过100个字符")
    private String description;

    @Schema(description = "变动凭据URL", type = "String", example = "https://www.baidu.com/img/bd_logo1.png")
    private String voucherUrl;
} 