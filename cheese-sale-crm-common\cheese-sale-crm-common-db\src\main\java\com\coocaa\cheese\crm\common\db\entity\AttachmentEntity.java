package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 附件表
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@TableName("sale_crm_attachment")
public class AttachmentEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务ID
     */
    private Integer bizId;

    /**
     * 业务类型
     */
    private Integer type;

    /**
     * 附件子类型
     */
    private String subType;

    /**
     * 文件名称
     */
    @TableField("name")
    private String fileName;

    /**
     * 文件大小(KB)
     */
    @TableField("size")
    private Long fileSize;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件URL
     */
    private String url;

    /**
     * 删除标记[0:未删除, 1:已删除]
     */
    private Integer deleteFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
