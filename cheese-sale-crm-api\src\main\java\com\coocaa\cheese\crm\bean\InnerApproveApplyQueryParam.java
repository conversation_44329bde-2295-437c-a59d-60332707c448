package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Schema(name = "InnerApproveApplyQueryParam", description = "查询站内审批申请列表")
public class InnerApproveApplyQueryParam {

    @NotNull
    @Schema(description = "审批中:false 已审批 true")
    private Boolean examine = Boolean.FALSE;
}
