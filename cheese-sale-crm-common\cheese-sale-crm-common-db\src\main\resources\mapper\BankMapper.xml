<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.BankMapper">

    <resultMap id="BaseResultMap" type="com.coocaa.cheese.crm.common.db.entity.BankEntity">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="account" property="account" jdbcType="VARCHAR" typeHandler="com.coocaa.ad.common.core.handler.EncryptHandler"/>
        <result column="company_id" property="companyId" jdbcType="INTEGER"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="DATE"/>
        <result column="creator" property="creator" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="DATE"/>
        <result column="operator" property="operator" jdbcType="INTEGER"/>
    </resultMap>


    <!-- 按条件查询银行列表 -->
    <select id="pageList" resultMap="BaseResultMap">
        SELECT cb.id, cb.name, cb.account, cb.company_id, scc.name AS company_name, cb.type, cb.update_time, cb.operator
        FROM sale_comm_bank cb LEFT JOIN cheese_sale_crm.sale_comm_company scc ON cb.company_id = scc.id
        <where>
            AND cb.delete_flag = 0
            <if test="condition.bankName != null and condition.bankName != ''">
                AND cb.name LIKE CONCAT('%',#{condition.bankName},'%')
            </if>

            <if test="condition.account != null and condition.account != ''">
                AND cb.account = #{condition.account}
            </if>

            <if test="condition.type != null and condition.type != ''">
                AND cb.type = #{condition.type}
            </if>

            <if test="condition.companyName != null and condition.companyName != ''">
                AND scc.name LIKE CONCAT('%',#{condition.companyName},'%')
            </if>
        </where>
        ORDER BY cb.update_time DESC
    </select>
</mapper>
