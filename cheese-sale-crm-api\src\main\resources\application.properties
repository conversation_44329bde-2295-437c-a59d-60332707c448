spring.application.name=cheese-sale-crm-api

server.port=8021
server.servlet.context-path=/api/sale-crm

#\u5141\u8BB8\u5FAA\u73AF\u4F9D\u8D56 springboot2.4\u4E4B\u540E\u9ED8\u8BA4\u7981\u6B62\u5FAA\u73AF\u4F9D\u8D56
spring.main.allow-circular-references=true

logging.level.root=info
logging.level.com.coocaa=info
#logback\u7684\u65E5\u5FD7\u6253\u5370\u683C\u5F0F
logging.pattern.console=%clr(${LOG_LEVEL_PATTERN:[%level]}) [%d{yyyy-MM-dd HH:mm:ss.SSS}] %clr([${PID:-}]){faint} %clr([%thread]){magenta} %clr([%-40.40logger{80}:%line]){cyan} %msg%n

mybatis-plus.configuration.map-underscore-to-camel-case=true
spring.main.allow-bean-definition-overriding=true
app.code-name=sale-crm

# \u96C6\u6210 prometheus
management.endpoints.web.exposure.include=prometheus,health,metrics
management.metrics.enable.all=true
management.metrics.tags.application=${spring.application.name}
management.server.port=7021