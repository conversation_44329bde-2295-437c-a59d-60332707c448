package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 站内审批业务关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sale_crm_inner_approval")
public class InnerApproveEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务ID
     */
    private Long bizId;

    /**
     * 业务类型(字典0145)
     */
    private String type;

    /**
     * 流程实例code
     */
    private String instanceCode;

    /**
     * 审批结果
     */
    private String result;

    /**
     * 关闭时间
     */
    private LocalDateTime closeTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建人部门ID(飞书openId)
     */
    private String departmentId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
