package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Accessors(chain = true)
public class InnerProductApproveDetailDTO {

    /**
     * 产品线变更id
     */
    private Long id;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 当前商机进度
     */
    private String progress;

    /**
     * 变更前产品线
     */
    private String beforeProductLine;

    /**
     * 变更后产品线
     */
    private String afterProductLine;

    /**
     * 情况说明
     */
    private String remark;
}
