package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;
import com.coocaa.cheese.crm.common.db.mapper.InstitutionAccountMapper;
import com.coocaa.cheese.crm.common.db.service.IInstitutionAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 机构账户 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionAccountServiceImpl
        extends ServiceImpl<InstitutionAccountMapper, InstitutionAccountEntity>
        implements IInstitutionAccountService {

    @Override
    public IPage<InstitutionAccountEntity> pageList(IPage<InstitutionAccountEntity> page, Object condition) {
        return baseMapper.pageList(page, condition);
    }
} 