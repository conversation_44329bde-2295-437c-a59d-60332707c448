<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.BusinessMapper">

    <select id="queryBusinessDetailForInner" resultType="com.coocaa.cheese.crm.common.db.bean.InnerReleaseBusinessDetailDTO">
        SELECT
            scb.id,
            scb.brand_id,
            scas.id AS signSubjectId,
            scas.company_id,
            scb.channel_id,
            scb.assign_time,
            scb.advertising_release_date,
            scb.progress
        FROM sale_crm_business scb
        LEFT JOIN sale_comm_advertising_subject scas ON scb.advertising_subject_id = scas.id AND scas.delete_flag = 0
        WHERE scb.id = #{id}
          AND scb.delete_flag = '0'
    </select>

    <select id="queryDetailForProductLine" resultType="com.coocaa.cheese.crm.common.db.bean.InnerProductLineDetailDTO">
        SELECT
            scb.id,
            scb.brand_id,
            scas.id AS signSubjectId,
            scas.company_id,
            scb.progress
         FROM sale_crm_business scb
         LEFT JOIN sale_comm_advertising_subject scas ON scb.advertising_subject_id = scas.id AND scas.delete_flag = 0
         WHERE scb.id = #{id}
           AND scb.delete_flag = '0'
    </select>

    <select id="queryBusinessToBeReleased" resultType="com.coocaa.cheese.crm.common.db.bean.BusinessDelayDTO">
        SELECT
           scb.id,
           scb.owner_id,
           sscb.name AS brandName,
           scc.name AS signSubjectName,
           scb.advertising_release_date AS subjectReleaseDate
          FROM sale_crm_business scb
          LEFT JOIN sale_comm_brand sscb ON scb.brand_id = sscb.id AND sscb.delete_flag = 0
          LEFT JOIN sale_comm_advertising_subject scas ON scb.advertising_subject_id = scas.id AND scas.delete_flag = 0
          LEFT JOIN sale_comm_company scc ON scas.company_id = scc.id AND scc.delete_flag = 0
         WHERE scb.advertising_release_date IS NOT NULL
           AND scb.advertising_release_date = #{threeDaysLater}
    </select>
</mapper>
