
package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 释放改期记录表
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
@Data
@Accessors(chain = true)
public class InnerReleaseApproveTaskPageDTO implements Serializable {

    /**
     * 释放改期记录id
     */
    private Integer id;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 申请时的主体释放日期
     */
    private LocalDate advertisingReleaseDate;

    /**
     * 申请延期至的日期
     */
    private LocalDate applyDelayDate;

    /**
     * 执行状态
     */
    private String executeStatus;

    /**
     * 失败原因
     */
    private String failReason;
}