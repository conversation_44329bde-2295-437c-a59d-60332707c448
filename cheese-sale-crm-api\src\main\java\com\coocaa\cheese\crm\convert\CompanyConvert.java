package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.CompanyParam;
import com.coocaa.cheese.crm.common.db.entity.CompanyEntity;
import com.coocaa.cheese.crm.vo.CompanyVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 公司企业信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface CompanyConvert extends PageableConvert<CompanyEntity, CompanyVO> {
    CompanyConvert INSTANCE = Mappers.getMapper(CompanyConvert.class);

    /**
     * Entity 转 VO
     */
    CompanyVO toVo(CompanyEntity entity);

    /**
     * VO 转 Entity
     */
    CompanyEntity toEntity(CompanyVO vo);

    /**
     * VO 转 Entity
     */
    CompanyEntity toEntity(CompanyParam param);
}
