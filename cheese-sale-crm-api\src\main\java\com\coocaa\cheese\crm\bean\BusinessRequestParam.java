package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

/**
 * 商机管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-05
 */
@Data
@Schema(description = "商机修改参数")
public class BusinessRequestParam {

    @Schema(description = "备注说明")
    @Size(max = 50, message = "备注说明不能超过50字")
    private String description;

    @Schema(description = "商机延期天数")
    private Integer days;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "主体释放日期")
    private LocalDate advertisingReleaseDate;

} 