package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Accessors(chain = true)
public class InnerProductLineDetailDTO {

    /**
     * 商机id
     */
    private Integer id;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 签约主体id
     */
    private Integer signSubjectId;

    /**
     * 签约主体公司id
     */
    private Integer companyId;

    /**
     * 当前商机进度
     */
    private String progress;
}
