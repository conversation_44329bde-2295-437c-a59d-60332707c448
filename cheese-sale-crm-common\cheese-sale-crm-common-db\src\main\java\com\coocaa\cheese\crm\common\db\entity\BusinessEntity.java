package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商机数据表
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName(value = "sale_crm_business", autoResultMap = true)
public class BusinessEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 渠道ID
     */
    private Integer channelId;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 归属人ID
     */
    private Integer ownerId;

    /**
     * 归属人名称
     */
    private String ownerName;

    /**
     * 管理部门ID
     */
    private String departmentId;

    /**
     * 管理部门名称
     */
    private String departmentName;

    /**
     * 商机编码
     */
    private String code;

    /**
     * 商机进度 [初始, 初步接洽, 合同推进, 已签约]
     */
    private String progress;

    /**
     * 商机状态 [活跃, 终止]
     */
    private String status;

    /**
     * 签约主体ID
     */
    private Integer advertisingSubjectId;

    /**
     * 主体绑定日期
     */
    private LocalDate advertisingBindDate;

    /**
     * 主体释放日期
     */
    private LocalDate advertisingReleaseDate;

    /**
     * 商机绑定状态 [0:未绑定, 1:已绑定]
     */
    private Integer advertisingBindFlag;

    /**
     * 商机释放状态 [0:保护中, 1:已释放]
     */
    private Integer advertisingReleaseFlag;

    /**
     * 分配时间
     */
    private LocalDateTime assignTime;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 备注说明
     */
    private String description;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
