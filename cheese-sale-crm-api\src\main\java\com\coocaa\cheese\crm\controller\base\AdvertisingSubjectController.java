package com.coocaa.cheese.crm.controller.base;

import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectParam;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectPublicSeaQueryParam;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectQueryH5Param;
import com.coocaa.cheese.crm.bean.AdvertisingSubjectQueryParam;
import com.coocaa.cheese.crm.bean.ReportH5Param;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.AdvertisingSubjectService;
import com.coocaa.cheese.crm.vo.AdvertisingSubjectH5VO;
import com.coocaa.cheese.crm.vo.AdvertisingSubjectPublicSeaVO;
import com.coocaa.cheese.crm.vo.AdvertisingSubjectVO;
import com.coocaa.cheese.crm.vo.ProductLineDetailVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 签约主体管理
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@RestController
@RequestMapping("/advertising-subjects")
@Tag(name = "签约主体管理", description = "签约主体管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AdvertisingSubjectController extends BaseController {
    private final AdvertisingSubjectService advertisingSubjectService;

    /**
     * 签约主体下拉列表
     */
    @Operation(summary = "签约主体下拉列表")
    @GetMapping("/dropdown")
    public ResultTemplate<List<CodeNameVO>> listSubjects(
            @RequestParam(name = "brandId") Integer brandId,
            @RequestParam(name = "name", required = false) String name) {
        Objects.requireNonNull(brandId, "品牌ID不能为空");
        return ResultTemplate.success(advertisingSubjectService.listSubjects(brandId, name));
    }

    /**
     * 签约主体列表(分页)
     */
    @Operation(summary = "签约主体列表(分页)")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<AdvertisingSubjectVO>> pageList(@RequestBody PageRequestVO<AdvertisingSubjectQueryParam> pageRequest) {
        return ResultTemplate.success(advertisingSubjectService.pageList(pageRequest));
    }

    /**
     * H5签约主体列表(分页)
     */
    @Operation(summary = "H5签约主体列表(分页)")
    @PostMapping("/h5/page")
    public ResultTemplate<PageResponseVO<AdvertisingSubjectH5VO>> h5PageList(@RequestBody PageRequestVO<AdvertisingSubjectQueryH5Param> pageRequest) {
        return ResultTemplate.success(advertisingSubjectService.h5PageList(pageRequest));
    }

    /**
     * 签约主体详情
     */
    @Operation(summary = "签约主体详情")
    @Parameter(name = "id", description = "签约主体ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<AdvertisingSubjectVO> getDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(advertisingSubjectService.getDetail(id));
    }

    /**
     * 签约主体创建
     */
    @Operation(summary = "创建签约主体")
    @PostMapping
    public ResultTemplate<Boolean> create(@RequestBody @Validated AdvertisingSubjectParam param) {
        param.setProtectionPeriodFlag(BooleFlagEnum.NO.getCode());
        return ResultTemplate.success(advertisingSubjectService.createOrUpdate(null, param));
    }

    /**
     * 签约主体修改
     */
    @Operation(summary = "签约主体修改")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> update(@PathVariable("id") Integer id, @RequestBody AdvertisingSubjectParam param) {
        return ResultTemplate.success(advertisingSubjectService.createOrUpdate(id, param));
    }

    /**
     * 签约主体删除
     */
    @Operation(summary = "签约主体删除")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> delete(@PathVariable("id") Integer id) {
        return ResultTemplate.success(advertisingSubjectService.delete(id));
    }

    /**
     * 主体报备
     */
    @Operation(summary = "主体报备")
    @PostMapping("/report")
    public ResultTemplate<Integer> report(@RequestBody @Validated ReportH5Param param) {
        return ResultTemplate.success(advertisingSubjectService.report(param));

    }

    /**
     * 查询签约主体关联的产品线信息
     */
    @Operation(summary = "查询签约主体关联的产品线信息")
    @GetMapping("/{id}/product-lines")
    public ResultTemplate<ProductLineDetailVO> getProductLinesBySubjectId(@PathVariable("id") Integer subjectId) {
        return ResultTemplate.success(advertisingSubjectService.getProductLinesBySubjectId(subjectId));
    }

    /**
     * 签约主体公海列表(分页)
     */
    @Operation(summary = "签约主体公海列表(分页)")
    @PostMapping("/public-sea/page")
    public ResultTemplate<PageResponseVO<AdvertisingSubjectPublicSeaVO>> publicSeaPageList(@RequestBody PageRequestVO<AdvertisingSubjectPublicSeaQueryParam> pageRequest) {
        return ResultTemplate.success(advertisingSubjectService.publicSeaPageList(pageRequest));
    }

}
