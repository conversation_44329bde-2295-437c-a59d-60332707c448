package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.entity.ListingFeeQuoteEntity;
import com.coocaa.cheese.crm.vo.ListingFeeQuoteVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-09
 */
@Mapper
public interface ListingFeeQuoteConvert extends PageableConvert<ListingFeeQuoteEntity, ListingFeeQuoteVO> {
    ListingFeeQuoteConvert INSTANCE = Mappers.getMapper(ListingFeeQuoteConvert.class);
}
