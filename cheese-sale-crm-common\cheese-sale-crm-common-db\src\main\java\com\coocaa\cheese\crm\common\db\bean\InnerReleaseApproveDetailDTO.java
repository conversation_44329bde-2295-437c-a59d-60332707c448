package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Accessors(chain = true)
public class InnerReleaseApproveDetailDTO {

    /**
     * 释放改期id
     */
    private Integer id;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 商机分配方式
     */
    private String assignWay;

    /**
     * 商机来源渠道
     */
    private String channelId;

    /**
     * 商机分配日期
     */
    private LocalDate assignTime;

    /**
     * 申请时的主体释放日期
     */
    private LocalDate advertisingReleaseDate;

    /**
     * 申请延期至的日期
     */
    private LocalDate applyDelayDate;

    /**
     * 当前商机进度
     */
    private String progress;

    /**
     * 情况说明
     */
    private String remark;

    /**
     * 执行状态
     */
    private String executeStatus;

    /**
     * 失败原因
     */
    private String failReason;
}
