package com.coocaa.cheese.crm.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.DesensitizeSerializer;
import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 商机联系人信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessContactVO", description = "商机联系人信息VO")
public class BusinessContactVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "商机ID", type = "Integer", example = "1")
    private Integer businessId;

    @Schema(description = "姓名", type = "String", example = "张三", maxLength = 20)
    private String name;

    @JSONField(serializeUsing = DesensitizeSerializer.class)
    @Schema(description = "电话(加密存储)", type = "String", example = "138****8000", maxLength = 50)
    private String mobile;

    @Schema(description = "性别：[1-男;2-女]", type = "Integer", example = "1")
    private Integer gender;

    @Schema(description = "部门", type = "String", example = "销售部", maxLength = 20)
    private String department;

    @Schema(description = "职务", type = "String", example = "销售经理", maxLength = 20)
    private String position;

    @Schema(description = "决策态度(字典0070)", type = "String", example = "0070-1")
    @TransField(type = TransTypes.DICT)
    private String decisionAttitude;
    private String decisionAttitudeName;

    @Schema(description = "决策影响力", type = "Integer", example = "3")
    private Integer decisionInfluence;

    @Schema(description = "邮箱(加密传输)", type = "String", example = "<EMAIL>")
    @JSONField(serializeUsing = DesensitizeSerializer.class)
    private String email;

    @Schema(description = "公司地址", type = "String", example = "广东省深圳市", maxLength = 200)
    private String companyAddress;

    @Schema(description = "生日(月日)", maxLength = 4)
    private String birthdayMonthDay;

    @Schema(description = "生日(年)")
    private String birthdayYear;

    @Schema(description = "年龄段(DICT)")
    @TransField(type = TransTypes.DICT)
    private String ageGroup;
    private String ageGroupName;

    @Schema(description = "收件地址", maxLength = 200)
    private String address;

    @Schema(description = "兴趣爱好", maxLength = 40)
    private String hobbies;

    @Schema(description = "教育信息", maxLength = 40)
    private String education;

    @Schema(description = "家庭信息", maxLength = 40)
    private String familyInfo;

    @Schema(description = "人际信息", maxLength = 40)
    private String socialInfo;

    @Schema(description = "事业信息", maxLength = 40)
    private String careerInfo;

    @Schema(description = "生活信息", maxLength = 40)
    private String lifeInfo;

    @Schema(description = "信用价值观信息", maxLength = 40)
    private String creditValues;

    @Schema(description = "名片文件地址", maxLength = 200)
    private String businessCardUrl;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "操作人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer operator;

    @Schema(description = "操作人姓名", type = "String", example = "张三")
    private String operatorName;
} 