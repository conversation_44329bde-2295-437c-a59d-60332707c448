package com.coocaa.cheese.crm.controller.business;

import com.coocaa.ad.common.annotation.RepeatStrategy;
import com.coocaa.ad.common.annotation.RepeatSubmit;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.BusinessH5QueryParam;
import com.coocaa.cheese.crm.bean.BusinessJudgeParam;
import com.coocaa.cheese.crm.bean.BusinessParam;
import com.coocaa.cheese.crm.bean.BusinessRequestParam;
import com.coocaa.cheese.crm.bean.BusinessWebQueryParam;
import com.coocaa.cheese.crm.common.tools.constant.BusinessConstants;
import com.coocaa.cheese.crm.config.annotation.OperateLog;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.BusinessService;
import com.coocaa.cheese.crm.vo.BusinessAdvertisingSubjectVO;
import com.coocaa.cheese.crm.vo.BusinessContactVO;
import com.coocaa.cheese.crm.vo.BusinessDetailVO;
import com.coocaa.cheese.crm.vo.BusinessFollowVO;
import com.coocaa.cheese.crm.vo.BusinessH5VO;
import com.coocaa.cheese.crm.vo.BusinessIndexVO;
import com.coocaa.cheese.crm.vo.BusinessStatisticsVO;
import com.coocaa.cheese.crm.vo.BusinessWebVO;
import com.coocaa.cheese.permission.core.annotation.DataPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 商机管理
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@RestController
@RequestMapping("/businesses")
@Tag(name = "商机管理", description = "商机管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessController extends BaseController {
    private final BusinessService businessService;

    /**
     * 首页即将释放的商机列表
     */
    @Operation(summary = "首页即将释放的商机列表")
    @GetMapping("/index/page")
    @DataPermission(tableName = BusinessConstants.BUSINESS_TABLE_NAME)
    public ResultTemplate<List<BusinessIndexVO>> getListBusinessIndex(@Parameter(name = "showNum", description = "展示条数，默认4条") @RequestParam(name = "showNum", required = false) Integer showNum) {
        return ResultTemplate.success(businessService.getListBusinessIndex(showNum));
    }

    /**
     * h5商机列表(分页)
     */
    @Operation(summary = "h5商机列表(分页)")
    @PostMapping("/h5/page")
    @DataPermission(tableName = BusinessConstants.BUSINESS_TABLE_NAME)
    public ResultTemplate<PageResponseVO<BusinessH5VO>> pageListBusinessH5(@RequestBody PageRequestVO<BusinessH5QueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(BusinessH5QueryParam::new));
        return ResultTemplate.success(businessService.pageListBusinessH5(pageRequest));
    }

    /**
     * web商机列表(分页)
     */
    @Operation(summary = "web商机列表(分页)")
    @PostMapping("/web/page")
    @DataPermission(tableName = BusinessConstants.BUSINESS_TABLE_NAME)
    public ResultTemplate<PageResponseVO<BusinessWebVO>> pageListBusinessWeb(@RequestBody PageRequestVO<BusinessWebQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(BusinessWebQueryParam::new));
        return ResultTemplate.success(businessService.pageListBusinessWeb(pageRequest));
    }

    /**
     * 商机详情
     */
    @Operation(summary = "商机详情")
    @Parameter(name = "id", description = "商机ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<BusinessDetailVO> getBusinessDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessService.getBusinessDetail(id));
    }

    /**
     * 商机创建
     */
    @Operation(summary = "创建商机")
    @PostMapping
    @RepeatSubmit(strategy = RepeatStrategy.SPECIFIED_PARAMS, params = {"advertisingSubjectId"})
    public ResultTemplate<Boolean> createBusiness(@RequestBody @Validated BusinessParam business) {
        return ResultTemplate.success(businessService.createBusiness(business));
    }

    /**
     * 修改商机备注说明
     */
    @Operation(summary = "修改商机备注说明")
    @PutMapping("/{id}/description")
    public ResultTemplate<Boolean> updateBusinessDescription(
            @Parameter(description = "商机ID") @PathVariable("id") Integer id,
            @RequestBody @Validated BusinessRequestParam param) {
        return ResultTemplate.success(businessService.updateBusinessDescription(id, param.getDescription()));
    }

    /**
     * 获取首页统计
     */
    @Operation(summary = "获取首页统计")
    @GetMapping("/progress-statistics")
    @DataPermission(tableName = BusinessConstants.BUSINESS_TABLE_NAME)
    public ResultTemplate<BusinessStatisticsVO> getProgressStatistics() {
        return ResultTemplate.success(businessService.getProgressStatistics());
    }

    /**
     * 延期签约主体
     */
    @Operation(summary = "延期签约主体")
    @Parameter(name = "id", description = "商机ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @Parameter(name = "days", description = "延期天数，不传则表示永久", in = ParameterIn.QUERY, schema = @Schema(type = "int"))
    @PutMapping("/{id}/extend")
    @OperateLog(functionName = "延期签约主体", entityCode = "0005-25", entityId = "#id")
    public ResultTemplate<Boolean> extendAdvertisingSubject(
            @PathVariable("id") Integer id,
            @RequestBody @Validated BusinessRequestParam param) {
        return ResultTemplate.success(businessService.extendAdvertisingSubject(id, param));
    }

    /**
     * 释放签约主体
     */
    @Operation(summary = "释放签约主体")
    @Parameter(name = "id", description = "商机ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @PutMapping("/{id}/release")
    @OperateLog(functionName = "释放签约主体", entityCode = "0005-25", entityId = "#id")
    public ResultTemplate<Boolean> releaseAdvertisingSubject(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessService.releaseAdvertisingSubject(id));
    }

    /**
     * sale-cms使用，获取当前登录用户自己及下属的有效商机（保护期内）列表
     */
    @Operation(summary = "获取当前用户下所有有效商机id")
    @GetMapping("/id-list-by-user")
    @DataPermission(tableName = BusinessConstants.BUSINESS_TABLE_NAME)
    public ResultTemplate<Set<Integer>> getBusinessIdListByUser() {
        return ResultTemplate.success(businessService.getBusinessIdListByUser());
    }

    /**
     * 根据签约主体ID获取保护中的商机详情(用于报价单查询)
     */
    @Operation(summary = "根据签约主体ID获取保护中的商机详情")
    @Parameter(name = "advertisingSubjectId", description = "签约主体ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/detail/{advertisingSubjectId}")
    public ResultTemplate<List<BusinessAdvertisingSubjectVO>> getDetailByAdvertisingSubjectId(@PathVariable("advertisingSubjectId") Integer advertisingSubjectId) {
        return ResultTemplate.success(businessService.getDetailByAdvertisingSubjectId(advertisingSubjectId));
    }

    /**
     * 查询商机是否在保护中
     */
    @Operation(summary = "查询商机是否在保护中")
    @Parameter(name = "id", description = "商机ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}/is-protected")
    public ResultTemplate<Boolean> isBusinessProtected(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessService.isBusinessProtected(id));
    }

    @Operation(summary = "商机判定")
    @PostMapping("/judge")
    public ResultTemplate<Boolean> judgeBusiness(@RequestBody BusinessJudgeParam param) {
        return ResultTemplate.success(businessService.judgeBusiness(param));
    }

    @Operation(summary = "获取联系人列表")
    @GetMapping("/{id}/contact-list")
    public ResultTemplate<List<BusinessContactVO>> getContactList(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessService.getContactList(id));
    }

    @Operation(summary = "获取跟进记录列表")
    @GetMapping("/{id}/follow-list")
    public ResultTemplate<List<BusinessFollowVO>> getFollowList(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessService.getFollowList(id));
    }

    /**
     * 查询在保护中的商机
     */
    @Operation(summary = "查询在保护中的商机")
    @PostMapping("/protected")
    public ResultTemplate<List<Integer>> getBusinessProtected(@RequestBody List<Integer> ids) {
        return ResultTemplate.success(businessService.getBusinessProtected(ids));
    }
}