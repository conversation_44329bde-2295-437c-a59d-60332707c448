package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.annotation.DistributedLock;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.common.util.BigDecimalUtils;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.InstitutionFundChangeParam;
import com.coocaa.cheese.crm.bean.InstitutionFundFreezeParam;
import com.coocaa.cheese.crm.bean.InstitutionFundQueryParam;
import com.coocaa.cheese.crm.bean.ListingFeeQuoteParam;
import com.coocaa.cheese.crm.bean.PlanFreezeParam;
import com.coocaa.cheese.crm.bean.PlanUnfreezeParam;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;
import com.coocaa.cheese.crm.common.db.entity.InstitutionFundAccountEntity;
import com.coocaa.cheese.crm.common.db.entity.InstitutionFundChangeRecordEntity;
import com.coocaa.cheese.crm.common.db.entity.ListingFeeQuoteEntity;
import com.coocaa.cheese.crm.common.db.service.IInstitutionAccountService;
import com.coocaa.cheese.crm.common.db.service.IInstitutionFundAccountService;
import com.coocaa.cheese.crm.common.db.service.IInstitutionFundChangeRecordService;
import com.coocaa.cheese.crm.common.db.service.IListingFeeQuoteService;
import com.coocaa.cheese.crm.common.tools.constant.BusinessConstants;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.AccountStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.ChangeReasonDecreaseEnum;
import com.coocaa.cheese.crm.common.tools.enums.ChangeReasonEnum;
import com.coocaa.cheese.crm.common.tools.enums.ChangeReasonFreezeEnum;
import com.coocaa.cheese.crm.common.tools.enums.ChangeReasonIncreaseEnum;
import com.coocaa.cheese.crm.common.tools.enums.ChangeTypeEnum;
import com.coocaa.cheese.crm.common.tools.util.EasyExcelUtils;
import com.coocaa.cheese.crm.common.tools.util.StringUtils;
import com.coocaa.cheese.crm.convert.InstitutionFundAccountConvert;
import com.coocaa.cheese.crm.convert.InstitutionFundChangeRecordConvert;
import com.coocaa.cheese.crm.convert.ListingFeeQuoteConvert;
import com.coocaa.cheese.crm.mongo.PointFeeDocument;
import com.coocaa.cheese.crm.mongo.service.PointFeeService;
import com.coocaa.cheese.crm.rpc.FeignSaleCmsRpc;
import com.coocaa.cheese.crm.rpc.bean.EvidencePublishCalculateParam;
import com.coocaa.cheese.crm.rpc.vo.EvidencePublishVO;
import com.coocaa.cheese.crm.util.DateBillingCalculator;
import com.coocaa.cheese.crm.vo.InstitutionFundAccountVO;
import com.coocaa.cheese.crm.vo.InstitutionFundChangeRecordVO;
import com.coocaa.cheese.crm.vo.ListingFeeExportVO;
import com.coocaa.cheese.crm.vo.ListingFeeQuoteVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 机构资金账户服务
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionFundAccountService {
    private final IInstitutionAccountService institutionAccountService;
    private final IInstitutionFundAccountService institutionFundAccountService;
    private final IInstitutionFundChangeRecordService institutionFundChangeRecordService;
    private final DiscountRuleService discountRuleService;
    private final IListingFeeQuoteService listingFeeQuoteService;
    private final UserCacheHelper userCacheHelper;
    private final FeignSaleCmsRpc feignSaleCmsRpc;
    private final PointFeeService pointFeeService;


    /**
     * 获取机构资金账户
     *
     * @return 机构资金账户
     */
    @AutoTranslate
    public InstitutionFundAccountVO getInstitutionFundAccount(String institutionCode) {
        InstitutionFundAccountEntity fundAccountByInstitutionId;
        if (StringUtils.isBlank(institutionCode)) {
            fundAccountByInstitutionId = getFundAccountByInstitutionId();
        } else {
            fundAccountByInstitutionId = getFundAccountByInstitutionCode(institutionCode);
        }
        // 转换为VO
        return InstitutionFundAccountConvert.INSTANCE.entityToVO(fundAccountByInstitutionId);
    }

    /**
     * 分页查询资金变动记录
     *
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    @AutoTranslate
    public PageResponseVO<InstitutionFundChangeRecordVO> pageListFundChangeRecord(PageRequestVO<InstitutionFundQueryParam> pageRequest) {
        // 构建分页对象
        Page<InstitutionFundChangeRecordEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));

        // 获取查询参数
        InstitutionFundQueryParam queryParam = Optional.ofNullable(pageRequest.getQuery())
                .orElseGet(InstitutionFundQueryParam::new);

        // 如果传入了月份，则设置对应的开始时间和结束时间
        if (Objects.nonNull(queryParam.getMonth())) {
            try {
                // 使用YearMonth解析年月字符串 (格式: yyyy-MM)
                YearMonth yearMonth = YearMonth.parse(queryParam.getMonth());

                // 设置月初时间为当月第一天的 00:00:00
                LocalDateTime firstDayOfMonth = yearMonth.atDay(1).atStartOfDay();
                // 设置月末时间为当月最后一天的 23:59:59
                LocalDateTime lastDayOfMonth = yearMonth.atEndOfMonth().atTime(23, 59, 59);

                queryParam.setStartTime(firstDayOfMonth);
                queryParam.setEndTime(lastDayOfMonth);
            } catch (Exception e) {
                throw new BusinessException("月份格式错误，正确格式为：yyyy-MM");
            }
        }

        // 根据机构账户编码获取资金账户id
        InstitutionFundAccountEntity fundAccountByInstitutionId;
        if (StringUtils.isBlank(queryParam.getFundAccountCode())) {
            fundAccountByInstitutionId = getFundAccountByInstitutionId();
        } else {
            fundAccountByInstitutionId = getFundAccountByInstitutionCode(queryParam.getFundAccountCode());
        }

        // 查询数据
        IPage<InstitutionFundChangeRecordEntity> pageResult = institutionFundChangeRecordService.lambdaQuery()
                .eq(InstitutionFundChangeRecordEntity::getFundAccountId, fundAccountByInstitutionId.getId())
                .ge(Objects.nonNull(queryParam.getStartTime()),
                        InstitutionFundChangeRecordEntity::getUpdateTime, queryParam.getStartTime())
                .le(Objects.nonNull(queryParam.getEndTime()),
                        InstitutionFundChangeRecordEntity::getUpdateTime, queryParam.getEndTime())
                .eq(InstitutionFundChangeRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(InstitutionFundChangeRecordEntity::getCreateTime)
                .page(page);

        // 转换为VO
        return InstitutionFundChangeRecordConvert.INSTANCE.toPageResponse(pageResult);
    }

    /**
     * 资金操作（增加、减少、冻结）
     *
     * @param param 资金变动参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(key = "#fundAccount.id", prefix = BusinessConstants.LOCK_PREFIX_FUND_ACCOUNT, errorMessage = "该账户正在进行资金操作，请重试")
    public Boolean changeFund(InstitutionFundChangeParam param, InstitutionFundAccountEntity fundAccount) {

        // 创建资金变动记录
        InstitutionFundChangeRecordEntity record = createFundChangeRecord(
                fundAccount.getId(),
                param.getAmount(),
                param.getChangeType(),
                param.getChangeReason(),
                param.getDescription(),
                param.getVoucherUrl(),
                // 人工操作
                BooleFlagEnum.YES.getCode(),
                null
        );

        // 如果操作类型为减的则，需要检查账户余额是否足够  还需要把冻结金额也减掉
        if (ChangeTypeEnum.DECREASE.getCode().equals(param.getChangeType())) {
            if (BigDecimalUtils.lt(BigDecimalUtils.sub(fundAccount.getBalance(), fundAccount.getTotalFrozen()), param.getAmount())) {
                throw new BusinessException("账户可用余额不足，无法扣除资金");
            }
        }

        // 检查冻结操作的非冻结资金是否足够
        if (ChangeTypeEnum.FREEZE.getCode().equals(param.getChangeType())) {
            if (BigDecimalUtils.lt(BigDecimalUtils.sub(fundAccount.getBalance(), fundAccount.getTotalFrozen()), param.getAmount())) {
                throw new BusinessException("账户可用余额不足，无法冻结资金");
            }
        }

        // 保存记录
        institutionFundChangeRecordService.save(record);

        // 更新账户余额
        updateFundAccount(fundAccount, record);

        return institutionFundAccountService.updateById(fundAccount);
    }

    /**
     * 处理冻结资金
     *
     * @param id    冻结记录ID
     * @param param 处理参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(key = "#param.fundAccountId", prefix = BusinessConstants.LOCK_PREFIX_FUND_ACCOUNT, errorMessage = "该账户正在进行资金操作，请重试")
    public Boolean processFrozenFund(Integer id, InstitutionFundFreezeParam param) {
        // 查询冻结记录
        InstitutionFundChangeRecordEntity freezeRecord = institutionFundChangeRecordService.getById(id);
        if (freezeRecord == null) {
            throw new BusinessException("冻结记录不存在");
        }

        // 检查记录类型
        if (!ChangeTypeEnum.FREEZE.getCode().equals(freezeRecord.getChangeType())) {
            throw new BusinessException("该记录不是冻结记录");
        }

        // 查询资金账户
        InstitutionFundAccountEntity fundAccount = institutionFundAccountService.getById(freezeRecord.getFundAccountId());
        freezeRecord.setAdjustmentVoucherUrl(param.getAdjustmentVoucherUrl());
        // 根据处理类型进行不同处理
        if (param.getProcessType() == 1) {
            // 解冻
            return unfreezeProcess(fundAccount, freezeRecord, param.getDescription());
        } else if (param.getProcessType() == 2) {
            // 转扣费
            return deductProcess(fundAccount, freezeRecord, param.getDeductAmount(), param.getDescription());
        } else {
            throw new BusinessException("不支持的处理类型");
        }

    }

    /**
     * 根据当前登录用户获取机构账户id   再获取资金账户
     *
     * @return 资金账户
     */
    public InstitutionFundAccountEntity getFundAccountByInstitutionId() {
        Integer userChannelId = userCacheHelper.getUserChannelId(UserThreadLocal.getUserId(), null);
        return getFundAccountByInstitutionId(userChannelId, false);
    }

    /**
     * 根据机构帐户id获取资金帐户
     * 并校验他们的状态
     *
     * @param institutionId 机构id
     * @return 资金帐户
     */
    public InstitutionFundAccountEntity getFundAccountByInstitutionId(Integer institutionId, Boolean isFreeze) {
        // 查询机构账户
        InstitutionAccountEntity institutionAccount = institutionAccountService.getById(institutionId);
        if (institutionAccount == null) {
            throw new BusinessException("机构账户不存在");
        }
        if (isFreeze && AccountStatusEnum.DISABLED.getCode().equals(institutionAccount.getAccountStatus())) {
            throw new BusinessException("该机构帐户已停用");
        }

        // 查询资金账户
        InstitutionFundAccountEntity fundAccount = institutionFundAccountService.lambdaQuery()
                .eq(InstitutionFundAccountEntity::getInstitutionId, institutionAccount.getId())
                .eq(InstitutionFundAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        if (fundAccount == null) {
            throw new BusinessException("资金帐户不存在");
        }
        if (isFreeze && AccountStatusEnum.DISABLED.getCode().equals(fundAccount.getAccountStatus())) {
            throw new BusinessException("该机构资金帐户已停用");
        }
        return fundAccount;
    }

    /**
     * 根据机构帐户id获取资金帐户
     * 并校验他们的状态
     *
     * @param institutionCode 机构code
     * @return 资金帐户
     */
    public InstitutionFundAccountEntity getFundAccountByInstitutionCode(String institutionCode) {
        // 查询机构账户
        InstitutionAccountEntity institutionAccount = institutionAccountService.lambdaQuery()
                .eq(InstitutionAccountEntity::getAccountCode, institutionCode)
                .eq(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        if (institutionAccount == null) {
            throw new BusinessException("机构账户不存在");
        }

        // 查询资金账户
        InstitutionFundAccountEntity fundAccount = institutionFundAccountService.lambdaQuery()
                .eq(InstitutionFundAccountEntity::getInstitutionId, institutionAccount.getId())
                .eq(InstitutionFundAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        if (fundAccount == null) {
            throw new BusinessException("资金帐户不存在");
        }
        return fundAccount;
    }

    /**
     * 方案转扣费冻结
     *
     * @param param 请求入参
     * @return 方案转扣费冻结结果
     */
    @DistributedLock(key = "#fundAccount.id", prefix = BusinessConstants.LOCK_PREFIX_FUND_ACCOUNT, errorMessage = "该账户正在进行资金操作，请重试")
    public Boolean planFreeze(PlanFreezeParam param, InstitutionFundAccountEntity fundAccount) {
        // 检查该方案是否已经冻结 如果已经冻结 直接返回true
        if (institutionFundChangeRecordService.lambdaQuery()
                .eq(InstitutionFundChangeRecordEntity::getPlanId, param.getPlanId())
                .eq(InstitutionFundChangeRecordEntity::getChangeType, ChangeTypeEnum.FREEZE.getCode())
                .eq(InstitutionFundChangeRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .exists()) {
            return true;
        }

        // 计算刊例费
        String billingType = param.getBillingType();
        // 获取计费周期
        long launchCycle = DateBillingCalculator.calculateBillingUnits(param.getStartDate(), param.getEndDate(), billingType);
        int cycle = Math.toIntExact(launchCycle);
        param.setLaunchCycle(cycle);
        EvidencePublishCalculateParam evidencePublishCalculateParam = new EvidencePublishCalculateParam(
                billingType,
                cycle,
                Collections.singletonList(param.getPlanId())
        );
        List<EvidencePublishVO> publishList = feignSaleCmsRpc.calculatePublishList(evidencePublishCalculateParam).getData();
        if (CollectionUtils.isEmpty(publishList)) {
            throw new BusinessException("刊例明细计算结果为空");
        }

        // 根据城市分组
        Map<Integer, List<EvidencePublishVO>> publishMap = publishList.stream()
                .collect(Collectors.groupingBy(EvidencePublishVO::getCityId));

        // 获取资金帐户累计消耗
        BigDecimal totalConsumption = fundAccount.getTotalConsumption();

        // 本次需冻结金额
        BigDecimal allCityAfterDiscount = BigDecimal.ZERO;

        List<ListingFeeQuoteEntity> cityFees = new ArrayList<>();
        // 获取机构帐户的调剂比例
        BigDecimal adjustmentRatio = getAdjustmentRatio(param.getInstitutionId());
        // 计算每个城市的刊例费
        for (Map.Entry<Integer, List<EvidencePublishVO>> cityEntry : publishMap.entrySet()) {
            List<EvidencePublishVO> cityPublish = cityEntry.getValue();

            // 总点位数
            int totalPointCount = cityPublish.stream().mapToInt(EvidencePublishVO::getPointCount).sum();
            // 计费数量
            int billingPointCount = BigDecimal.valueOf(totalPointCount).divide(adjustmentRatio, 0, RoundingMode.UP).intValue();

            EvidencePublishVO one = cityPublish.get(0);
            // 获取单价
            BigDecimal price = one.getPrice();

            if (BigDecimalUtils.eq(price, BigDecimal.ZERO)) {
                throw new BusinessException(one.getCityName() + "尚未设置刊例价，请联系销售支持");
            }
            BigDecimal cityTotalPrice = BigDecimalUtils.multiply(price, new BigDecimal(billingPointCount)).multiply(new BigDecimal(cycle));
            // 获取城市折扣
            BigDecimal discount = discountRuleService.calculateDiscount(String.valueOf(cityEntry.getKey()), totalConsumption);

            // 计算城市的折后价格
            BigDecimal afterDiscountPrice = cityTotalPrice.multiply(discount).multiply(Constants.ZERO_POINT_ONE).setScale(2, RoundingMode.DOWN);

            allCityAfterDiscount = BigDecimalUtils.add(allCityAfterDiscount, afterDiscountPrice);
            ListingFeeQuoteEntity cityFee = buildCityFee(
                    cityEntry.getKey(),
                    one.getCityName(),
                    param,
                    totalPointCount,
                    billingPointCount,
                    price,
                    discount,
                    cityTotalPrice,
                    afterDiscountPrice
            );
            cityFees.add(cityFee);
        }

        // 检查帐户余额是否足够
        BigDecimal balance = fundAccount.getBalance();
        BigDecimal availableBalance = balance.subtract(fundAccount.getTotalFrozen());
        if (BigDecimalUtils.lt(availableBalance, allCityAfterDiscount)) {
            throw new BusinessException(String.format("资金不足：帐户可用余额%s元，本次投放需冻结%s元，还差%s元",
                    availableBalance,
                    allCityAfterDiscount,
                    BigDecimalUtils.sub(allCityAfterDiscount, availableBalance))
            );
        }

        // 创建冻结记录
        InstitutionFundChangeRecordEntity changeRecord = createFundChangeRecord(
                fundAccount.getId(),
                allCityAfterDiscount,
                ChangeTypeEnum.FREEZE.getCode(),
                ChangeReasonEnum.LISTING_FEE.getCode(),
                param.getPlanName() + Constants.SLASH + param.getCustomerName(),
                null,
                BooleFlagEnum.NO.getCode(),
                param.getPlanId());

        // 更新冻结金额
        fundAccount.setTotalFrozen(BigDecimalUtils.add(fundAccount.getTotalFrozen(), allCityAfterDiscount));
        // 入库
        savePlanFreeze(changeRecord, cityFees, fundAccount);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePlanFreeze(InstitutionFundChangeRecordEntity changeRecord,
                               List<ListingFeeQuoteEntity> cityFees,
                               InstitutionFundAccountEntity fundAccount) {
        institutionFundChangeRecordService.save(changeRecord);
        cityFees.forEach(item -> item.setFundChangeRecordId(changeRecord.getId()));
        listingFeeQuoteService.saveBatch(cityFees);
        institutionFundAccountService.updateById(fundAccount);
    }

    /**
     * 方案解冻
     *
     * @param param       请求入参
     * @param fundAccount 资金帐户信息
     * @return 方案解冻结果
     */
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(key = "#fundAccount.id", prefix = BusinessConstants.LOCK_PREFIX_FUND_ACCOUNT, errorMessage = "该账户正在进行资金操作，请重试")
    public Boolean planUnfreeze(PlanUnfreezeParam param, InstitutionFundAccountEntity fundAccount) {
        // 解冻该方案的所有的资金变动记录 包括人工的
        List<InstitutionFundChangeRecordEntity> list = institutionFundChangeRecordService.lambdaQuery()
                .eq(InstitutionFundChangeRecordEntity::getFundAccountId, fundAccount.getId())
                .eq(InstitutionFundChangeRecordEntity::getPlanId, param.getPlanId())
                .eq(InstitutionFundChangeRecordEntity::getChangeType, ChangeTypeEnum.FREEZE.getCode())
                .eq(InstitutionFundChangeRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();

        if (CollectionUtils.isEmpty(list)) {
            // 兼容老数据  线上已经转销售的   需要能取消转销售状态
            return true;
        }

        list.forEach(item -> item.setDeleteFlag(BooleFlagEnum.YES.getCode()));

        institutionFundChangeRecordService.updateBatchById(list);

        // 删掉所有的刊例明细
        listingFeeQuoteService.lambdaUpdate()
                .in(ListingFeeQuoteEntity::getFundChangeRecordId, list.stream().map(InstitutionFundChangeRecordEntity::getId).collect(Collectors.toList()))
                .eq(ListingFeeQuoteEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .set(ListingFeeQuoteEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .update();

        // 更新帐户冻结金额
        fundAccount.setTotalFrozen(BigDecimalUtils.sub(fundAccount.getTotalFrozen(),
                list.stream().map(InstitutionFundChangeRecordEntity::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimalUtils::add)));
        institutionFundAccountService.updateById(fundAccount);
        return true;
    }

    /**
     * 分页查询刊例账单
     *
     * @param pageRequest 分页请求对象
     * @return 分页账单数据
     */
    @AutoTranslate
    public PageResponseVO<ListingFeeQuoteVO> pageListListingFee(PageRequestVO<ListingFeeQuoteParam> pageRequest) {
        // 构建分页对象
        Page<ListingFeeQuoteEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));

        // 获取查询参数
        ListingFeeQuoteParam queryParam = Optional.ofNullable(pageRequest.getQuery())
                .orElseGet(ListingFeeQuoteParam::new);

        if (Objects.isNull(queryParam.getFundChangeRecordId())) {
            throw new BusinessException("变动记录id不能为空");
        }

        // 查询数据
        IPage<ListingFeeQuoteEntity> pageResult = listingFeeQuoteService.lambdaQuery()
                .eq(Objects.nonNull(queryParam.getFundChangeRecordId()),
                        ListingFeeQuoteEntity::getFundChangeRecordId, queryParam.getFundChangeRecordId())
                .eq(ListingFeeQuoteEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(ListingFeeQuoteEntity::getCreateTime)
                .page(page);

        // 转换为VO
        return ListingFeeQuoteConvert.INSTANCE.toPageResponse(pageResult);
    }

    /**
     * 获取机构帐户的调剂比例
     */
    private BigDecimal getAdjustmentRatio(Integer institutionId) {
        if (null == institutionId) {
            return BigDecimal.ZERO;
        }
        InstitutionAccountEntity byId = institutionAccountService.getById(institutionId);
        // （100+《机构账户》【调剂比例】)/100
        if (null != byId && null != byId.getAdjustmentRatio()) {
            return BigDecimal.valueOf(100 + byId.getAdjustmentRatio()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }


    /**
     * 构建城市刊例费
     *
     * @param cityId            城市id
     * @param cityName          城市名称
     * @param param             请求入参 方案的信息等
     * @param pointCount        点位总数
     * @param billingPointCount 计费点位总数
     * @param pointUnitPrice    点位单价
     * @param discount          适用折扣
     * @param preDiscountTotal  折前总价
     * @param postDiscountTotal 折后总价
     * @return 城市刊例费
     */
    private ListingFeeQuoteEntity buildCityFee(Integer cityId, String cityName, PlanFreezeParam param, Integer pointCount, Integer billingPointCount,
                                               BigDecimal pointUnitPrice, BigDecimal discount, BigDecimal preDiscountTotal, BigDecimal postDiscountTotal) {
        ListingFeeQuoteEntity cityFee = new ListingFeeQuoteEntity();
        cityFee.setCityId(cityId);
        cityFee.setCityName(cityName);
        cityFee.setBillingType(param.getBillingType());
        cityFee.setPointCount(pointCount);
        cityFee.setBillingPointCount(billingPointCount);
        cityFee.setPointUnitPrice(pointUnitPrice);
        cityFee.setBillingCycle(param.getLaunchCycle());
        cityFee.setApplicableDiscount(discount);
        cityFee.setPreDiscountTotal(preDiscountTotal);
        cityFee.setPostDiscountTotal(postDiscountTotal);
        return cityFee;
    }

    /**
     * 创建资金变动记录
     */
    private InstitutionFundChangeRecordEntity createFundChangeRecord(
            Integer fundAccountId,
            BigDecimal amount,
            String changeType,
            String changeReason,
            String description,
            String voucherUrl,
            Integer creationMethod,
            Integer planId) {

        InstitutionFundChangeRecordEntity record = new InstitutionFundChangeRecordEntity();
        record.setFundAccountId(fundAccountId);
        record.setChangeAmount(amount);
        record.setChangeType(changeType);
        record.setChangeReason(changeReason);
        record.setDescription(description);
        record.setVoucherUrl(voucherUrl);
        record.setPlanId(planId);
        record.setCreationMethod(creationMethod);
        record.setDeleteFlag(BooleFlagEnum.NO.getCode());

        return record;
    }

    /**
     * 更新资金账户
     */
    private void updateFundAccount(InstitutionFundAccountEntity fundAccount, InstitutionFundChangeRecordEntity record) {
        // 根据变动类型更新余额
        if (ChangeTypeEnum.INCREASE.getCode().equals(record.getChangeType())) {
            // 增加余额
            fundAccount.setBalance(BigDecimalUtils.add(fundAccount.getBalance(), record.getChangeAmount()));
        } else if (ChangeTypeEnum.DECREASE.getCode().equals(record.getChangeType())) {
            // 减少余额
            fundAccount.setBalance(BigDecimalUtils.sub(fundAccount.getBalance(), record.getChangeAmount()));
        }
        if (ChangeTypeEnum.FREEZE.getCode().equals(record.getChangeType())) {
            // 冻结金额
            fundAccount.setTotalFrozen(BigDecimalUtils.add(fundAccount.getTotalFrozen(), record.getChangeAmount()));
        }

        // 根据变动原因更新累计金额
        if (ChangeReasonIncreaseEnum.CASH_RECHARGE.getCode().equals(record.getChangeReason())) {
            fundAccount.setTotalCashRecharge(BigDecimalUtils.add(fundAccount.getTotalCashRecharge(), record.getChangeAmount()));
        } else if (ChangeReasonIncreaseEnum.NON_CASH_RECHARGE.getCode().equals(record.getChangeReason())) {
            fundAccount.setTotalNonCashRecharge(BigDecimalUtils.add(fundAccount.getTotalNonCashRecharge(), record.getChangeAmount()));
        } else if (ChangeReasonIncreaseEnum.COMPENSATION.getCode().equals(record.getChangeReason())) {
            fundAccount.setTotalCompensation(BigDecimalUtils.add(fundAccount.getTotalCompensation(), record.getChangeAmount()));
        } else if (ChangeReasonEnum.LISTING_FEE.getCode().equals(record.getChangeReason())
                || ChangeReasonDecreaseEnum.OTHER_FEE.getCode().equals(record.getChangeReason())) {
            // 消费类型
            fundAccount.setTotalConsumption(BigDecimalUtils.add(fundAccount.getTotalConsumption(), record.getChangeAmount()));
        } else if (ChangeReasonDecreaseEnum.REFUND.getCode().equals(record.getChangeReason())) {
            // 退款类型
            fundAccount.setTotalRefund(BigDecimalUtils.add(fundAccount.getTotalRefund(), record.getChangeAmount()));
        } else if (ChangeReasonDecreaseEnum.CANCEL.getCode().equals(record.getChangeReason())) {
            // 作废类型
            fundAccount.setTotalCancel(BigDecimalUtils.add(fundAccount.getTotalCancel(), record.getChangeAmount()));
        }
    }

    /**
     * 解冻处理
     */
    private Boolean unfreezeProcess(InstitutionFundAccountEntity fundAccount, InstitutionFundChangeRecordEntity freezeRecord, String description) {
        // 检查冻结金额
        if (BigDecimalUtils.lt(fundAccount.getTotalFrozen(), freezeRecord.getChangeAmount())) {
            throw new BusinessException("账户冻结金额不足");
        }

        // 减少冻结金额
        fundAccount.setTotalFrozen(BigDecimalUtils.sub(fundAccount.getTotalFrozen(), freezeRecord.getChangeAmount()));

        // 修改冻结记录为解冻
        freezeRecord.setChangeType(ChangeTypeEnum.UNFREEZE.getCode());
        freezeRecord.setDescription(description);
        freezeRecord.setOperator(UserThreadLocal.getUserId());
        freezeRecord.setUpdateTime(LocalDateTime.now());
        // 解冻的数据不展示  软删除
        freezeRecord.setDeleteFlag(BooleFlagEnum.YES.getCode());

        // 保存修改
        institutionFundChangeRecordService.updateById(freezeRecord);

        return institutionFundAccountService.updateById(fundAccount);
    }

    /**
     * 转扣费处理
     */
    private Boolean deductProcess(InstitutionFundAccountEntity fundAccount, InstitutionFundChangeRecordEntity freezeRecord,
                                  BigDecimal deductAmount, String description) {
        // 检查扣费金额
        if (deductAmount == null || BigDecimalUtils.le(deductAmount, BigDecimal.ZERO)) {
            throw new BusinessException("扣费金额必须大于0");
        }

        if (BigDecimalUtils.gt(deductAmount, freezeRecord.getChangeAmount())) {
            throw new BusinessException("扣费金额不能大于冻结金额");
        }
        // 减少冻结金额
        fundAccount.setTotalFrozen(BigDecimalUtils.sub(fundAccount.getTotalFrozen(), freezeRecord.getChangeAmount()));

        // 减少余额
        fundAccount.setBalance(BigDecimalUtils.sub(fundAccount.getBalance(), deductAmount));

        // 修改冻结记录为扣费
        freezeRecord.setChangeType(ChangeTypeEnum.DECREASE.getCode());
        freezeRecord.setChangeAmount(deductAmount);
        freezeRecord.setDescription(description);
        freezeRecord.setCreationMethod(BooleFlagEnum.YES.getCode());
        freezeRecord.setOperator(UserThreadLocal.getUserId());
        freezeRecord.setUpdateTime(LocalDateTime.now());

        // 保存修改
        institutionFundChangeRecordService.updateById(freezeRecord);

        // 更新累计金额
        String changeReason = freezeRecord.getChangeReason();
        if (ChangeReasonEnum.LISTING_FEE.getCode().equals(changeReason)
                || ChangeReasonDecreaseEnum.OTHER_FEE.getCode().equals(changeReason)
                || ChangeReasonFreezeEnum.OTHER_FEE.getCode().equals(changeReason)) {
            fundAccount.setTotalConsumption(BigDecimalUtils.add(fundAccount.getTotalConsumption(), deductAmount));
        } else if (ChangeReasonDecreaseEnum.REFUND.getCode().equals(changeReason)
                || ChangeReasonFreezeEnum.REFUND.getCode().equals(changeReason)) {
            fundAccount.setTotalRefund(BigDecimalUtils.add(fundAccount.getTotalRefund(), deductAmount));
        } else if (ChangeReasonDecreaseEnum.CANCEL.getCode().equals(changeReason)) {
            fundAccount.setTotalCancel(BigDecimalUtils.add(fundAccount.getTotalCancel(), deductAmount));
        }
        return institutionFundAccountService.updateById(fundAccount);
    }

    /**
     * 下载刊例费Excel文件
     *
     * @param planId 方案ID
     * @param cityId 城市ID
     * @return COS文件访问URL
     * @throws BusinessException 当数据为空或生成Excel失败时抛出
     */
    public String downloadListingFee(Integer planId, Integer cityId) {
        List<PointFeeDocument> pointFeeDocuments = pointFeeService.findPointFee(cityId, planId);
        if (CollectionUtils.isEmpty(pointFeeDocuments)) {
            throw new BusinessException("刊例费数据为空");
        }
        List<ListingFeeExportVO> listingFeeExports = pointFeeDocuments.stream().map(
                pointFeeDocument -> {
                    ListingFeeExportVO exportVO = new ListingFeeExportVO();
                    exportVO.setPlanId(pointFeeDocument.getPlanId());
                    exportVO.setCityName(pointFeeDocument.getCityName());
                    exportVO.setPlayDate(pointFeeDocument.getPlayDate());
                    exportVO.setPointCode(pointFeeDocument.getPointCode());
                    exportVO.setProjectName(pointFeeDocument.getProjectName());
                    exportVO.setPrePlayCount(pointFeeDocument.getPrePlayCount());
                    exportVO.setPlayCount(pointFeeDocument.getPlayCount());
                    exportVO.setAchievementRate(pointFeeDocument.getAchievementRate());
                    exportVO.setActualConsumption(pointFeeDocument.getActualConsumption());
                    exportVO.setPointMark(pointFeeDocument.getPointMark());
                    return exportVO;
                }).collect(Collectors.toList());
        // 需要下载的字段
        List<String> fields = Arrays.asList("planId", "cityName", "playDate", "pointCode", "projectName",
                "prePlayCount", "playCount", "achievementRate", "actualConsumption", "pointMark");
        // 过滤掉ActualConsumption为null的数据 （剔除掉的点位不展示）
        listingFeeExports = listingFeeExports.stream().filter(
                listingFeeExportVO -> listingFeeExportVO.getActualConsumption() != null).collect(Collectors.toList());

        try {
            return EasyExcelUtils.createExcelToCos("刊例费明细", listingFeeExports, fields,
                    planId + "-" + pointFeeDocuments.get(0).getCityName() + ".xlsx", null);
        } catch (Exception e) {
            throw new BusinessException("刊例费下载失败:" + e.getMessage());
        }
    }
}