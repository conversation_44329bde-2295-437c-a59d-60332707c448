package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.entity.TransferRecordEntity;
import com.coocaa.cheese.crm.vo.TransferRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 转移记录信息转换
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Mapper
public interface TransferRecordConvert extends PageableConvert<TransferRecordEntity, TransferRecordVO> {
    TransferRecordConvert INSTANCE = Mappers.getMapper(TransferRecordConvert.class);

    /**
     * Entity 转 VO
     */
    TransferRecordVO toVo(TransferRecordEntity entity);

    /**
     * Entity列表 转 VO列表
     */
    List<TransferRecordVO> entityToVO(List<TransferRecordEntity> entityList);
} 