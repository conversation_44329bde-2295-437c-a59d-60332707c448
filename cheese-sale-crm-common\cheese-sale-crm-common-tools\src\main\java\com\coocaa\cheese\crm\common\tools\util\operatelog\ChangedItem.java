package com.coocaa.cheese.crm.common.tools.util.operatelog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ChangedItem {
    /**
     * 变更的字段
     */
    private String fieldName;

    /**
     * 字段变更前的数据
     */
    private String beforeChanged;

    /**
     * 字段变更后的数据
     */
    private String afterChanged;
}
