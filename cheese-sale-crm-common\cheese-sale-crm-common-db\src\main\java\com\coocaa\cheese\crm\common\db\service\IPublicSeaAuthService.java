package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.bean.PublicSeaAuthDTO;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaAuthEntity;

/**
 * 公海打捞授权表服务接口
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
public interface IPublicSeaAuthService extends IService<PublicSeaAuthEntity> {

    /**
     * 分页查询
     *
     * @param publicSeaAuth 查询条件
     * @param page          分页对象
     * @return 分页结果
     */
    IPage<PublicSeaAuthEntity> pageList(IPage<PublicSeaAuthEntity> page, PublicSeaAuthDTO publicSeaAuth);
}