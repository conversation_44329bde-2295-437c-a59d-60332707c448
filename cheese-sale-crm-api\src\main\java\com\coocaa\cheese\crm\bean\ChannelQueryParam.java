package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
public class ChannelQueryParam {
    @Schema(description = "父渠道ID", type = "Integer", example = "0")
    private Integer parentId;

    @Schema(description = "渠道名称", type = "String", example = "个人开发")
    private String name;
}
