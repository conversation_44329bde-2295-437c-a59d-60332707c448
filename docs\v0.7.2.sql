-- cheese_sale_crm.sale_crm_public_sea_auth definition

CREATE TABLE `sale_crm_public_sea_auth`
(
    `id`             BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `biz_id`         INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '业务id',
    `remark`         VARCHAR(100)        NOT NULL DEFAULT '' COMMENT '情况说明',
    `execute_status` VARCHAR(10)         NOT NULL DEFAULT '0151-1' COMMENT '执行状态(字典0151)',
    `delete_flag`    TINYINT(1)          NOT NULL DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`        INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '创建人',
    `department_id`  VARCHAR(40)         NOT NULL DEFAULT '' COMMENT '创建人部门ID(飞书openId)',
    `create_time`    DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`       INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '操作人',
    `update_time`    DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='公海打捞授权表';


ALTER TABLE sale_comm_brand ADD parent_id int(11) unsigned NULL COMMENT '母品牌ID' AFTER id;
ALTER TABLE sale_crm_business DROP COLUMN product_line;


UPDATE sale_comm_channel
SET  create_time='1997-01-01 00:00:00', update_time='1997-01-01 00:00:00'
WHERE id=2;

ALTER TABLE sale_comm_public_sea_quota ADD auth_day int(11) unsigned DEFAULT 0 NOT NULL COMMENT '授权天数' AFTER temp_used;


DROP TABLE sale_crm_business_transfer_record;
