package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/19
 */
@Data
@Accessors(chain = true)
@Schema(name = "ProductLineUnionVO", description = "站内审批任务产品线变更关联查询返回参数")
public class ProductLineUnionVO {

    @Schema(description = "品牌Id")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "签约主体id")
    private Integer signSubjectId;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY, target = "signSubjectName")
    private Integer companyId;
    private String signSubjectName;

    @Schema(description = "产品线")
    private String productLine;
}
