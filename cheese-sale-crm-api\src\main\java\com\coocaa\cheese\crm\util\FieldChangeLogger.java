package com.coocaa.cheese.crm.util;

import com.coocaa.ad.common.util.ReflectUtils;
import com.coocaa.cheese.crm.config.annotation.LogAsChinese;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 字段变更日志工具类
 *
 * <AUTHOR>
 * @since 2025/4/3
 */
public final class FieldChangeLogger {
    /**
     * 字段值包裹开始符号
     */
    private static final String FIELD_WRAPPER_START = "(";
    /**
     * 字段值包裹结束符号
     */
    private static final String FIELD_WRAPPER_END = ")";
    /**
     * 集合开始符号
     */
    private static final String COLLECTION_START = "[";
    /**
     * 集合结束符号
     */
    private static final String COLLECTION_END = "]";
    /**
     * 变更分隔符
     */
    private static final String CHANGE_SEPARATOR = "改为";
    /**
     * 字段分隔符
     */
    private static final String FIELD_SEPARATOR = ", ";

    /**
     * 生成变更日志
     *
     * @param newObj 新对象
     * @param oldObj 旧对象
     * @return 格式化后的变更日志字符串
     */
    public static String generateChangeLog(Object newObj, Object oldObj) {
        try {
            validateInput(newObj, oldObj);
            StringBuilder log = new StringBuilder();
            processObject(newObj.getClass(), log, newObj, oldObj);
            return log.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成变更日志失败", e);
        }
    }

    /**
     * 验证输入参数
     *
     * @param newObj 新对象
     * @param oldObj 旧对象
     * @throws IllegalArgumentException 如果参数无效
     */
    private static void validateInput(Object newObj, Object oldObj) {
        if (newObj == null) {
            throw new IllegalArgumentException("新对象不能为null");
        }
        if (oldObj != null && !newObj.getClass().equals(oldObj.getClass())) {
            throw new IllegalArgumentException("新旧对象类型不一致");
        }
    }

    /**
     * 处理对象并生成变更日志
     *
     * @param targetClass 目标类
     * @param log         日志构建器
     * @param newObj      新对象
     * @param oldObj      旧对象
     */
    private static void processObject(Class<?> targetClass, StringBuilder log, Object newObj, Object oldObj) throws Exception {
        if (newObj == null) {
            return;
        }
        // 处理集合类型
        if (newObj instanceof Collection) {
            processCollection(targetClass, log, (Collection<?>) newObj, oldObj instanceof Collection ? (Collection<?>) oldObj : null);
            return;
        }
        // 处理数组类型
        if (newObj.getClass().isArray()) {
            processArray(targetClass, log, newObj, oldObj != null && oldObj.getClass().isArray() ? oldObj : null);
            return;
        }
        // 处理简单类型（基本类型+String+Java内置类）
        if (isJavaBuiltInType(newObj.getClass())) {
            handlePrimitiveChange(log, newObj, oldObj);
            return;
        }
        // 处理自定义对象
        List<String> fieldLogs = new ArrayList<>();
        Map<String, Method> readableMethods = ReflectUtils.getReadableMethods(newObj);
        // 遍历所有字段
        for (Field field : ReflectUtils.getAllFields(newObj)) {
            // 转小写是因为readableMethods这个里面获取出来的字段名是小写的
            String fieldKey = StringUtils.toRootLowerCase(field.getName());
            Method getter = readableMethods.get(fieldKey);
            if (getter != null && shouldProcessField(field)) {
                processField(getter, field, fieldLogs, newObj, oldObj);
            }
        }
        // 拼接所有字段变更日志
        if (!fieldLogs.isEmpty()) {
            appendWithSeparator(log);
            log.append(String.join(FIELD_SEPARATOR, fieldLogs));
        }
    }

    /**
     * 处理集合对象（顶级入口，非字段场景）
     *
     * @param elementType 元素类型
     * @param log         日志构建器
     * @param newCol      新集合
     * @param oldCol      旧集合
     */
    private static void processCollection(Class<?> elementType, StringBuilder log, Collection<?> newCol, Collection<?> oldCol) throws Exception {
        log.append(COLLECTION_START);
        if (oldCol != null && collectionsEqual(newCol, oldCol)) {
            // 处理变更集合
            List<String> newItems = getCollectionItems(elementType, newCol);
            List<String> oldItems = getCollectionItems(elementType, oldCol);
            log.append(String.join(FIELD_SEPARATOR, newItems))
                    .append(FIELD_WRAPPER_START)
                    .append(String.join(FIELD_SEPARATOR, oldItems))
                    .append(CHANGE_SEPARATOR)
                    .append(String.join(FIELD_SEPARATOR, newItems))
                    .append(FIELD_WRAPPER_END);
        } else {
            // 处理新增集合
            List<String> items = getCollectionItems(elementType, newCol);
            log.append(String.join(FIELD_SEPARATOR, items));
        }
        log.append(COLLECTION_END);
    }

    /**
     * 处理字段变更
     *
     * @param getter    getter方法
     * @param field     字段
     * @param fieldLogs 字段日志列表
     * @param newObj    新对象
     * @param oldObj    旧对象
     */
    private static void processField(Method getter, Field field, List<String> fieldLogs, Object newObj, Object oldObj) throws Exception {
        // 获取新旧值
        Object newValue = getter.invoke(newObj);
        Object oldValue = oldObj != null ? getter.invoke(oldObj) : null;
        String description = getFieldDescription(field);
        StringBuilder fieldLog = new StringBuilder(description).append(FIELD_WRAPPER_START);
        if (newValue instanceof Collection) {
            // 集合字段特殊处理
            processCollectionField(field, fieldLog, (Collection<?>) newValue,
                    oldValue instanceof Collection ? (Collection<?>) oldValue : null);
        } else if (oldValue != null && !Objects.equals(newValue, oldValue)) {
            // 普通字段：新值(旧值改为新值)
            fieldLog.append(newValue)
                    .append(FIELD_WRAPPER_START)
                    .append(oldValue)
                    .append(CHANGE_SEPARATOR)
                    .append(newValue)
                    .append(FIELD_WRAPPER_END);
        } else {
            // 无变更情况
            fieldLog.append(newValue);
        }
        fieldLog.append(FIELD_WRAPPER_END);
        fieldLogs.add(fieldLog.toString());
    }

    /**
     * 处理集合字段
     *
     * @param field  字段
     * @param sb     字符串构建器
     * @param newCol 新集合
     * @param oldCol 旧集合
     */
    private static void processCollectionField(Field field, StringBuilder sb, Collection<?> newCol, Collection<?> oldCol)
            throws Exception {
        Class<?> elementType = getCollectionElementType(field);
        List<String> newItems = getCollectionItems(elementType, newCol);
        // 先输出新集合内容
        sb.append(COLLECTION_START)
                .append(String.join(FIELD_SEPARATOR, newItems))
                .append(COLLECTION_END);
        // 如果有旧集合且内容不同，输出变更对比
        if (oldCol != null && !collectionsEqual(newCol, oldCol)) {
            List<String> oldItems = getCollectionItems(elementType, oldCol);
            sb.append(FIELD_WRAPPER_START)
                    .append(COLLECTION_START)
                    .append(String.join(FIELD_SEPARATOR, oldItems))
                    .append(COLLECTION_END)
                    .append(CHANGE_SEPARATOR)
                    .append(COLLECTION_START)
                    .append(String.join(FIELD_SEPARATOR, newItems))
                    .append(COLLECTION_END)
                    .append(FIELD_WRAPPER_END);
        }
    }

    /**
     * 处理数组对象
     *
     * @param componentType 数组元素类型
     * @param log           日志构建器
     * @param newArray      新数组
     * @param oldArray      旧数组
     */
    private static void processArray(Class<?> componentType, StringBuilder log, Object newArray, Object oldArray) throws Exception {
        log.append(COLLECTION_START);
        List<String> newItems = getArrayItems(componentType, newArray);
        log.append(String.join(FIELD_SEPARATOR, newItems));
        if (oldArray != null && !arraysEqual(newArray, oldArray)) {
            List<String> oldItems = getArrayItems(componentType, oldArray);
            log.append(FIELD_WRAPPER_START)
                    .append(String.join(FIELD_SEPARATOR, oldItems))
                    .append(CHANGE_SEPARATOR)
                    .append(String.join(FIELD_SEPARATOR, newItems))
                    .append(FIELD_WRAPPER_END);
        }
        log.append(COLLECTION_END);
    }

    /**
     * 获取集合元素的字符串表示
     *
     * @param elementType 元素类型
     * @param collection  集合
     * @return 元素字符串列表
     */
    private static List<String> getCollectionItems(Class<?> elementType, Collection<?> collection) throws Exception {
        List<String> items = new ArrayList<>();
        for (Object item : collection) {
            StringBuilder itemLog = new StringBuilder();
            processObject(elementType, itemLog, item, null);
            items.add(itemLog.toString());
        }
        return items;
    }

    /**
     * 获取数组元素的字符串表示
     *
     * @param componentType 数组元素类型
     * @param array         数组
     * @return 元素字符串列表
     */
    private static List<String> getArrayItems(Class<?> componentType, Object array) throws Exception {
        List<String> items = new ArrayList<>();
        int length = Array.getLength(array);
        for (int i = 0; i < length; i++) {
            Object item = Array.get(array, i);
            StringBuilder itemLog = new StringBuilder();
            processObject(componentType, itemLog, item, null);
            items.add(itemLog.toString());
        }
        return items;
    }

    /**
     * 获取集合元素的类型
     *
     * @param field 字段
     * @return 元素类型
     */
    private static Class<?> getCollectionElementType(Field field) {
        // 获取字段的泛型类型
        Type genericType = field.getGenericType();
        // 如果泛型类型不是ParameterizedType，或者没有实际类型参数，返回Object.class
        if (!(genericType instanceof ParameterizedType)) {
            return Object.class;
        }
        // 获取泛型类型的实际类型参数
        Type[] actualTypes = ((ParameterizedType) genericType).getActualTypeArguments();
        if (actualTypes.length == 0) {
            return Object.class;
        }
        // 获取第一个实际类型参数
        Type type = actualTypes[0];
        if (type instanceof Class) {
            // 如果实际类型参数是Class类型，直接返回
            return (Class<?>) type;
        }
        if (type instanceof ParameterizedType) {
            // 如果实际类型参数是ParameterizedType类型，返回其原始类型
            return (Class<?>) ((ParameterizedType) type).getRawType();
        }
        // 如果实际类型参数既不是Class类型也不是ParameterizedType类型，返回Object.class
        return Object.class;
    }

    /**
     * 处理基本类型变更
     *
     * @param log    日志构建器
     * @param newVal 新值
     * @param oldVal 旧值
     */
    private static void handlePrimitiveChange(StringBuilder log, Object newVal, Object oldVal) {
        if (oldVal != null && !newVal.equals(oldVal)) {
            appendWithSeparator(log);
            log.append(newVal)
                    .append(FIELD_WRAPPER_START)
                    .append(oldVal)
                    .append(CHANGE_SEPARATOR)
                    .append(newVal)
                    .append(FIELD_WRAPPER_END);
        } else if (oldVal == null) {
            appendWithSeparator(log);
            log.append(newVal);
        }
    }

    /**
     * 比较两个集合是否相等
     *
     * @param col1 集合1
     * @param col2 集合2
     * @return 是否相等
     */
    private static boolean collectionsEqual(Collection<?> col1, Collection<?> col2) {
        if (col1 == col2) {
            return true;
        }
        if (col1 == null || col2 == null) {
            return false;
        }
        if (col1.size() != col2.size()) {
            return false;
        }
        Iterator<?> it1 = col1.iterator();
        Iterator<?> it2 = col2.iterator();
        while (it1.hasNext() && it2.hasNext()) {
            if (!Objects.equals(it1.next(), it2.next())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 比较两个数组是否相等
     *
     * @param array1 数组1
     * @param array2 数组2
     * @return 是否相等
     */
    private static boolean arraysEqual(Object array1, Object array2) {
        if (array1 == array2) {
            return true;
        }
        if (array1 == null || array2 == null) {
            return false;
        }
        if (Array.getLength(array1) != Array.getLength(array2)) {
            return false;
        }
        for (int i = 0; i < Array.getLength(array1); i++) {
            if (!Objects.equals(Array.get(array1, i), Array.get(array2, i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断字段是否需要处理
     *
     * @param field 字段
     * @return 是否需要处理
     */
    private static boolean shouldProcessField(Field field) {
        LogAsChinese chinese = field.getAnnotation(LogAsChinese.class);
        Schema schema = field.getAnnotation(Schema.class);
        return chinese != null && schema != null && StringUtils.isNotBlank(schema.description());
    }

    /**
     * 获取字段描述
     *
     * @param field 字段
     * @return 描述信息
     */
    private static String getFieldDescription(Field field) {
        return field.getAnnotation(Schema.class).description();
    }

    /**
     * 添加分隔符
     *
     * @param sb 字符串构建器
     */
    private static void appendWithSeparator(StringBuilder sb) {
        sb.append(!sb.isEmpty() ? FIELD_SEPARATOR : "");
    }

    /**
     * 判断是否是Java内置类型
     *
     * @param type 类型
     * @return 是否是内置类型
     */
    private static boolean isJavaBuiltInType(Class<?> type) {
        // 判断传入的类型是否是基本数据类型（如int, char等）
        return type.isPrimitive()
                // 判断传入的类型是否是String类型
                || type.equals(String.class)
                // 判断传入的类型是否属于java包或其子包
                || (type.getPackage() != null
                // 获取类型的包名，并判断是否以"java."开头
                && type.getPackage().getName().startsWith("java."));
    }
}