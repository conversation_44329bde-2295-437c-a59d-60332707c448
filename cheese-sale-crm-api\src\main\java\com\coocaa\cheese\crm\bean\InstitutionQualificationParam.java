package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 机构资质参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构资质参数")
public class InstitutionQualificationParam {
    @Schema(description = "机构账户ID", type = "Integer", example = "1")
    @NotNull(message = "机构账户ID不能为空")
    private Integer institutionId;

    @Schema(description = "资质名称", type = "String", example = "营业执照")
    @NotBlank(message = "资质名称不能为空")
    @Size(max = 30, message = "资质名称长度不能超过30个字符")
    @Pattern(regexp = "^\\S+$", message = "资质名称不能包含空格")
    private String qualificationName;

    @Schema(description = "备注说明", type = "String", example = "长期有效")
    @Size(message = "备注说明长度必须小于50个字符", max = 50)
    private String description;

    @Schema(description = "文件地址", type = "String", example = "http://www.test.com")
    @NotBlank(message = "文件地址不能为空")
    private String fileUrl;

} 