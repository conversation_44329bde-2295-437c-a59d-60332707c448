package com.coocaa.cheese.crm.common.tools.util;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;

import java.util.stream.IntStream;


/**
 * 字符串工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-15
 */
@Slf4j
public final class StringUtils extends org.apache.commons.lang3.StringUtils {
    /**
     * 转换成布尔值
     */
    public static boolean toBoolean(String value) {
        return org.apache.commons.lang3.StringUtils.equalsIgnoreCase(value, "true")
                || org.apache.commons.lang3.StringUtils.equalsIgnoreCase(value, "on")
                || org.apache.commons.lang3.StringUtils.equalsIgnoreCase(value, "1");
    }

    /**
     * 取汉语拼音的首字母
     *
     * @param chinese 汉字
     * @return 首字母
     */
    public static String getPinyinInitial(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return EMPTY;
        }

        String firstWord = chinese.trim().substring(0, 1);
        String pinyin = chineseToPinyin(firstWord, true);
        return isBlank(pinyin) ? EMPTY : pinyin.substring(0, 1).toUpperCase();
    }


    /**
     * 获取拼音首字母
     *
     * @param chinese 汉字
     * @param length  保留长度
     * @return 拼音首字母
     */
    public static String getPinyinInitial(String chinese, int length) {
        String fistChars = chineseToPinyin(chinese, true);
        if (StringUtils.isBlank(fistChars)) {
            return StringUtils.EMPTY;
        }

        // 转换成大写
        fistChars = fistChars.toUpperCase();

        // 只保留指定位数
        if (fistChars.length() > length) {
            return fistChars.substring(0, length);
        }
        return fistChars;
    }

    /**
     * 汉字转换位汉语拼音，英文字符不变
     *
     * @param chinese       汉字
     * @param firstCharOnly 只取首字母
     * @return 拼音
     */
    public static String chineseToPinyin(String chinese, boolean firstCharOnly) {
        if (StringUtils.isBlank(chinese)) {
            return StringUtils.EMPTY;
        }

        char[] chars = chinese.toCharArray();
        HanyuPinyinOutputFormat formatter = new HanyuPinyinOutputFormat();
        formatter.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        formatter.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        StringBuilder pinyinBuilder = new StringBuilder();
        IntStream.range(0, chars.length).forEach(idx -> {
            if (chars[idx] > 128) {
                try {
                    String pinyin = PinyinHelper.toHanyuPinyinStringArray(chars[idx], formatter)[0];
                    pinyinBuilder.append(firstCharOnly ? pinyin.charAt(0) : pinyin);
                } catch (Exception ex) {
                    log.warn(ex.getMessage());
                }
            } else {
                pinyinBuilder.append(chars[idx]);
            }
        });
        return pinyinBuilder.toString();
    }
}
