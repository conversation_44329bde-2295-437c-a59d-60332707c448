package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.BusinessH5QueryParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.vo.BusinessH5VO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商机信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BusinessH5Convert extends PageableConvert<BusinessEntity, BusinessH5VO> {
    BusinessH5Convert INSTANCE = Mappers.getMapper(BusinessH5Convert.class);

    /**
     * Entity 转 VO
     */
    BusinessH5VO toVo(BusinessEntity entity);

    /**
     * VO 转 Entity
     */
    BusinessEntity toEntity(BusinessH5VO vo);

    /**
     * Param 转 Entity
     */
    BusinessEntity toEntity(BusinessH5QueryParam param);
} 