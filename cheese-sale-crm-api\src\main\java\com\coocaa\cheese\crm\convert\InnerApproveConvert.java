package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveApplyVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveNodeVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveTaskVO;
import com.coocaa.cheese.crm.vo.InnerApproveApplyPageVO;
import com.coocaa.cheese.crm.vo.InnerApproveDetailVO;
import com.coocaa.cheese.crm.vo.InnerApprovePkChallengePageVO;
import com.coocaa.cheese.crm.vo.InnerApprovePublicSeaAuthPageVO;
import com.coocaa.cheese.crm.vo.InnerApproveTaskPageVO;
import com.coocaa.cheese.crm.vo.InnerExamineApproveVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 站内审批数据转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Mapper
public interface InnerApproveConvert extends PageableConvert<InnerApproveTaskVO, InnerApproveTaskPageVO> {
    InnerApproveConvert INSTANCE = Mappers.getMapper(InnerApproveConvert.class);

    /**
     * vo 转 VO
     */
    InnerApproveTaskPageVO toVo(InnerApproveTaskVO vo);

    /**
     * dto 转 VO
     */
    InnerApproveDetailVO toVo(InnerApproveDetailDTO dto);

    /**
     * vo 转 VO
     */
    InnerApproveApplyPageVO toVo(InnerApproveApplyVO vo);

    /**
     * vo 转 VO
     */
    InnerApprovePublicSeaAuthPageVO toPageVo(InnerApproveApplyVO vo);

    /**
     * vo 转 VO
     */
    InnerApprovePublicSeaAuthPageVO toPageVo(InnerApproveTaskVO vo);

    /**
     * vo 转 VO
     */
    InnerExamineApproveVO toVo(InnerApproveNodeVO vo);

    /**
     * vo 转 VO
     */
    InnerApprovePkChallengePageVO toPkPageVo(InnerApproveTaskVO row);

    InnerApprovePkChallengePageVO toPkPageVo(InnerApproveApplyVO row);

}