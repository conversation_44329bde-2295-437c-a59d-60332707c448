package com.coocaa.cheese.crm.service;

import com.coocaa.cheese.crm.common.db.service.IProductLineSignSubjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 产品线签约主体关系表 业务实现类
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ProductLineSignSubjectService {

    private final IProductLineSignSubjectService productLineSignSubjectService;
}