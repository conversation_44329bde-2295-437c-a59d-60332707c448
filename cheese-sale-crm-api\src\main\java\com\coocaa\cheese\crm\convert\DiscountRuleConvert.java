package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.DiscountRuleParam;
import com.coocaa.cheese.crm.common.db.entity.DiscountRuleEntity;
import com.coocaa.cheese.crm.vo.DiscountRuleDetailVO;
import com.coocaa.cheese.crm.vo.DiscountRuleVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 折扣规则信息转换
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Mapper
public interface DiscountRuleConvert extends PageableConvert<DiscountRuleEntity, DiscountRuleVO> {
    DiscountRuleConvert INSTANCE = Mappers.getMapper(DiscountRuleConvert.class);
    /**
     * Entity 转 DetailVO
     */
    DiscountRuleDetailVO toDetailVo(DiscountRuleEntity entity);

    /**
     * Entity列表 转 VO列表
     */
    List<DiscountRuleVO> entityToVO(List<DiscountRuleEntity> entityList);

    /**
     * Param 转 Entity
     */
    DiscountRuleEntity toEntity(DiscountRuleParam param);
} 