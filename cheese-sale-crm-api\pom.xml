<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>cheese-sale-crm</artifactId>
        <groupId>com.coocaa.ad</groupId>
        <version>1.0.0</version>
    </parent>
    <artifactId>cheese-sale-crm-api</artifactId>
    <version>1.0.0</version>
    <name>cheese-sale-crm-api</name>
    <description>销售-CRM系统</description>

    <dependencies>
        <!-- Spring Cloud 相关依赖 -->
        <!--用于加载bootstrap配置文件-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- 微服务相关依赖 -->
        <!--OpenFeign 服务调用-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!--Spring Cloud LoadBalancer 负载均衡-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <!--Nacos 配置中心-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--Nacos 服务注册与发现-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- 监控相关依赖 -->
        <!--Spring Boot Actuator 监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!--Prometheus 监控指标收集-->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <!-- 工具类库 -->
        <!--Apache Commons Lang3 工具包-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- 开发工具依赖 -->
        <!--Lombok 注解处理器-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!--MapStruct 对象映射工具-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <!-- API文档相关 -->
        <!--SpringDoc OpenAPI文档-->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <!--Knife4j API文档UI-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>

        <!-- 自定义依赖 -->
        <dependency>
            <groupId>com.coocaa.ad</groupId>
            <artifactId>cheese-common-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coocaa.ad</groupId>
            <artifactId>cheese-common-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coocaa.ad</groupId>
            <artifactId>cheese-data-permission-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coocaa.ad</groupId>
            <artifactId>cheese-translate-starter</artifactId>
        </dependency>
        <!-- 项目内部模块依赖 -->
        <!--数据库操作模块-->
        <dependency>
            <groupId>com.coocaa.ad</groupId>
            <artifactId>cheese-sale-crm-common-db</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coocaa.ad</groupId>
            <artifactId>cheese-sale-crm-common-tools</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <!--默认激活开发配置，使用index-dev.properties来替换实际的文件key-->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <filters.env>dev</filters.env>
            </properties>
        </profile>
        <!-- 测试环境配置 -->
        <profile>
            <id>release</id>
            <properties>
                <filters.env>release</filters.env>
            </properties>
        </profile>
        <!-- 生产环境配置 -->
        <profile>
            <id>master</id>
            <properties>
                <filters.env>master</filters.env>
            </properties>
        </profile>
    </profiles>


    <build>
        <!--指定下面的目录为资源文件-->
        <resources>
            <!--设置自动替换-->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/**</include>
                </includes>
                <excludes>
                    <exclude>**/*.docx</exclude>
                    <exclude>**/*.doc</exclude>
                    <exclude>**/*.pdf</exclude>
                    <exclude>**/*.ttf</exclude>
                    <exclude>**/*.ttc</exclude>
                    <exclude>**/*.jar</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.docx</include>
                    <include>**/*.doc</include>
                    <include>**/*.pdf</include>
                    <include>**/*.ttf</include>
                    <include>**/*.ttc</include>
                    <include>**/*.jar</include>
                </includes>
            </resource>
        </resources>
        <filters>
            <filter>configs/${filters.env}/app.properties</filter>
        </filters>

        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
