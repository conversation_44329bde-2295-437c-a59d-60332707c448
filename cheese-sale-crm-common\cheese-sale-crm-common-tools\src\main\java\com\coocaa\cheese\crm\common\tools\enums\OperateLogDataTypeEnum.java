package com.coocaa.cheese.crm.common.tools.enums;

/**
 * 操作日志数据类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-24
 */
public enum OperateLogDataTypeEnum {
    INNER_APPROVE(1, "站内审批"),
    BUSINESS(2, "商机");

    private final int code;
    private final String description;

    OperateLogDataTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
