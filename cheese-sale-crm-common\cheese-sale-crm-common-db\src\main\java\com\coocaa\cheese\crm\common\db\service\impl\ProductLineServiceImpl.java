package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.bean.ProductLineUnionDTO;
import com.coocaa.cheese.crm.common.db.bean.SignSubjectProductLineDTO;
import com.coocaa.cheese.crm.common.db.entity.ProductLineEntity;
import com.coocaa.cheese.crm.common.db.mapper.ProductLineMapper;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品线表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ProductLineServiceImpl extends ServiceImpl<ProductLineMapper, ProductLineEntity> implements IProductLineService {

    @Override
    public List<String> queryProductLineBySignSubjectId(Integer signSubjectId) {
        return getBaseMapper().queryProductLineBySignSubjectId(signSubjectId);
    }

    @Override
    public List<ProductLineUnionDTO> queryInBrandUnionProductLine(Integer brandId) {
        return getBaseMapper().queryInBrandUnionProductLine(brandId);
    }

    @Override
    public List<ProductLineUnionDTO> queryOutBrandUnionProductLine(List<Integer> companyIds) {
        return getBaseMapper().queryOutBrandUnionProductLine(companyIds);
    }

    @Override
    public List<SignSubjectProductLineDTO> querySignSubjectProductLine(List<Integer> bizIds) {
        return getBaseMapper().querySignSubjectProductLine(bizIds);
    }
}