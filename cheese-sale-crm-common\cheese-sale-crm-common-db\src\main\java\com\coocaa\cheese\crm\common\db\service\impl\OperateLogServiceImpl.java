package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.OperateLogEntity;
import com.coocaa.cheese.crm.common.db.mapper.OperateLogMapper;
import com.coocaa.cheese.crm.common.db.service.IOperateLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class OperateLogServiceImpl extends ServiceImpl<OperateLogMapper, OperateLogEntity> implements IOperateLogService {

}
