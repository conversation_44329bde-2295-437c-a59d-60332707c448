package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.PublicSeaAuthParam;
import com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.PublicSeaAuthDTO;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaAuthEntity;
import com.coocaa.cheese.crm.vo.InnerApprovePublicSeaAuthDetailVO;
import com.coocaa.cheese.crm.vo.PublicSeaAuthVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 公海打捞授权表 信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-6-17
 */
@Mapper
public interface PublicSeaAuthConvert extends PageableConvert<PublicSeaAuthEntity, PublicSeaAuthVO> {
    PublicSeaAuthConvert INSTANCE = Mappers.getMapper(PublicSeaAuthConvert.class);

    /**
     * Entity 转 VO
     */
    PublicSeaAuthVO toVo(PublicSeaAuthEntity entity);

    /**
     * 转换查询参数
     */
    PublicSeaAuthDTO toDto(PublicSeaAuthParam param);

    /**
     * 转换查询参数
     */
    InnerApprovePublicSeaAuthDetailVO dtoToVo(InnerApproveDetailDTO dto);

    /**
     * VO 转 Entity
     */
    PublicSeaAuthEntity toEntity(PublicSeaAuthVO vo);

    /**
     * Param 转 Entity
     */
    PublicSeaAuthEntity toEntity(PublicSeaAuthParam param);
}