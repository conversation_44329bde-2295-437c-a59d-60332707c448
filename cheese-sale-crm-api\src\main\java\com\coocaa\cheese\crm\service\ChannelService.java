package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.ChannelParam;
import com.coocaa.cheese.crm.bean.ChannelQueryParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.ChannelEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IChannelService;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.convert.ChannelConvert;
import com.coocaa.cheese.crm.vo.ChannelVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 渠道管理服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ChannelService {
    private static final int FIRST_LEVEL_PARENT_ID = 0;

    private final IChannelService channelService;
    private final IBusinessService businessService;

    /**
     * 查询渠道列表
     *
     * @param firstLevel 是否查询一级渠道列表
     * @return 渠道列表
     */
    @AutoTranslate
    public List<ChannelVO> listByLevel(boolean firstLevel) {
        LambdaQueryWrapper<ChannelEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByDesc(ChannelEntity::getUpdateTime);
        queryWrapper.eq(ChannelEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());
        if (firstLevel) {
            queryWrapper.eq(ChannelEntity::getParentId, FIRST_LEVEL_PARENT_ID);
        } else {
            queryWrapper.gt(ChannelEntity::getParentId, FIRST_LEVEL_PARENT_ID);
        }

        return channelService.list(queryWrapper).stream()
                .map(ChannelConvert.INSTANCE::toVo)
                .toList();
    }

    /**
     * 渠道列表(不分页)
     *
     * @param name  渠道名称
     * @param alias 是否查询别名
     * @return 渠道列表
     */
    public List<CodeNameVO> listChannels(String name, boolean alias) {
        List<ChannelEntity> channels = channelService.lambdaQuery()
                .select(ChannelEntity::getId, ChannelEntity::getParentId, ChannelEntity::getName, ChannelEntity::getNickName)
                .gt(ChannelEntity::getParentId, FIRST_LEVEL_PARENT_ID)
                .eq(ChannelEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .like(StringUtils.isNotBlank(name), ChannelEntity::getName, name)
                .orderByDesc(ChannelEntity::getUpdateTime)
                .list();
        if (CollectionUtils.isEmpty(channels)) {
            return Collections.emptyList();
        }

        // 查询上级名称
        Map<Integer, String> parentMap = channelService.lambdaQuery()
                .select(ChannelEntity::getId, ChannelEntity::getName)
                .in(ChannelEntity::getId, channels.stream().map(ChannelEntity::getParentId).collect(Collectors.toSet()))
                .list().stream()
                .collect(Collectors.toMap(ChannelEntity::getId, ChannelEntity::getName, (o, n) -> n));

        return channels.stream()
                .map(channel -> CodeNameVO.builder()
                        .id(channel.getId())
                        .name(String.format("%s(%s)", alias ? channel.getNickName() : channel.getName(), parentMap.get(channel.getParentId())))
                        .build())
                .toList();
    }

    /**
     * 根据渠道ID查询列表
     *
     * @param ids 渠道ID列表
     * @return 渠道列表
     */
    public List<CodeNameVO> listByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        Set<Integer> uniqueIds = ids.stream().filter(Objects::nonNull).filter(id -> id > 0).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(uniqueIds)) {
            return Collections.emptyList();
        }

        return channelService.lambdaQuery()
                .select(ChannelEntity::getId, ChannelEntity::getName)
                .eq(ChannelEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(ChannelEntity::getId, uniqueIds)
                .list().stream()
                .map(item -> CodeNameVO.builder().id(item.getId()).name(item.getName()).build())
                .toList();
    }

    /**
     * 渠道列表(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的渠道列表
     */
    @AutoTranslate
    public PageResponseVO<ChannelVO> pageList(@RequestBody PageRequestVO<ChannelQueryParam> pageRequest) {
        ChannelQueryParam queryParam = pageRequest.getQuery();
        LambdaQueryWrapper<ChannelEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByDesc(ChannelEntity::getUpdateTime);
        queryWrapper.eq(ChannelEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());
        queryWrapper.eq(ChannelEntity::getLockFlag, BooleFlagEnum.NO.getCode());
        if (Objects.nonNull(queryParam.getParentId())) {
            queryWrapper.eq(ChannelEntity::getParentId, queryParam.getParentId());
        }
        if (StringUtils.isNotBlank(queryParam.getName())) {
            queryWrapper.and(i -> i.like(ChannelEntity::getName, queryParam.getName())
                    .or().like(ChannelEntity::getNickName, queryParam.getName()));
        }

        return ChannelConvert.INSTANCE.toPageResponse(channelService.page(getPage(pageRequest), queryWrapper));
    }

    /**
     * 渠道详情
     *
     * @param id 渠道ID
     * @return 渠道详情
     */
    @AutoTranslate
    public ChannelVO getDetail(Integer id) {
        return Optional.ofNullable(channelService.getById(id)).map(ChannelConvert.INSTANCE::toVo).orElse(null);
    }

    /**
     * 渠道新增或修改
     *
     * @param id    渠道ID (为空表示新增)
     * @param param 渠道信息
     * @return true: 析增或修改成功
     */
    public boolean createOrUpdate(Integer id, ChannelParam param) {
        // 检查是否已存在
        if (isExist(id, param.getName())) {
            throw new BusinessException(String.format("已存在同名渠道:%s", param.getName()));
        }

        if (isExist(id, param.getNickName())) {
            throw new BusinessException(String.format("已存在同名渠道:%s", param.getNickName()));
        }

        // 转换实体
        ChannelEntity entity = ChannelConvert.INSTANCE.toEntity(param);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());

        // 新增或修改
        if (Objects.isNull(id)) {
            return channelService.save(entity);
        } else {
            entity.setId(id);
            return channelService.updateById(entity);
        }
    }

    /**
     * 渠道删除
     *
     * @param id 渠道ID
     * @return true: 删除成功
     */
    public boolean delete(Integer id) {
        // 检查是否存在
        ChannelEntity entity = Optional.ofNullable(channelService.getById(id))
                .orElseThrow(() -> new BusinessException("渠道不存在，删除失败"));

        if (BooleFlagEnum.isYes(entity.getLockFlag())) {
            throw new BusinessException("系统配置渠道，禁止删除");
        }

        // 检查是否有下级
        if (channelService.lambdaQuery()
                .eq(ChannelEntity::getParentId, id)
                .eq(ChannelEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .exists()) {
            throw new BusinessException("渠道已创建下级渠道，禁止删除");
        }

        // 已关联商机
        if (businessService.lambdaQuery()
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getChannelId, id).exists()) {
            throw new BusinessException("该投渠道已关联商机，禁止删除");
        }


        return channelService.lambdaUpdate()
                .set(ChannelEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(ChannelEntity::getId, id)
                .update();
    }

    /**
     * 检查名称是否重复
     *
     * @param id   渠道ID, 修改时排除自己
     * @param name 渠道名称或别名
     * @return true: 重复
     */
    private boolean isExist(Integer id, String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }

        return channelService.lambdaQuery()
                .select(ChannelEntity::getName)
                .eq(ChannelEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .ne(Objects.nonNull(id), ChannelEntity::getId, id)
                .and(condition -> condition.eq(ChannelEntity::getName, name).or().eq(ChannelEntity::getNickName, name))
                .exists();
    }

    /**
     * 获取分页对象
     */
    private Page<ChannelEntity> getPage(PageRequestVO<?> pageRequest) {
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE)
        );
    }
}
