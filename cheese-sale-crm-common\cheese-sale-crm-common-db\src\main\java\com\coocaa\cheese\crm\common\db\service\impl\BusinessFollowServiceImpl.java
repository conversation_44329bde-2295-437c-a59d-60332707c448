package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.BusinessFollowEntity;
import com.coocaa.cheese.crm.common.db.mapper.BusinessFollowMapper;
import com.coocaa.cheese.crm.common.db.service.IBusinessFollowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 商机跟进服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessFollowServiceImpl
        extends ServiceImpl<BusinessFollowMapper, BusinessFollowEntity>
        implements IBusinessFollowService {
} 