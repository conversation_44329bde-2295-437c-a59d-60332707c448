package com.coocaa.cheese.crm.handler.message;

import com.coocaa.ad.common.core.redission.model.DelayedMessage;
import com.coocaa.cheese.crm.common.tools.enums.DelayQueueEnum;

/**
 * 延迟消息处理器接口
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
public interface MessageHandler {
    /**
     * 获取队列类型
     *
     * @return 队列枚举
     */
    DelayQueueEnum getBizType();

    /**
     * 处理消息
     *
     * @param message 延迟消息
     */
    void handle(DelayedMessage message);
} 