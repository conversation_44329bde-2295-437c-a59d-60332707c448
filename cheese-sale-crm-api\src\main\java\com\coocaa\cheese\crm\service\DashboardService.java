package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.DashboardCustomerReportQueryParam;
import com.coocaa.cheese.crm.common.db.bean.DashboardCustomerReportSearchDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveOpinionTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.convert.AdvertisingSubjectConvert;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import com.coocaa.cheese.crm.rpc.bean.UserDataHighestParam;
import com.coocaa.cheese.crm.rpc.vo.DepartmentVO;
import com.coocaa.cheese.crm.rpc.vo.ResourceVO;
import com.coocaa.cheese.crm.rpc.vo.UserVO;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportCardVO;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportDepartmentDetailVO;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportDetailVO;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportSearchVO;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportUserVO;
import com.coocaa.cheese.crm.vo.DashboardWeekVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 数据看板服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-09
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DashboardService {
    @Value("${dashboard.departmentIds:od-65972cb6ebeed2905b72e9748dbe994d,od-4a9796f299c0452673fef3d76cdcac6c}")
    private String departmentIds;

    private final IInnerApproveService innerApproveService;
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final IBusinessService businessService;
    private final IAdvertisingSubjectService advertisingSubjectService;
    private final ProductLineService productLineService;

    /**
     * 数据看板客户报备顶部数据
     *
     * @param code 数据资源code
     * @return 客户报备顶部数据列表
     */
    public List<DashboardWeekVO> top(String code) {
        // 1. 获取输入日期所在周的周日
        LocalDate sunday = LocalDate.now().with(DayOfWeek.SUNDAY);

        // 2. 向前推7周(6周前的周一 + 当前周)
        LocalDate monday7WeeksAgo = sunday.minusWeeks(Constants.NUMBER_SIX).with(DayOfWeek.MONDAY);

        // 3. 获取当前日期所在周以及前6周的日期信息
        List<InnerApproveEntity> approves =
                getAllApproveDataByType(monday7WeeksAgo, sunday, code,
                        StationTargetEnum.CUSTOMER_REPORT.getCode(), InnerApproveOpinionTypeEnum.AGREE.getCode());
        return calculateRecent7Weeks(LocalDate.now(), approves);
    }

    /**
     * 数据看板客户报备底部统计卡片
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param code 数据资源code
     * @return 客户报备底部统计卡片列表数据
     */
    @AutoTranslate
    public List<DashboardCustomerReportCardVO> card(LocalDate startTime, LocalDate endTime, String code) {
        List<DashboardCustomerReportCardVO> cardList = new ArrayList<>();
        // 获取自己数据权限的所有数据
        List<InnerApproveEntity> approves = getAllApproveDataByType(startTime, endTime, code,
                                                                    StationTargetEnum.CUSTOMER_REPORT.getCode(), null);
        if (CollectionUtils.isEmpty(approves)) {
            return Collections.emptyList();
        }

        // 获取所有主体id
        List<Integer> bizIds = approves.stream()
                .map(InnerApproveEntity::getBizId)
                .map(Long::intValue)
                .toList();

        // 获取数据权限下的所有商机
        List<BusinessEntity> businesses = getBusinessData(bizIds);

        if (StringUtils.isNotBlank(departmentIds)) {
            List<String> departmentIdList = List.of(departmentIds.split(Constants.COMMA));
            for (String departmentId : departmentIdList) {
                // 获取部门下的所有子部门的id
                List<String> subDepartmentIds = getDepartmentIds(departmentId);
                List<InnerApproveEntity> subApproves = approves.stream()
                        .filter(a -> subDepartmentIds.contains(a.getDepartmentId()))
                        .toList();
                if (CollectionUtils.isNotEmpty(subApproves)) {
                    // 签约主体id和创建人id
                    Map<Integer, Integer> subBizIdAndCreator = subApproves.stream()
                            .collect(Collectors.toMap(
                                    e -> e.getBizId().intValue(),
                                    InnerApproveEntity::getCreator));
                    buildCard(cardList, subApproves, businesses, subBizIdAndCreator, departmentId);
                    // 移除包含的数据
                    approves.removeAll(subApproves);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(approves)) {
            Map<Integer, Integer> subBizIdAndCreator = approves.stream()
                    .collect(Collectors.toMap(
                            e -> e.getBizId().intValue(),
                            InnerApproveEntity::getCreator
                    ));
            buildCard(cardList, approves, businesses, subBizIdAndCreator, null);
        }
        return cardList;
    }

    /**
     * 组装卡片数据
     *
     * @param cardList 卡片数据列表
     * @param approves approves 审批数据列表
     * @param businesses 商机数据列表
     * @param subBizIdAndCreator 签约主体id和创建人id
     */
    private void buildCard(List<DashboardCustomerReportCardVO> cardList,
                           List<InnerApproveEntity> approves,
                           List<BusinessEntity> businesses,
                           Map<Integer, Integer> subBizIdAndCreator,
                           String departmentId) {
        DashboardCustomerReportCardVO card = new DashboardCustomerReportCardVO();
        if (StringUtils.isNotBlank((departmentId))) {
            card.setDepartmentId(departmentId);
        }
        card.setApplyCount(approves.size());
        card.setPendingCount((int) approves.stream().filter(
                a -> InnerApproveOpinionTypeEnum.PENDING.getCode().equals(a.getResult())).count());
        card.setApprovedCount((int) approves.stream().filter(
                a -> InnerApproveOpinionTypeEnum.AGREE.getCode().equals(a.getResult())).count());
        if (CollectionUtils.isNotEmpty(businesses)) {
            // 申请人id和商机创建人id相同的才进入商机统计
            card.setBusinessCount((int) businesses.stream().filter(
                    b -> subBizIdAndCreator.containsKey(b.getAdvertisingSubjectId())
                            && Objects.equals(b.getCreator(), subBizIdAndCreator.get(b.getAdvertisingSubjectId()))).count());
        } else {
            card.setBusinessCount(0);
        }
        cardList.add(card);
    }

    /**
     * 数据看板客户报备部门详情
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param code 数据资源code
     * @param departmentId 部门ID
     * @return 客户报备部门详情数据
     */
    @AutoTranslate
    public DashboardCustomerReportDepartmentDetailVO departmentDetail(LocalDate startTime,
                                                                      LocalDate endTime,
                                                                      String code,
                                                                      String departmentId) {
        DashboardCustomerReportDepartmentDetailVO detail = new DashboardCustomerReportDepartmentDetailVO();
        // 设置年份和周数
        WeekFields weekFields = WeekFields.ISO;
        detail.setYear(startTime.getYear());
        detail.setWeekNumber(startTime.get(weekFields.weekOfWeekBasedYear()));

        // 获取审批数据
        List<InnerApproveEntity> approves = getAllApproveDataByType(startTime, endTime, code,
                                                                    StationTargetEnum.CUSTOMER_REPORT.getCode(),
                                                              null);
        if (CollectionUtils.isEmpty(approves)) {
            return detail;
        }

        // 根据部门ID过滤审批数据
        if (StringUtils.isNotBlank(departmentId)) {
            detail.setDepartmentId(departmentId);
            List<String> subDepartmentIds = getDepartmentIds(departmentId);
            approves = approves.stream()
                    .filter(a -> subDepartmentIds.contains(a.getDepartmentId()))
                    .toList();
        }

        // 获取所有业务ID
        List<Integer> bizIds = approves.stream()
                .map(a -> a.getBizId().intValue())
                .toList();

        // 签约主体id和创建人id
        Map<Integer, Integer> subBizIdAndCreator = approves.stream()
                .collect(Collectors.toMap(
                        e -> e.getBizId().intValue(),
                        InnerApproveEntity::getCreator
                ));
        // 获取业务数据
        List<BusinessEntity> businesses = getBusinessData(bizIds);
        Map<Integer, Long> creatorBusinessCount = new HashMap<>();
        if (CollectionUtils.isNotEmpty(businesses)) {
            for (BusinessEntity business : businesses) {
                if (subBizIdAndCreator.containsKey(business.getAdvertisingSubjectId())
                        && Objects.equals(business.getCreator(), subBizIdAndCreator.get(business.getAdvertisingSubjectId()))) {
                    creatorBusinessCount.put(business.getCreator(),
                            creatorBusinessCount.getOrDefault(business.getCreator(), 0L) + 1);
                }
            }
        }
        // 按创建者分组处理
        Map<Integer, List<InnerApproveEntity>> approvesByCreator = approves.stream()
                .collect(Collectors.groupingBy(InnerApproveEntity::getCreator));

        List<DashboardCustomerReportUserVO> userList = approvesByCreator.entrySet().stream()
                .map(entry -> {
                    Integer creatorId = entry.getKey();
                    List<InnerApproveEntity> creatorApproves = entry.getValue();
                    DashboardCustomerReportUserVO user = new DashboardCustomerReportUserVO();
                    user.setUserId(creatorId);
                    user.setApplyCount(creatorApproves.size());
                    user.setApprovedCount((int) creatorApproves.stream()
                            .filter(a -> InnerApproveOpinionTypeEnum.AGREE.getCode().equals(a.getResult()))
                            .count());
                    user.setBusinessCount(creatorBusinessCount.getOrDefault(creatorId, 0L).intValue());

                    return user;
                })
                .toList();

        detail.setUserList(userList);
        return detail;
    }

    /**
     * 获取所有商机数据
     *
     * @param bizIds 主体ID列表
     * @return 商机数据列表
     */
    private List<BusinessEntity> getBusinessData(List<Integer> bizIds) {
        return businessService.list(new LambdaQueryWrapper<BusinessEntity>()
                .select(BusinessEntity::getDepartmentId,
                        BusinessEntity::getCreator,
                        BusinessEntity::getAdvertisingSubjectId)
                .in(BusinessEntity::getAdvertisingSubjectId, bizIds)
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode()));
    }

    /**
     * 获取最近7周的数据
     *
     * @param date 日期
     * @param approves 审批数据列表
     * @return 最近7周的数据列表
     */
    private List<DashboardWeekVO> calculateRecent7Weeks(LocalDate date, List<InnerApproveEntity> approves) {
        List<DashboardWeekVO> weeks = new ArrayList<>();
        boolean hasApproves = CollectionUtils.isNotEmpty(approves);

        // 获取ISO周定义（周一为一周的第一天）
        WeekFields weekFields = WeekFields.ISO;
        LocalDate currentMonday = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        // 构建数据
        IntStream.rangeClosed(0, Constants.NUMBER_SIX)
                .mapToObj(i -> {
                    // 按时间顺序生成
                    LocalDate weekStart = currentMonday.minusWeeks(Constants.NUMBER_SIX - i);
                    LocalDate weekEnd = weekStart.plusDays(Constants.NUMBER_SIX);

                    DashboardWeekVO weekVO = new DashboardWeekVO();
                    weekVO.setStartDate(weekStart);
                    weekVO.setEndDate(weekEnd);
                    weekVO.setYear(weekStart.get(weekFields.weekBasedYear()));
                    weekVO.setWeekNumber(weekStart.get(weekFields.weekOfWeekBasedYear()));

                    if (hasApproves) {
                        long count = approves.stream()
                                .filter(a -> isWithinWeek(a.getCreateTime(), weekStart, weekEnd))
                                .count();
                        weekVO.setApprovedCount((int) count);
                    } else {
                        weekVO.setApprovedCount(0);
                    }
                    return weekVO;
                })
                .forEach(weeks::add);

        return weeks;
    }

    /**
     * 判断时间是否在周内
     *
     * @param createTime 创建时间
     * @param weekStart 周开始日期
     * @param weekEnd 周结束日期
     * @return 是否在周内
     */
    private boolean isWithinWeek(LocalDateTime createTime, LocalDate weekStart, LocalDate weekEnd) {
        LocalDateTime start = weekStart.atStartOfDay();
        LocalDateTime end = weekEnd.atTime(23, 59, 59, 0);
        return !createTime.isBefore(start) && !createTime.isAfter(end);
    }

    /**
     * 获取数据权限下所有审批数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param code 资源编码
     * @param type 审批类型
     * @return 审批数据列表
     */
    private List<InnerApproveEntity> getAllApproveDataByType(LocalDate startTime,
                                                             LocalDate endTime,
                                                             String code,
                                                             String type,
                                                             String result) {
        CachedUser cachedUser = UserThreadLocal.getUser();
        String permission = getPermission(cachedUser.getId(), code);

        // 基础查询条件（公共部分）
        LambdaQueryWrapper<InnerApproveEntity> baseQuery = new LambdaQueryWrapper<InnerApproveEntity>()
                .eq(InnerApproveEntity::getType, type)
                .between(InnerApproveEntity::getCreateTime, startTime, endTime);
        if (StringUtils.isNotBlank(result)) {
            baseQuery.eq(InnerApproveEntity::getResult, result);
        }

        // 根据权限动态添加条件
        switch (permission) {
            // 查所有数据
            case "4":
                break;
            // 查部门及子部门
            case "3":
                baseQuery.in(InnerApproveEntity::getDepartmentId, getDepartmentIds(cachedUser.getDepartmentId()));
                break;
            // 查本部门
            case "2":
                baseQuery.eq(InnerApproveEntity::getDepartmentId, cachedUser.getDepartmentId());
                break;
            // 查自己（默认情况也走此逻辑）
            case "1":
            default:
                baseQuery.eq(InnerApproveEntity::getCreator, cachedUser.getId());
                break;
        }

        return innerApproveService.list(baseQuery);
    }

    /**
     * 获取部门以及子部门的ID列表
     *
     * @param departmentId 部门ID
     * @return 部门ID列表
     */
    private List<String> getDepartmentIds(String departmentId) {
        List<String> departmentIdList = new ArrayList<>();
        try {
            ResultTemplate<List<DepartmentVO>> result = feignAuthorityRpc.getChildDepartmentList(departmentId);
            log.info("查自己部门以及子部门返回结果：{}", result);
            List<DepartmentVO> departmentList = result.getData();
            if (CollectionUtils.isEmpty(departmentList)) {
                departmentIdList.add(departmentId);
            } else {
                departmentList.forEach(departmentVO -> departmentIdList.add(departmentVO.getDepartmentId()));
            }
        } catch (Exception e) {
            throw new BusinessException("查自己部门以及子部门失败");
        }
        return departmentIdList;
    }

    /**
     * 获取用户资源最高数据权限
     *
     * @param userId 用户ID
     * @param code 资源编码
     * @return 用户资源最高数据权限
     */
    private String getPermission(Integer userId, String code) {
        try {
            UserDataHighestParam param = new UserDataHighestParam();
            param.setUserId(userId);
            param.setResourceCode(code);
            log.info("获取用户资源最高数据权限参数：{}", param);
            ResultTemplate<ResourceVO> result = feignAuthorityRpc.getUserDataHighest(param);
            log.info("获取用户资源最高数据权限返回结果：{}", result);
            ResourceVO resource = result.getData();
            if (resource == null || StringUtils.isBlank(resource.getCode())) {
                return "1";
            }

            return resource.getCode();
        } catch (Exception e) {
            throw new BusinessException("调用权限接口获取用户资源最高数据权限失败");
        }
    }

    /**
     * 分页查询客户报备数据
     *
     * @param code 资源编码
     * @param pageRequest 分页请求
     * @return 客户报备数据分页数据
     */
    @AutoTranslate
    public PageResponseVO<DashboardCustomerReportDetailVO> page(String code,
                                                                PageRequestVO<DashboardCustomerReportQueryParam> pageRequest) {
        // 1. 构建查询条件
        LambdaQueryWrapper<InnerApproveEntity> queryWrapper = buildQueryWrapper(code, pageRequest.getQuery());

        // 2. 执行分页查询
        IPage<InnerApproveEntity> pageResult = executePageQuery(pageRequest, queryWrapper);

        // 3. 转换结果
        return buildPageResponse(pageResult);
    }

    /**
     * 构建查询条件
     *
     * @param code 资源编码
     * @param query 查询参数
     * @return 查询条件封装类
     */
    private LambdaQueryWrapper<InnerApproveEntity> buildQueryWrapper(String code,
                                                                     DashboardCustomerReportQueryParam query) {
        CachedUser currentUser = UserThreadLocal.getUser();
        LambdaQueryWrapper<InnerApproveEntity> wrapper = new QueryWrapper<InnerApproveEntity>().lambda()
                .eq(InnerApproveEntity::getType, StationTargetEnum.CUSTOMER_REPORT.getCode())
                .between(InnerApproveEntity::getCreateTime, query.getStartTime(), query.getEndTime());

        // 处理权限条件
        String permission = getPermission(currentUser.getId(), code);
        switch (permission) {
            case "4":
                if (StringUtils.isNotBlank(query.getDepartmentId())) {
                    wrapper.in(InnerApproveEntity::getDepartmentId, getDepartmentIds(query.getDepartmentId()));
                }
                break;
            case "3":
                handleCrossDepartmentPermission(wrapper, query.getDepartmentId(), currentUser);
                break;
            case "2":
                wrapper.eq(InnerApproveEntity::getDepartmentId, currentUser.getDepartmentId());
                break;
            case "1":
            default:
                if (StringUtils.isNotBlank(query.getDepartmentId())) {
                    wrapper.eq(InnerApproveEntity::getDepartmentId, query.getDepartmentId());
                }
                wrapper.eq(InnerApproveEntity::getCreator, currentUser.getId());
        }
        return wrapper;
    }

    /**
     * 处理跨部门权限条件
     *
     * @param wrapper 查询条件
     * @param departmentId 部门ID
     * @param currentUser 当前用户
     */
    private void handleCrossDepartmentPermission(LambdaQueryWrapper<InnerApproveEntity> wrapper,
                                                 String departmentId,
                                                 CachedUser currentUser) {
        // 如果有部门id，则先查出自己部门及下级部门的数据,再查出传入的部门及子部门数据，两个集合保留传入部门数据的交集
        // 如果没有,则查自己部门及子部门
        if (StringUtils.isNotBlank(departmentId)) {
            List<String> userDepartments = getDepartmentIds(currentUser.getDepartmentId());
            List<String> targetDepartments = getDepartmentIds(departmentId);
            List<String> intersection = targetDepartments.stream()
                    .filter(userDepartments::contains)
                    .toList();
            wrapper.in(InnerApproveEntity::getDepartmentId, intersection);
        } else {
            wrapper.in(InnerApproveEntity::getDepartmentId, getDepartmentIds(currentUser.getDepartmentId()));
        }
    }

    /**
     * 执行分页查询
     *
     * @param pageRequest 分页请求
     * @param wrapper 查询条件
     * @return 分页查询结果
     */
    private IPage<InnerApproveEntity> executePageQuery(PageRequestVO<DashboardCustomerReportQueryParam> pageRequest,
                                                       LambdaQueryWrapper<InnerApproveEntity> wrapper) {
        Page<InnerApproveEntity> page = new Page<>(Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));
        return innerApproveService.getBaseMapper().selectPage(page, wrapper);
    }

    /**
     * 构建分页结果
     *
     * @param pageResult 分页查询结果
     * @return 分页数据
     */
    private PageResponseVO<DashboardCustomerReportDetailVO> buildPageResponse(IPage<InnerApproveEntity> pageResult) {
        PageResponseVO<DashboardCustomerReportDetailVO> response = new PageResponseVO<>();
        response.setCurrentPage(pageResult.getCurrent());
        response.setTotalRows(pageResult.getTotal());
        response.setTotal(pageResult.getTotal());
        response.setPageSize(pageResult.getSize());
        response.setTotalPages(pageResult.getPages());

        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return response;
        }

        // 批量获取所有需要的数据
        List<Integer> bizIds = pageResult.getRecords().stream()
                .map(entity -> entity.getBizId().intValue())
                .toList();

        // 批量查询商机数据
        List<BusinessEntity> businesses = getBusinessData(bizIds);

        // 按照主体id分组
        Map<Integer, List<BusinessEntity>> businessByAdvertisingSubjectId = CollectionUtils.isEmpty(businesses)
                ? Collections.emptyMap()
                : businesses.stream().collect(Collectors.groupingBy(BusinessEntity::getAdvertisingSubjectId));

        // 批量查询广告主体数据
        Map<Integer, AdvertisingSubjectEntity> advertisingSubjects = getAdvertisingData(bizIds);

        // 批量查询产品线名称
        Map<Integer, String> productLineNames = productLineService.getProductLineBySubjectId(bizIds);

        // 转换结果
        response.setRows(pageResult.getRecords().stream()
                .map(entity -> convertToDetailVO(
                        entity,
                        businessByAdvertisingSubjectId,
                        advertisingSubjects,
                        productLineNames))
                .toList());

        return response;
    }

    /**
     * 转换结果
     *
     * @param entity 审批数据实体
     * @param businessByAdvertisingSubjectId 主体商机集合
     * @param advertisingSubjects 广告主体数据集合
     * @param productLineNames 产品线名称集合
     * @return 转换后的数据实体
     */
    private DashboardCustomerReportDetailVO convertToDetailVO(InnerApproveEntity entity,
                                                              Map<Integer, List<BusinessEntity>> businessByAdvertisingSubjectId,
                                                              Map<Integer, AdvertisingSubjectEntity> advertisingSubjects,
                                                              Map<Integer, String> productLineNames) {
        Integer bizId = entity.getBizId().intValue();
        AdvertisingSubjectEntity subject = advertisingSubjects.get(bizId);

        DashboardCustomerReportDetailVO vo = new DashboardCustomerReportDetailVO();
        if (subject != null) {
            vo.setCompanyId(subject.getCompanyId());
            vo.setBrandId(subject.getBrandId());
        }

        vo.setProductLine(productLineNames.get(bizId));
        vo.setUserId(entity.getCreator());
        vo.setDepartmentId(entity.getDepartmentId());
        vo.setCreateTime(entity.getCreateTime());
        vo.setResult(entity.getResult());
        Integer isCreateBusiness = BooleFlagEnum.NO.getCode();
        if (businessByAdvertisingSubjectId.containsKey(bizId)) {
            List<BusinessEntity> businesses = businessByAdvertisingSubjectId.get(bizId);
            for (BusinessEntity business : businesses) {
                if (Objects.equals(business.getCreator(), entity.getCreator())) {
                    isCreateBusiness = BooleFlagEnum.YES.getCode();
                    break;
                }
            }
        }
        vo.setIsCreateBusiness(isCreateBusiness);
        return vo;
    }

    /**
     * 获取主体数据
     *
     * @param businessAdvertisingSubjectIds 主体id集合
     * @return 主体数据集合
     */
    private Map<Integer, AdvertisingSubjectEntity> getAdvertisingData(List<Integer> businessAdvertisingSubjectIds) {
        List<AdvertisingSubjectEntity> advertisingSubjectEntityList = advertisingSubjectService.lambdaQuery()
                .select(AdvertisingSubjectEntity::getBrandId,
                        AdvertisingSubjectEntity::getCompanyId,
                        AdvertisingSubjectEntity::getId)
                .in(AdvertisingSubjectEntity::getId, businessAdvertisingSubjectIds)
                .list();
        if (CollectionUtils.isEmpty(advertisingSubjectEntityList)) {
            return Collections.emptyMap();
        }
        return advertisingSubjectEntityList.stream().collect(Collectors.toMap(AdvertisingSubjectEntity::getId, entity -> entity));
    }

    /**
     * 刷新主体表创建人名称
     */
    public void refresh() {
        List<AdvertisingSubjectEntity> advertisingSubjectEntityList = advertisingSubjectService.lambdaQuery().list();
        if (CollectionUtils.isEmpty(advertisingSubjectEntityList)) {
            log.info("主体表没有可刷新数据");
            return;
        }
        List<Integer> userIds = advertisingSubjectEntityList.stream().map(AdvertisingSubjectEntity::getCreator).toList();
        try {
            log.info("调用用户信息:{}", userIds);
            ResultTemplate<List<UserVO>> result = feignAuthorityRpc.getUserByIds(userIds);
            log.info("用户信息:{}", result);
            if (CollectionUtils.isNotEmpty(result.getData())) {
                Map<Integer, String> userNameMap = result.getData().stream()
                        .collect(Collectors.toMap(UserVO::getId, UserVO::getName));
                advertisingSubjectEntityList.forEach(entity -> entity.setCreatorName(userNameMap.get(entity.getCreator())));
            }
        } catch (Exception e) {
            log.error("调用用户信息失败", e);
        }
        advertisingSubjectService.updateBatchById(advertisingSubjectEntityList);
    }

    /**
     * 客户报备分页查询
     *
     * @param param 分页参数
     * @return 分页结果数据
     */
    @AutoTranslate
    public PageResponseVO<DashboardCustomerReportSearchVO> search(PageRequestVO<String> param) {
        IPage<DashboardCustomerReportSearchDTO> pageResult =
                advertisingSubjectService.customerReportPageList(getCustomerReportPage(param), param.getQuery());
        PageResponseVO<DashboardCustomerReportSearchVO> pageResponse = new PageResponseVO<>();
        pageResponse.setCurrentPage(pageResult.getCurrent());
        pageResponse.setTotalRows(pageResult.getTotal());
        pageResponse.setTotal(pageResult.getTotal());
        pageResponse.setPageSize(pageResult.getSize());
        pageResponse.setTotalPages(pageResult.getPages());

        // 没查到数据，直接返回
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return pageResponse;
        }
        List<Integer> subjectIds = pageResult.getRecords().stream().map(DashboardCustomerReportSearchDTO::getId).toList();

        // 批量查询产品线名称
        Map<Integer, String> productLineNames = productLineService.getProductLineBySubjectId(subjectIds);

        // 批量查询商机数据
        List<BusinessEntity> businesses = getBusinessData(subjectIds);
        Set<Integer> businessSubjectIds;
        businessSubjectIds = CollectionUtils.isEmpty(businesses)
                ? Collections.emptySet()
                : businesses.stream()
                .map(BusinessEntity::getAdvertisingSubjectId)
                .collect(Collectors.toSet());

        // 转换结果
        pageResponse.setRows(pageResult.getRecords().stream()
                .map(entity -> convertToSearchVO(
                        entity,
                        businessSubjectIds,
                        productLineNames))
                .toList());
        return pageResponse;
    }

    /**
     * 转换结果
     *
     * @param dto 查询返回dto
     * @param businessSubjectIds 有商机的主体id集合
     * @param productLineNames 产品线名称集合
     * @return DashboardCustomerReportSearchVO 查询vo
     */
    private DashboardCustomerReportSearchVO convertToSearchVO(DashboardCustomerReportSearchDTO dto,
                                                              Set<Integer> businessSubjectIds,
                                                              Map<Integer, String> productLineNames) {
        DashboardCustomerReportSearchVO vo = AdvertisingSubjectConvert.INSTANCE.dtoToVo(dto);
        vo.setProductLine(productLineNames.get(dto.getId()));
        vo.setIsCreateBusiness(businessSubjectIds
                .contains(dto.getId()) ? BooleFlagEnum.YES.getCode() : BooleFlagEnum.NO.getCode());
        return vo;
    }

    /**
     * 获取分页对象
     */
    private Page<DashboardCustomerReportSearchDTO> getCustomerReportPage(PageRequestVO<?> pageRequest) {
        // 分页查询列表，自定义统计SQL
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));
    }

}
