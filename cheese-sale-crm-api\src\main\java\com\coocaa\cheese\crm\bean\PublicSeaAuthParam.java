
package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.audit.bean.ApproveRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 公海打捞授权表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Schema(name = "PublicSeaAuthParam", description = "公海打捞授权表参数")
public class PublicSeaAuthParam implements ApproveRequest {

    @NotNull(message = "ID不能为空")
    @Schema(description = "签约主体业务ID")
    private Integer bizId;

    @NotNull(message = "情况说明不能为空")
    @Schema(description = "情况说明", maxLength = 100)
    private String remark;

}