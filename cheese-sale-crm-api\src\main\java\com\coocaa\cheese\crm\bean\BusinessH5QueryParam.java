package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 商机管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
@Schema(name = "BusinessH5QueryParam", description = "商机H5查询参数")
public class BusinessH5QueryParam {

    @Schema(description = "品牌名称、归属人姓名、商机编码")
    private String queryName;

    @Schema(description = "是否本人查询 [0:否, 1:是]", type = "String", example = "0")
    private Integer ownerFlag;

    @Schema(description = "是否个人渠道 [0:否, 1:是]", type = "String", example = "0")
    private Integer ownerChannelFlag;

    @Schema(description = "商机释放状态 [0:保护中, 1:已释放]", type = "Integer", example = "0")
    private Integer advertisingReleaseFlag;

    @Schema(description = "商机进度(字典0073)", type = "String", example = "0073-1")
    private List<String> progresses;

    @Schema(description = "商机状态(字典0074)", type = "String", example = "0074-1")
    private List<String> businessStatus;

    @Schema(description = "商机绑定状态 [0:未绑定, 1:已绑定]", type = "Integer", example = "1")
    private Integer advertisingBindFlag;

} 