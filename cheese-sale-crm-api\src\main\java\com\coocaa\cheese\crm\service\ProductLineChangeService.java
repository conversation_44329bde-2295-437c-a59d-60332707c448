package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.InnerApproveApplyQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTemplateParam;
import com.coocaa.cheese.crm.bean.ProductLineInitiateParam;
import com.coocaa.cheese.crm.common.db.bean.InnerProductApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerProductLineDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineChangePageDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineUnionDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandTagEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineChangeEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBrandService;
import com.coocaa.cheese.crm.common.db.service.IBrandTagService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IProductLineChangeService;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import com.coocaa.cheese.crm.common.tools.config.api.InnerApproveConfig;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.ApproveFieldTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.ProductLineChangeExecuteStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.convert.ProductLineChangeConvert;
import com.coocaa.cheese.crm.convert.ProductLineConvert;
import com.coocaa.cheese.crm.rpc.FeignInnerApprovalRpc;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveApplyPageQueryParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveDealCountQueryParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveTaskPageQueryParam;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveApplyVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveDealCountVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveTaskVO;
import com.coocaa.cheese.crm.vo.InnerApprovePendingCountVO;
import com.coocaa.cheese.crm.vo.InnerApproveProductApplyPageVO;
import com.coocaa.cheese.crm.vo.InnerProductApproveDetailVO;
import com.coocaa.cheese.crm.vo.InnerProductApproveTaskPageVO;
import com.coocaa.cheese.crm.vo.ProductLineInitiateVO;
import com.coocaa.cheese.crm.vo.ProductLineUnionVO;
import com.coocaa.cheese.crm.vo.ProductLineVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 产品线变更记录表 业务实现类
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ProductLineChangeService {

    private static final int PRODUCT_LINE_ZERO = 0;
    private static final int PRODUCT_LINE_ONE = 1;

    private final IBrandService brandService;
    private final IBrandTagService brandTagService;
    private final IBusinessService businessService;
    private final InnerApproveService approveService;
    private final InnerApproveConfig innerApproveConfig;
    private final IProductLineService productLineService;
    private final IInnerApproveService innerApproveService;
    private final FeignInnerApprovalRpc feignInnerApprovalRpc;
    private final IProductLineChangeService productLineChangeService;
    private final IAdvertisingSubjectService advertisingSubjectService;

    @AutoTranslate
    public ProductLineInitiateVO queryInitiate(Integer id) {

        InnerProductLineDetailDTO dto = Optional.ofNullable(businessService.queryDetailForProductLine(id))
                .orElseThrow(() -> new BusinessException("查询商机详情失败!"));
        log.info("发起产品线变更查询商机详情id:{}, dto:{}", id, JSONUtil.toJsonStr(dto));
        ProductLineInitiateVO vo = ProductLineConvert.INSTANCE.toVo(dto);
        //查询该签约主体绑定的产品线
        List<String> productLineName = productLineService.queryProductLineBySignSubjectId(dto.getSignSubjectId());
        log.info("发起产品线变更查询该品牌下的产品线:{}", JSONUtil.toJsonStr(productLineName));
        vo.setProductLines(productLineName);

        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long initiate(ProductLineInitiateParam param) {

        log.info("发起产品线变更参数:{}", JSONUtil.toJsonStr(param));
        //校验数据
        checkInitiate(param);
        log.info("发起产品线变更校验数据成功!");
        //保存商机释放改期记录表
        ProductLineChangeEntity entity = saveProductLineChange(param);
        log.info("发起产品线变更保存成功!");
        //发起申请
        Boolean approval = approveService.initiateApproval(entity.getId(), StationTargetEnum.PRODUCT_LINE_CHANGE,
                innerApproveConfig.getProduct(), buildApproveList());
        if (!approval) {
            throw new BusinessException("产品线变更发起审批失败!");
        }

        return entity.getId();
    }

    @AutoTranslate
    public PageResponseVO<InnerProductApproveTaskPageVO> pageApproveTask(PageRequestVO<InnerApproveTaskQueryParam> pageRequest) {

        PageResponseVO<InnerProductApproveTaskPageVO> resultPage = new PageResponseVO<>();
        //组装查询站内审批分页参数
        InnerApproveTaskPageQueryParam param = new InnerApproveTaskPageQueryParam();
        param.setRuleCode(innerApproveConfig.getProduct());
        param.setFlag(pageRequest.getQuery().getExamine() ? PRODUCT_LINE_ONE : PRODUCT_LINE_ZERO);
        param.setApplicant(pageRequest.getQuery().getInstanceUserName());
        PageResponseVO<?> pageResponseVO = approveService.queryTaskPage(param, pageRequest.getCurrentPage(), pageRequest.getPageSize());
        if (CollectionUtil.isEmpty(pageResponseVO.getRows())) {
            return resultPage;
        }
        List<InnerApproveTaskVO> rowList = JSONUtil.toList(JSONUtil.toJsonStr(pageResponseVO.getRows()), InnerApproveTaskVO.class);
        //查询审批code信息
        Set<String> codes = rowList.stream().map(InnerApproveTaskVO::getInstanceCode).collect(Collectors.toSet());
        List<InnerApproveEntity> innerApproveEntities = innerApproveService.queryBizId(codes, StationTargetEnum.PRODUCT_LINE_CHANGE.getCode());
        log.info("产品线变更查询审批任务列表查询审批任务code信息pageRequest:{}, data:{}",
                JSONUtil.toJsonStr(pageRequest), JSONUtil.toJsonStr(innerApproveEntities));
        if (CollectionUtil.isEmpty(innerApproveEntities)) {
            return resultPage;
        }
        //因为InnerApproveEntity表逻辑没办法联查，所以跟主体表分开查询
        List<Long> bizIds = innerApproveEntities.stream().map(InnerApproveEntity::getBizId).toList();
        List<ProductLineChangePageDTO> entityList = productLineChangeService.queryInnerProductLineChange(bizIds);
        log.info("产品线变更查询审批任务列表查询签约主体信息:{}", JSONUtil.toJsonStr(entityList));
        if (CollectionUtil.isEmpty(entityList)) {
            return resultPage;
        }
        Map<String, InnerApproveEntity> examineApproveEntityMap = innerApproveEntities.stream()
                .collect(Collectors.toMap(InnerApproveEntity::getInstanceCode, entity -> entity));
        Map<Long, ProductLineChangePageDTO> subjectMap = entityList.stream()
                .collect(Collectors.toMap(ProductLineChangePageDTO::getId, entity -> entity));
        //组装新的分页数据返回
        resultPage.setCurrentPage(pageResponseVO.getCurrentPage());
        resultPage.setTotal(pageResponseVO.getTotal());
        resultPage.setTotalPages(pageResponseVO.getTotalPages());
        resultPage.setRows(rowList.stream().map(e -> {
            InnerProductApproveTaskPageVO vo = ProductLineChangeConvert.INSTANCE.toVo(e);
            InnerApproveEntity innerApproveEntity = examineApproveEntityMap.get(e.getInstanceCode());
            if (Objects.nonNull(innerApproveEntity) && Objects.nonNull(subjectMap.get(innerApproveEntity.getBizId()))) {
                ProductLineChangePageDTO dto = subjectMap.get(innerApproveEntity.getBizId());
                vo.setId(dto.getId());
                vo.setBrandId(dto.getBrandId());
                vo.setCompanyId(dto.getCompanyId());
                vo.setDepartmentId(e.getDepartId());
                vo.setProductLine(dto.getProductLine());
            } else {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).toList());

        return resultPage;
    }

    @AutoTranslate
    public InnerProductApproveDetailVO queryApproveDetail(Long id) {

        InnerApproveEntity entity = innerApproveService.lambdaQuery()
                .eq(InnerApproveEntity::getBizId, id)
                .eq(InnerApproveEntity::getType, StationTargetEnum.PRODUCT_LINE_CHANGE.getCode())
                .orderByDesc(InnerApproveEntity::getCreateTime)
                .last("LIMIT 1")
                .one();
        log.info("产品线变更查询审批信息详情id:{}, data:{}", id, JSONUtil.toJsonStr(entity));
        if (Objects.isNull(entity)) {
            throw new BusinessException("产品线变更查询审批信息详情未找到审批信息");
        }
        //查询品牌+主体信息
        InnerProductApproveDetailDTO dto = productLineChangeService.queryInnerApproveDetail(id);
        if (Objects.isNull(dto)) {
            throw new BusinessException("产品线变更查询审批信息详情失败");
        }
        InnerProductApproveDetailVO vo = ProductLineChangeConvert.INSTANCE.toVo(dto);
        //查询审批信息
        InnerApproveInstanceVO innerApproveInstanceVO = approveService.queryDetail(entity.getInstanceCode());
        if (Objects.nonNull(innerApproveInstanceVO)) {
            vo.setApprovalName(innerApproveInstanceVO.getApprovalName());
            vo.setInstanceUserId(innerApproveInstanceVO.getUserId());
            vo.setDepartmentId(innerApproveInstanceVO.getDepartId());
            vo.setInstanceCreateTime(innerApproveInstanceVO.getCreateTime());
            vo.setEndTime(innerApproveInstanceVO.getEndTime());
            vo.setApprovalResult(innerApproveInstanceVO.getApprovalResult());
        }
        return vo;
    }

    @AutoTranslate
    public List<ProductLineUnionVO> queryBrandUnionProductLine(Long id) {

        //查询品牌信息
        InnerProductApproveDetailDTO innerProductApproveDetailDTO = Optional.ofNullable(productLineChangeService.queryInnerApproveDetail(id))
                .orElseThrow(() -> new BusinessException("产品线变更查询品牌信息详情未找到品牌信息"));
        //查询该品牌已报备的签约主体及产品线
        List<ProductLineUnionDTO> inBrandProductLineList = productLineService.queryInBrandUnionProductLine(innerProductApproveDetailDTO.getBrandId());
        log.info("产品线变更查询该品牌关联信息详情id:{}, data:{}", id, JSONUtil.toJsonStr(inBrandProductLineList));
        if (CollectionUtil.isEmpty(inBrandProductLineList)) {
            return Collections.emptyList();
        }
        //获取所有签约主体对应公司id
        List<Integer> signSubjectIds = inBrandProductLineList.stream().map(ProductLineUnionDTO::getSignSubjectId).distinct().toList();
        List<AdvertisingSubjectEntity> advertisingSubjectEntityList = advertisingSubjectService.lambdaQuery()
                .select(AdvertisingSubjectEntity::getCompanyId)
                .in(AdvertisingSubjectEntity::getId, signSubjectIds)
                .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
        List<Integer> companyIds = advertisingSubjectEntityList.stream().map(AdvertisingSubjectEntity::getCompanyId).distinct().toList();
        //查询该签约主体绑定公司对应签约主体绑定产品线
        List<ProductLineUnionDTO> outBrandProductLineList = productLineService.queryOutBrandUnionProductLine(companyIds);
        log.info("产品线变更查询品牌信息详情id:{}, data:{}", id, JSONUtil.toJsonStr(outBrandProductLineList));
        if (CollectionUtil.isEmpty(outBrandProductLineList)) {
            return Collections.emptyList();
        }

        return outBrandProductLineList.stream().map(ProductLineConvert.INSTANCE::toVo).toList();
    }

    @AutoTranslate
    public PageResponseVO<InnerApproveProductApplyPageVO> pageApplyApprove(PageRequestVO<InnerApproveApplyQueryParam> pageRequest) {

        PageResponseVO<InnerApproveProductApplyPageVO> resultPage = new PageResponseVO<>();
        //组装查询站内审批申请分页参数
        InnerApproveApplyPageQueryParam param = new InnerApproveApplyPageQueryParam();
        param.setRuleCode(innerApproveConfig.getProduct());
        param.setFlag(pageRequest.getQuery().getExamine() ? PRODUCT_LINE_ONE : PRODUCT_LINE_ZERO);
        param.setSortFiled(pageRequest.getQuery().getExamine() ? "endTime" : "createTime");
        PageResponseVO pageResponseVO = approveService.queryApplyPage(param, pageRequest.getCurrentPage(), pageRequest.getPageSize());
        if (CollectionUtil.isEmpty(pageResponseVO.getRows())) {
            return resultPage;
        }
        List<InnerApproveApplyVO> rowList = JSONUtil.toList(JSONUtil.toJsonStr(pageResponseVO.getRows()), InnerApproveApplyVO.class);
        //查询审批申请code信息
        Set<String> codes = rowList.stream().map(InnerApproveApplyVO::getInstanceCode).collect(Collectors.toSet());
        List<InnerApproveEntity> innerApproveEntities = innerApproveService.queryBizId(codes, StationTargetEnum.PRODUCT_LINE_CHANGE.getCode());
        log.info("产品线变更查询审批申请code信息pageRequest:{},:{}", JSONUtil.toJsonStr(pageRequest), JSONUtil.toJsonStr(innerApproveEntities));
        if (CollectionUtil.isEmpty(innerApproveEntities)) {
            return resultPage;
        }
        List<Long> bizIds = innerApproveEntities.stream().map(InnerApproveEntity::getBizId).toList();
        List<ProductLineChangePageDTO> entityList = productLineChangeService.queryInnerProductLineChange(bizIds);
        log.info("产品线变更查询审批申请查询签约主体信息:{}", JSONUtil.toJsonStr(entityList));
        if (CollectionUtil.isEmpty(entityList)) {
            return resultPage;
        }
        Map<String, InnerApproveEntity> examineApproveEntityMap = innerApproveEntities.stream()
                .collect(Collectors.toMap(InnerApproveEntity::getInstanceCode, entity -> entity));
        Map<Long, ProductLineChangePageDTO> subjectMap = entityList.stream()
                .collect(Collectors.toMap(ProductLineChangePageDTO::getId, entity -> entity));
        //组装新的分页数据返回
        resultPage.setCurrentPage(pageResponseVO.getCurrentPage());
        resultPage.setTotal(pageResponseVO.getTotal());
        resultPage.setTotalPages(pageResponseVO.getTotalPages());
        resultPage.setRows(rowList.stream().map(e -> {
            InnerApproveProductApplyPageVO vo = ProductLineChangeConvert.INSTANCE.toVo(e);
            vo.setInstanceCreateTime(e.getCreateTime());
            if (Objects.nonNull(examineApproveEntityMap.get(e.getInstanceCode()))
                    && Objects.nonNull(subjectMap.get(examineApproveEntityMap.get(e.getInstanceCode()).getBizId()))) {
                ProductLineChangePageDTO dto = subjectMap.get(examineApproveEntityMap.get(e.getInstanceCode()).getBizId());
                vo.setId(dto.getId());
                vo.setBrandId(dto.getBrandId());
                vo.setCompanyId(dto.getCompanyId());
                vo.setProductLine(dto.getProductLine());
            } else {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).toList());

        return resultPage;
    }

    public List<InnerApprovePendingCountVO> queryApprovePendingCount() {

        //查询站内审批规则编号
        List<Integer> codeList = Arrays.asList(innerApproveConfig.getReport(), innerApproveConfig.getRelease(),
                innerApproveConfig.getSea(), innerApproveConfig.getProduct(), innerApproveConfig.getPk());
        InnerApproveDealCountQueryParam param = new InnerApproveDealCountQueryParam();
        param.setCodes(codeList);
        ResultTemplate<List<InnerApproveDealCountVO>> listResultTemplate = feignInnerApprovalRpc.queryDealCount(param);
        log.info("查询待处理任务数量结果:{}", JSONUtil.toJsonStr(listResultTemplate));
        if (listResultTemplate.getSuccess() && CollectionUtil.isNotEmpty(listResultTemplate.getData())) {
            return listResultTemplate.getData().stream()
                    .map(vo -> new InnerApprovePendingCountVO().setCode(vo.getCode()).setCount(vo.getCount()))
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @AutoTranslate
    public List<ProductLineVO> queryProductInfoBySign(Integer id) {

        List<ProductLineDTO> productLineDTOList = productLineChangeService.queryProductLineBySign(id);
        return productLineDTOList.stream().map(productLineDTO -> ProductLineConvert.INSTANCE.toVo(productLineDTO)).toList();
    }

    @AutoTranslate
    public ProductLineVO queryProductInfoByProduct(Long id) {

        ProductLineEntity entity = productLineService.getById(id);
        return ProductLineConvert.INSTANCE.toVo(entity);
    }

    private void checkInitiate(ProductLineInitiateParam param) {

        BusinessEntity entity = Optional.ofNullable(businessService.lambdaQuery()
                .eq(BusinessEntity::getId, param.getBusinessId())
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one()).orElseThrow(() -> new BusinessException("商机不存在!"));
        BrandEntity brandEntity = Optional.ofNullable(brandService.lambdaQuery()
                .eq(BrandEntity::getId, entity.getBrandId())
                .eq(BrandEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one()).orElseThrow(() -> new BusinessException("品牌不存在!"));
        List<BrandTagEntity> brandTagEntityList = brandTagService.lambdaQuery()
                .eq(BrandTagEntity::getBrandId, brandEntity.getId())
                .list();
        List<ProductLineChangeEntity> productLineChangeEntityList = productLineChangeService.lambdaQuery()
                .eq(ProductLineChangeEntity::getBusinessId, param.getBusinessId())
                .eq(ProductLineChangeEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
        //基础校验
        if (!UserThreadLocal.getUserId().equals(entity.getOwnerId())) {
            throw new BusinessException("您不是当前商机的归属人，无法操作");
        }
        if (!Objects.equals(PRODUCT_LINE_ZERO, entity.getAdvertisingReleaseFlag())) {
            throw new BusinessException("该商机不处于保护中，无法操作!");
        }

        //产品线名称不能与界面填写的品牌名称或品牌别名相同；不能在一个界面重复录入相同名称的产品线（不同的品牌允许有相同的产品线，故无需跨品牌验重）；
        Set<String> invalidNames = new HashSet<>();
        // 检查数组内是否有重复元素
        Set<String> uniqueNames = new HashSet<>();
        for (String name : param.getAfterProductLines()) {
            if (!uniqueNames.add(name)) {
                invalidNames.add(name);
            }
        }

        // 检查是否与品牌名称或标签冲突
        List<String> brandTags = Collections.emptyList();
        String brandName = brandEntity.getName();
        if (CollectionUtil.isNotEmpty(brandTagEntityList)) {
            brandTags = brandTagEntityList.stream().map(BrandTagEntity::getName).toList();
        }

        List<String> finalBrandTags = brandTags;
        Set<String> conflictNames = param.getAfterProductLines().stream()
                .filter(n -> n.equals(brandName) || (CollectionUtil.isNotEmpty(finalBrandTags) && finalBrandTags.contains(n)))
                .collect(Collectors.toSet());
        invalidNames.addAll(conflictNames);

        if (CollectionUtil.isNotEmpty(invalidNames)) {
            throw new BusinessException("存在重复的产品线: " + invalidNames);
        }
        //提交时检查该【商机ID】下是否有【执行状态】=待定 的《产品线变更记录》，若有则报错
        if (CollectionUtil.isNotEmpty(productLineChangeEntityList)) {
            if (productLineChangeEntityList.stream().anyMatch(e ->
                    Objects.equals(ProductLineChangeExecuteStatusEnum.PENDING.getCode(), e.getExecuteStatus()))) {
                throw new BusinessException("已有审核中的申请，请勿重复提交");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ProductLineChangeEntity saveProductLineChange(ProductLineInitiateParam param) {

        ProductLineChangeEntity entity = ProductLineChangeConvert.INSTANCE.toEntity(param);
        String beforeProductLine = String.join(Constants.COMMA, param.getBeforeProductLines());
        entity.setBeforeProductLine(beforeProductLine);
        String afterProductLine = String.join(Constants.COMMA, param.getAfterProductLines());
        entity.setAfterProductLine(afterProductLine);
        entity.setExecuteStatus(ProductLineChangeExecuteStatusEnum.PENDING.getCode());
        entity.setStatusChangeTime(LocalDateTime.now());
        boolean save = productLineChangeService.save(entity);

        if (!save) {
            throw new BusinessException("产品线变更记录创建失败!");
        }

        return entity;
    }

    private List<InnerApproveTemplateParam> buildApproveList() {

        return Arrays.asList(
                new InnerApproveTemplateParam("creator", ApproveFieldTypeEnum.NUMBER.getCode(), UserThreadLocal.getUserId().toString())
        );
    }
}