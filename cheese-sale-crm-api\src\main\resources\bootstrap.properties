# \u5E94\u7528\u540D\u79F0
spring.application.name=cheese-sale-crm-api
# \u914D\u7F6E\u4E2D\u5FC3\u7684\u5730\u5740
spring.cloud.nacos.config.server-addr=${nacos_config_server}
# \u914D\u7F6E\u4E2D\u5FC3\u547D\u540D\u7A7A\u95F4
spring.cloud.nacos.config.namespace=${nacos_config_namespace}
# \u914D\u7F6E\u6587\u4EF6\u7C7B\u578B
spring.cloud.nacos.config.file-extension=properties
# \u9879\u76EE\u672C\u8EAB\u7684\u914D\u7F6E\u6587\u4EF6\u4F18\u5148\u7EA7\u6700\u9AD8
spring.cloud.nacos.config.name=cheese-sale-crm-api.properties
spring.cloud.nacos.config.refresh-enabled=true
spring.cloud.nacos.config.group=CHEESE_SALE_CRM

#\u6269\u5C55\u7684\u914D\u7F6E\u6587\u4EF6 \u4E0B\u6807\u8D8A\u5927\uFF0C\u4F18\u5148\u7EA7\u8D8A\u9AD8
spring.cloud.nacos.config.extension-configs[0].data-id=${spring.application.name}-mysql.properties
spring.cloud.nacos.config.extension-configs[0].refresh=false
spring.cloud.nacos.config.extension-configs[0].group=${spring.cloud.nacos.config.group}

spring.cloud.nacos.config.extension-configs[1].data-id=${spring.application.name}-redis.properties
spring.cloud.nacos.config.extension-configs[1].refresh=false
spring.cloud.nacos.config.extension-configs[1].group=${spring.cloud.nacos.config.group}

# \u670D\u52A1\u6CE8\u518C\u548C\u53D1\u73B0
spring.cloud.nacos.discovery.server-addr=${nacos_discovery_server}
# spring.cloud.service-registry.auto-registration.enabled=false