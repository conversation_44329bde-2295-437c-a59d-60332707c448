package com.coocaa.cheese.crm.controller.institution;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.InstitutionContractParam;
import com.coocaa.cheese.crm.bean.InstitutionContractQueryParam;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.InstitutionContractService;
import com.coocaa.cheese.crm.vo.InstitutionContractVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机构合同管理
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@RestController
@RequestMapping("/institution-contracts")
@Tag(name = "机构合同管理", description = "机构合同管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionContractController extends BaseController {
    private final InstitutionContractService institutionContractService;

    /**
     * 分页查询机构合同列表
     */
    @Operation(summary = "分页查询机构合同列表")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<InstitutionContractVO>> pageListInstitutionContract(@RequestBody PageRequestVO<InstitutionContractQueryParam> pageRequest) {
        return ResultTemplate.success(institutionContractService.pageListInstitutionContract(pageRequest));
    }

    /**
     * 查询合同详情
     */
    @Operation(summary = "查询合同详情")
    @GetMapping("/{id}")
    public ResultTemplate<InstitutionContractVO> detail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(institutionContractService.detail(id));
    }

    /**
     * 上传机构合同
     */
    @Operation(summary = "上传机构合同")
    @PostMapping
    public ResultTemplate<Boolean> uploadInstitutionContract(@RequestBody @Validated InstitutionContractParam param) {
        return ResultTemplate.success(institutionContractService.uploadInstitutionContract(param));
    }

    /**
     * 设置当前生效合同
     */
    @Operation(summary = "设置当前生效合同")
    @Parameter(name = "id", description = "合同ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @PutMapping("/{id}/effective")
    public ResultTemplate<Boolean> setEffectiveContract(
            @PathVariable("id") Integer id) {
        return ResultTemplate.success(institutionContractService.setEffectiveContract(id));
    }

    /**
     * 获取机构当前生效合同编号
     */
    @Operation(summary = "获取机构当前生效合同编号")
    @Parameter(name = "institutionId", description = "机构账户ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{institutionId}/effective")
    public ResultTemplate<String> getEffectiveContractCode(@PathVariable("institutionId") Integer institutionId) {
        return ResultTemplate.success(institutionContractService.getEffectiveContractCode(institutionId));
    }
} 