package com.coocaa.cheese.crm.rpc;

import com.coocaa.ad.common.config.FeignConfig;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.rpc.bean.EvidencePublishCalculateParam;
import com.coocaa.cheese.crm.rpc.vo.EvidencePublishVO;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * sale-cms rpc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02
 */
@FeignClient(value = "cheese-sale-cms-api", path = "/api/sale-cms",
//         url = "http://localhost:8022",
        configuration = FeignConfig.class)
public interface FeignSaleCmsRpc {
    @PostMapping("/evidence-publish/calculate")
    ResultTemplate<List<EvidencePublishVO>> calculatePublishList(@RequestBody @Valid EvidencePublishCalculateParam param);
}
