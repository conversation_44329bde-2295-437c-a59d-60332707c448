package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 转移参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "转移参数")
public class TransferParam {
    @Schema(description = "业务类型 (字典0113)", type = "String", example = "0113-1")
    @NotNull(message = "业务类型不能为空")
    private String bizType;

    @Schema(description = "业务ID列表", type = "List", example = "[1, 2, 3]")
    @NotEmpty(message = "业务ID列表不能为空")
    private List<Integer> bizIds;

    @Schema(description = "目标归属人ID", type = "Integer", example = "101")
    @NotNull(message = "目标归属人不能为空")
    private Integer targetOwnerId;

    @Schema(description = "目标归属人姓名", type = "String", example = "张三")
    private String targetOwnerName;

    @Schema(description = "目标部门ID", type = "Integer", example = "123")
    @NotNull(message = "目标部门不能为空")
    private String targetDepartmentId;

    @Schema(description = "目标部门名称", type = "String", example = "销售部")
    private String targetDepartmentName;

    @Schema(description = "转移原因 (0075)", type = "String", example = "0075-1")
    @NotNull(message = "转移原因不能为空")
    private String transferReason;

    @Schema(description = "备注说明", type = "String", example = "部门调整")
    private String description;
} 