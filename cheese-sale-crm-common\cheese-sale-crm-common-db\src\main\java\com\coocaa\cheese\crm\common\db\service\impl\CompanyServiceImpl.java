package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.CompanyEntity;
import com.coocaa.cheese.crm.common.db.mapper.CompanyMapper;
import com.coocaa.cheese.crm.common.db.service.ICompanyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 公司信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CompanyServiceImpl extends ServiceImpl<CompanyMapper, CompanyEntity> implements ICompanyService {

}
