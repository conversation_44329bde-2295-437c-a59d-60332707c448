package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.cheese.crm.common.db.entity.InstitutionFundChangeRecordEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 机构资金变动记录接口
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
public interface InstitutionFundChangeRecordMapper extends BaseMapper<InstitutionFundChangeRecordEntity> {

    /**
     * 按条件查询资金变动记录列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 资金变动记录列表
     */
    IPage<InstitutionFundChangeRecordEntity> pageList(@Param("page") IPage<InstitutionFundChangeRecordEntity> page,
                                                      @Param("condition") Object condition);
} 