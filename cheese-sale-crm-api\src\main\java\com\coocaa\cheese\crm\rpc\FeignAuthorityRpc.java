package com.coocaa.cheese.crm.rpc;

import com.coocaa.ad.common.core.interceptor.FeignInterceptor;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.rpc.bean.ChatMessageParam;
import com.coocaa.cheese.crm.rpc.bean.DepartmentQueryParam;
import com.coocaa.cheese.crm.rpc.bean.SameDepartmentQueryParam;
import com.coocaa.cheese.crm.rpc.bean.UserDataHighestParam;
import com.coocaa.cheese.crm.rpc.bean.UserMessageParam;
import com.coocaa.cheese.crm.rpc.vo.DepartmentVO;
import com.coocaa.cheese.crm.rpc.vo.ResourceVO;
import com.coocaa.cheese.crm.rpc.vo.UserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 调用权限系统接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-22
 */
@FeignClient(value = "cheese-authority-api",
        // url = "http://beta-cheese-ssp.coocaa.com",
        configuration = FeignInterceptor.class)
public interface FeignAuthorityRpc {
    /**
     * 根据用户ID查询用户信息
     *
     * @param userIds 用户IDs
     * @return 用户名字列表
     */
    @PostMapping("/sys/user/list/ids")
    ResultTemplate<List<UserVO>> getUserByIds(@RequestBody List<Integer> userIds);

    /**
     * 根据用户工号查询用户信息
     *
     * @param userWnos 用户工号列表
     * @return 用户名字列表
     */
    @PostMapping("/sys/user/list/wnos")
    ResultTemplate<List<UserVO>> getUserByWnos(@RequestBody List<String> userWnos);

    /**
     * 根据用户工号查询用户信息
     *
     * @param names 用户姓名列表
     * @return 用户名字列表
     */
    @PostMapping("/sys/user/list/names")
    ResultTemplate<List<UserVO>> getUserByNames(@RequestBody List<String> names);

    /**
     * 根据部门OpenId查询详情
     *
     * @param queryParam 部门查询参数
     * @return 部门列表
     */
    @PostMapping("/sys/department/list")
    ResultTemplate<List<DepartmentVO>> getDepartments(@RequestBody DepartmentQueryParam queryParam);

    /**
     * 查询数据权限用户
     */
    @GetMapping("/sys/user/data-user")
    ResultTemplate<List<CodeNameVO>> listDataUser(@RequestParam(name = "keyword", required = false) String keyword,
                                                  @RequestParam(name = "size", required = false) Integer size);

    /**
     * 根据城市名称获取城市
     *
     * @param name 城市名称
     * @return 城市
     */
    @GetMapping("/sys/city/name/{name}")
    ResultTemplate<CodeNameVO> getCityByName(@PathVariable(name = "name") String name);

    /**
     * 根据ID获取城市名称
     *
     * @param ids 城市ID列表
     * @return 城市名称列表
     */
    @PostMapping("/sys/city/list/ids")
    ResultTemplate<List<CodeNameVO>> listCityByIds(@RequestBody List<Integer> ids);

    /**
     * 查询所有已启用的城市列表
     */
    @GetMapping("/sys/city/list/select")
    ResultTemplate<List<CodeNameVO>> getAvailableCities();

    /**
     * 查询用户拥有的城市列表
     */
    @GetMapping("/sys/city/list/select/auth")
    ResultTemplate<List<CodeNameVO>> listCityByAuthority();

    /**
     * 查询用户拥有的城市列表
     * 如果是全部，仅返回一条 id = 0的数据
     */
    @GetMapping("/sys/user/city")
    ResultTemplate<List<CodeNameVO>> listUserCities();


    /**
     * 根据字典编码获取字典名称
     *
     * @param codes 字典编码列表
     * @return 字典名称列表
     */
    @PostMapping("/sys/dict/list/codes")
    ResultTemplate<List<CodeNameVO>> listDictByCodes(@RequestBody List<String> codes);

    /**
     * 根据父编码获取字典名称
     *
     * @param code 父字典编码
     * @return 字典名称列表
     */
    @GetMapping("/sys/dict/select/{code}")
    ResultTemplate<List<CodeNameVO>> listDictByParent(@PathVariable("code") String code);


    /**
     * 根据行业编码获取行业名称
     *
     * @param codes 行业编码
     * @return 行业名称列表
     */
    @PostMapping("/sys/industry/list/codes")
    ResultTemplate<List<CodeNameVO>> listIndustryByCodes(@RequestBody List<String> codes);

    /**
     * 获取所有二级行业
     *
     * @return 行业名称列表
     */
    @PostMapping("/sys/industry/second/select")
    ResultTemplate<List<CodeNameVO>> listSecondLevelIndustries();


    /**
     * 查询登陆用户允许查看其他用户的数据范围
     * 1. 超管，集合中仅有一条为0的数据
     * 2. 其它情况，集合中包含可以查看的用户ID
     *
     * @param status 查询的用户状态，为true时查询在职用户，为false或者空时查询所有用户
     * @return 用户ID列表
     */
    @GetMapping("/sys/data/permission/detail")
    ResultTemplate<List<Integer>> getScopedUserIds(@RequestParam(name = "status", required = false) Boolean status);

    /**
     * 获取自己及下属
     *
     * @param userId 指定用户ID
     * @return 用户ID列表
     */
    @GetMapping("/sys/data/permission/subordinates")
    ResultTemplate<List<Integer>> getSubordinates(@RequestParam(name = "userId") Integer userId);

    /**
     * 获取自己及下属
     *
     * @return 用户ID列表
     */
    @GetMapping("/sys/department/subordinates")
    ResultTemplate<List<DepartmentVO>> getSubordinatesByDepartmentId();


    /**
     * 获取用户详情
     *
     * @param id      用户id
     * @param encrypt 特殊处理敏感信息和扩展信息
     * @return
     */
    @GetMapping("/sys/user/{id}")
    ResultTemplate<UserVO> getDetail(@PathVariable("id") Integer id,
                                     @RequestParam(name = "encrypt", required = false, defaultValue = "true") boolean encrypt);

    /**
     * 获取本部门及子部门
     */
    @GetMapping("/sys/department/child-department/{departmentOpenId}")
    ResultTemplate<List<DepartmentVO>> getChildDepartmentList(@PathVariable(name = "departmentOpenId") String departmentId);

    /**
     * 获取公司下所有用户
     */
    @GetMapping("/sys/department/user")
    ResultTemplate<List<UserVO>> getUserListByDepartmentId(@RequestHeader(name = "Department-Open-Id") String departmentId);

    /**
     * 判断两个用户是否是同一个部门
     */
    @PostMapping("/sys/department/same-department")
    ResultTemplate<Boolean> sameDepartment(@RequestBody SameDepartmentQueryParam queryParam);

    /**
     * 获取用户资源最高数据权限
     */
    @PostMapping("/sys/resource/user-data-highest")
    ResultTemplate<ResourceVO> getUserDataHighest(@RequestBody UserDataHighestParam param);

    /**
     * 发送消息
     */
    @PostMapping("/sys/message/station/user-message")
    ResultTemplate<?> sendUserMessage(@Validated @RequestBody UserMessageParam param);

    /**
     * 飞书异常消息通知
     *
     * @param param 请求参数
     * @return 返回值
     */
    @PostMapping("/sys/message/chat-message")
    ResultTemplate<String> sendChatMessage(@RequestBody @Validated ChatMessageParam param);
}