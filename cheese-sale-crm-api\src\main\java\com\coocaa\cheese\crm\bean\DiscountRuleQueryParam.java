package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 折扣规则查询参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "折扣规则查询参数")
public class DiscountRuleQueryParam {
    @Schema(description = "折扣规则名称", type = "String", example = "春节促销")
    private String name;

    @Schema(description = "折扣规则编码", type = "String", example = "DIS20250101")
    private String code;

    @Schema(description = "状态", type = "String", example = "1")
    private String status;

    @Schema(description = "城市ID", type = "String", example = "110100")
    private String cityId;
} 