package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.bean.AdvertisingSubjectQueryDTO;
import com.coocaa.cheese.crm.common.db.bean.DashboardCustomerReportSearchDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.SignSubjectApproveDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;

import java.util.List;

/**
 * 签约主体 服务类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface IAdvertisingSubjectService extends IService<AdvertisingSubjectEntity> {
    /**
     * 分页查询签约主体列表
     *
     * @param page      分页条件
     * @param condition 查询条件
     * @return 签约主体数据列表
     */
    IPage<AdvertisingSubjectEntity> pageList(IPage<AdvertisingSubjectEntity> page, AdvertisingSubjectQueryDTO condition);

    /**
     * 分页查询签约主体列表
     *
     * @param page      分页条件
     * @param condition 查询条件
     * @return 签约主体数据列表
     */
    IPage<AdvertisingSubjectEntity> h5PageList(IPage<AdvertisingSubjectEntity> page, AdvertisingSubjectQueryDTO condition);

    /**
     * 分页查询签约主体公海列表
     *
     * @param page          分页条件
     * @param condition     查询条件
     * @param currentUserId 当前用户ID
     * @return 签约主体公海数据列表
     */
    IPage<AdvertisingSubjectEntity> publicSeaPageList(IPage<AdvertisingSubjectEntity> page, AdvertisingSubjectQueryDTO condition, Integer currentUserId);

    /*
     * <AUTHOR>
     * @Description 站内审批查询签约主体信息(主要显示审批信息，主体不关联删除字段)
     * @Date 2025/5/9
     * @Param [bizIds]
     * @return java.util.List<com.coocaa.cheese.crm.common.db.bean.SignSubjectApproveDTO>
     **/
    List<SignSubjectApproveDTO> queryInnerSubjectEntity(List<Integer> bizIds);

    /*
     * <AUTHOR>
     * @Description 站内审批根据签约主体id查询详情(主要显示审批信息，品牌+主体均不关联删除字段)
     * @Date 2025/5/7 
     * @Param [id]
     * @return com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO
     **/
    InnerApproveDetailDTO queryInnerApproveDetail(Integer id);

    /**
     * 查询主体信息
     * @param advertisingSubjectIds 主体id集合
     * @return 主体列表
     */
    List<SignSubjectApproveDTO> innerSubjectEntity(List<Integer> advertisingSubjectIds);

    /**
     * 分页获取客户报备
     * @param page 分页条件
     * @param query 搜索条件
     * @return
     */
    IPage<DashboardCustomerReportSearchDTO> customerReportPageList(Page<DashboardCustomerReportSearchDTO> page, String query);
}
