package com.coocaa.cheese.crm.util;

import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 对字典翻译
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-02
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CodeNameHelper {
    private final FeignAuthorityRpc authorityRpc;
    private final UserCacheHelper userCacheHelper;

    // 城市缓存
    private static final Cache<Integer, String> CITY_CACHE = Caffeine.newBuilder()
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();

    // 字典缓存
    private static final Cache<String, String> DICT_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();


    /**
     * 翻译城市区县名字
     */
    public Map<Integer, String> getCityMapping(Collection<Integer> cityIds) {
        if (CollectionUtils.isEmpty(cityIds)) {
            return Collections.emptyMap();
        }

        // 优先从缓存中取数据
        Set<Integer> ids = Sets.newHashSet(cityIds);
        Map<Integer, String> resultMap = Maps.newHashMapWithExpectedSize(ids.size());
        resultMap.putAll(CITY_CACHE.getAllPresent(ids));
        ids.removeIf(resultMap::containsKey);
        if (CollectionUtils.isEmpty(ids)) {
            log.info("[数据翻译] 所有城市({})[{}]都在本地缓存中!", resultMap.size(),
                    StringUtils.join(resultMap.keySet(), Constants.COMMA));
            return resultMap;
        }

        // 调用服务获取数据
        Map<Integer, String> mapping = getIdNameMapping(ids, authorityRpc::listCityByIds);
        if (MapUtils.isNotEmpty(mapping)) {
            resultMap.putAll(mapping);
            CITY_CACHE.putAll(mapping);
            log.info("[数据翻译] 远程加载城市({})[{}]名称!", ids.size(), StringUtils.join(ids, ","));
        }

        return resultMap;
    }

    /**
     * 翻译用户名字
     */
    public Map<Integer, String> getUserMapping(Collection<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> resultMap = Maps.newHashMapWithExpectedSize(userIds.size());
        Map<Integer, CachedUser> userMapping = userCacheHelper.getUserByIds(userIds);
        if (MapUtils.isNotEmpty(userMapping)) {
            userMapping.forEach((userId, cachedUser) -> resultMap.put(userId, cachedUser.getName()));
        }
        return resultMap;
    }

    /**
     * 翻译用户工号
     */
    public Map<Integer, String> getUserWnoMapping(Collection<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> resultMap = Maps.newHashMapWithExpectedSize(userIds.size());
        Map<Integer, CachedUser> userMapping = userCacheHelper.getUserByIds(userIds);
        if (MapUtils.isNotEmpty(userMapping)) {
            userMapping.forEach((userId, cachedUser) -> resultMap.put(userId, cachedUser.getName() + "(" + cachedUser.getWno() + ")"));
        }
        return resultMap;
    }

    /**
     * 翻译行业名字
     */
    public Map<String, String> getIndustryMapping(Collection<String> codes) {
        return getCodeNameMapping(codes, authorityRpc::listIndustryByCodes);
    }


    /**
     * 通过字典翻译名字
     */
    public Map<String, String> getDictMapping(Collection<String> dictCodes) {
        if (CollectionUtils.isEmpty(dictCodes)) {
            return Collections.emptyMap();
        }

        // 优先从缓存中取数据
        Set<String> codes = Sets.newHashSet(dictCodes);
        Map<String, String> resultMap = Maps.newHashMapWithExpectedSize(codes.size());
        resultMap.putAll(DICT_CACHE.getAllPresent(codes));
        codes.removeIf(resultMap::containsKey);
        if (CollectionUtils.isEmpty(codes)) {
            log.info("[数据翻译] 所有字典({})[{}]都在本地缓存中!", resultMap.size(),
                    StringUtils.join(resultMap.keySet(), Constants.COMMA));
            return resultMap;
        }

        // 调用服务获取数据
        Map<String, String> mapping = getCodeNameMapping(codes, authorityRpc::listDictByCodes);
        if (MapUtils.isNotEmpty(mapping)) {
            resultMap.putAll(mapping);
            DICT_CACHE.putAll(mapping);
            log.info("[数据翻译] 远程加载字典({})[{}]名称!", codes.size(), StringUtils.join(codes, Constants.COMMA));
        }

        return resultMap;
    }


    /**
     * 调用权限系统翻译数据
     */
    private Map<Integer, String> getIdNameMapping(Collection<Integer> ids,
                                                  Function<List<Integer>, ResultTemplate<List<CodeNameVO>>> function) {
        if (CollectionUtils.isEmpty(ids) || Objects.isNull(function)) {
            return Collections.emptyMap();
        }

        // 去掉空ID
        List<Integer> uniqueIds = ids.stream().filter(Objects::nonNull).distinct().toList();
        if (CollectionUtils.isEmpty(uniqueIds)) {
            return Collections.emptyMap();
        }

        return Optional.ofNullable(function.apply(uniqueIds))
                .map(ResultTemplate::getData)
                .map(items -> items.stream().collect(Collectors.toMap(CodeNameVO::getId, CodeNameVO::getName)))
                .orElse(Collections.emptyMap());
    }

    /**
     * 调用权限系统翻译数据
     */
    private Map<String, String> getCodeNameMapping(Collection<String> codes,
                                                   Function<List<String>, ResultTemplate<List<CodeNameVO>>> function) {
        if (CollectionUtils.isEmpty(codes) || Objects.isNull(function)) {
            return Collections.emptyMap();
        }

        // 去掉空编码
        List<String> uniqueCodes = codes.stream().filter(StringUtils::isNotBlank).distinct().toList();
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            return Collections.emptyMap();
        }

        return Optional.ofNullable(function.apply(uniqueCodes))
                .map(ResultTemplate::getData)
                .map(items -> items.stream().collect(Collectors.toMap(CodeNameVO::getCode, CodeNameVO::getName)))
                .orElse(Collections.emptyMap());
    }
}
