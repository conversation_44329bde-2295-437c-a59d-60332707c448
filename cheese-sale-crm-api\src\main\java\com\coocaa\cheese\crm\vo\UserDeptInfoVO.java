package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "UserDeptInfo", description = "用户对象封装")
public class UserDeptInfoVO {
    /**
     * 用户id
     */
    @TransField(type = TransTypes.USER)
    Integer userId;
    String userName;
    /**
     * 部门id
     */
    @TransField(type = TransTypes.DEPARTMENT)
    String departmentId;
    String departmentName;
}