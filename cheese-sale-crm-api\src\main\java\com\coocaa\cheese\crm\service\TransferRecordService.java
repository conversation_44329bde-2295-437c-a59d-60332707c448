package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.TransferRecordQueryParam;
import com.coocaa.cheese.crm.common.db.entity.TransferRecordEntity;
import com.coocaa.cheese.crm.common.db.service.ITransferRecordService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.convert.TransferRecordConvert;
import com.coocaa.cheese.crm.vo.TransferRecordVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 转移记录服务
 * 只负责记录的创建和查询，不包含业务逻辑
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class TransferRecordService {
    private final ITransferRecordService transferRecordService;

    /**
     * 分页查询转移记录
     *
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    @AutoTranslate
    public PageResponseVO<TransferRecordVO> pageListTransferRecord(PageRequestVO<TransferRecordQueryParam> pageRequest) {
        // 构建分页对象
        Page<TransferRecordEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));

        // 构建查询条件
        TransferRecordQueryParam query = pageRequest.getQuery();
        IPage<TransferRecordEntity> pageResult = transferRecordService.lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getBizType()), TransferRecordEntity::getBizType, query.getBizType())
                .eq(Objects.nonNull(query.getBizId()), TransferRecordEntity::getBizId, query.getBizId())
                .eq(Objects.nonNull(query.getSourceOwnerId()), TransferRecordEntity::getSourceOwnerId, query.getSourceOwnerId())
                .eq(Objects.nonNull(query.getTargetOwnerId()), TransferRecordEntity::getTargetOwnerId, query.getTargetOwnerId())
                .eq(TransferRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(TransferRecordEntity::getCreateTime)
                .page(page);
        // 构建分页响应
        return TransferRecordConvert.INSTANCE.toPageResponse(pageResult);
    }

    /**
     * 获取业务转移记录列表
     *
     * @param bizType 业务类型
     * @param bizId   业务ID
     * @return 转移记录列表
     */
    @AutoTranslate
    public List<TransferRecordVO> listTransferRecord(String bizType, Integer bizId) {
        // 查询转移记录
        List<TransferRecordEntity> recordList = transferRecordService.lambdaQuery()
                .eq(TransferRecordEntity::getBizType, bizType)
                .eq(TransferRecordEntity::getBizId, bizId)
                .eq(TransferRecordEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(TransferRecordEntity::getCreateTime)
                .list();
        // 转换为VO
        return TransferRecordConvert.INSTANCE.entityToVO(recordList);
    }
} 