package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.BrandParam;
import com.coocaa.cheese.crm.bean.BrandQueryH5Param;
import com.coocaa.cheese.crm.bean.BrandQueryParam;
import com.coocaa.cheese.crm.bean.BrandReportH5Param;
import com.coocaa.cheese.crm.common.db.bean.BrandQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandTagEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.CompanyEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineEntity;
import com.coocaa.cheese.crm.common.db.mapper.ProductLineMapper;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBrandService;
import com.coocaa.cheese.crm.common.db.service.IBrandTagService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.ICompanyService;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.convert.BrandConvert;
import com.coocaa.cheese.crm.convert.BrandH5Convert;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import com.coocaa.cheese.crm.vo.BrandH5VO;
import com.coocaa.cheese.crm.vo.BrandVO;
import com.coocaa.cheese.crm.vo.ProductLineDetailVO;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 品牌管理服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BrandService {
    private final IBrandService brandService;
    private final IBrandTagService brandTagService;
    private final IBusinessService businessService;
    private final IAdvertisingSubjectService advertisingSubjectService;
    private final IProductLineService productLineService;
    private final ProductLineService productLineServiceManager;
    private final ProductLineMapper productLineMapper;
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final ICompanyService companyService;

    /**
     * 品牌列表(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的品牌列表
     */
    @AutoTranslate
    public PageResponseVO<BrandVO> pageList(@RequestBody PageRequestVO<BrandQueryParam> pageRequest) {
        // 转换查询参数
        BrandQueryDTO queryDto = new BrandQueryDTO()
                .setName(StringUtils.trimToNull(pageRequest.getQuery().getName()))
                .setIndustryCode(StringUtils.trimToNull(pageRequest.getQuery().getIndustryCode()))
                .setCompanyName(StringUtils.trimToNull(pageRequest.getQuery().getCompanyName()))
                .setEffectiveStatus(pageRequest.getQuery().getEffectiveStatus())
                .setCreateEndDate(pageRequest.getQuery().getCreateEndDate())
                .setCreateStartDate(pageRequest.getQuery().getCreateStartDate())
                .setCreator(pageRequest.getQuery().getCreator())
                .setDepartmentId(pageRequest.getQuery().getDepartmentId());
        // 分页查询
        IPage<BrandEntity> pageBrands = brandService.pageList(getPage(pageRequest), queryDto);
        PageResponseVO<BrandVO> pageResponse = new PageResponseVO<>();
        pageResponse.setCurrentPage(pageBrands.getCurrent());
        pageResponse.setTotalRows(pageBrands.getTotal());
        pageResponse.setTotal(pageBrands.getTotal());
        pageResponse.setPageSize(pageBrands.getSize());
        pageResponse.setTotalPages(pageBrands.getPages());

        // 没查到数据，直接返回
        if (CollectionUtils.isEmpty(pageBrands.getRecords())) {
            return pageResponse;
        }

        // 转换VO, 填充标签
        Set<Integer> brandIds = pageBrands.getRecords().stream().map(BrandEntity::getId).collect(Collectors.toSet());
        Map<Integer, List<BrandTagEntity>> brandTagGroupedMap = brandTagService.lambdaQuery()
                .in(BrandTagEntity::getBrandId, brandIds)
                .list().stream().collect(Collectors.groupingBy(BrandTagEntity::getBrandId));

        // 填充返回数据
        pageResponse.setRows(pageBrands.getRecords().stream()
                .map(brand -> toVo(brand, brandTagGroupedMap.get(brand.getId())))
                .toList());

        return pageResponse;
    }

    /**
     * 品牌列表(分页)
     *
     * @param pageRequest 分页查询条件
     * @return 分页的品牌列表
     */
    @AutoTranslate
    public PageResponseVO<BrandH5VO> h5PageList(@RequestBody PageRequestVO<BrandQueryH5Param> pageRequest) {
        // 转换查询参数
        BrandQueryDTO queryDto = new BrandQueryDTO()
                .setName(StringUtils.trimToNull(pageRequest.getQuery().getName()))
                .setEffectiveStatus(BooleFlagEnum.YES.getCode());
        // 分页查询
        IPage<BrandEntity> pageBrands = brandService.h5PageList(getPage(pageRequest), queryDto);
        return BrandH5Convert.INSTANCE.toPageResponse(pageBrands);
    }

    /**
     * 品牌列表(不分页)
     *
     * @param name  品牌名称
     * @param exact 是否精确查询
     * @return 品牌列表
     */
    public List<CodeNameVO> listBrands(String name, boolean exact) {
        Page<BrandEntity> page = new Page<>(1, -1);
        BrandQueryDTO queryDto = new BrandQueryDTO()
                .setExactFlag(exact ? BooleFlagEnum.YES.getCode() : BooleFlagEnum.NO.getCode())
                .setName(StringUtils.trimToNull(name))
                .setEffectiveStatus(BooleFlagEnum.YES.getCode());
        IPage<BrandEntity> pageBrands = brandService.pageList(page, queryDto);

        // 没查到数据，直接返回
        if (CollectionUtils.isEmpty(pageBrands.getRecords())) {
            return Collections.emptyList();
        }

        return pageBrands.getRecords().stream()
                .map(brand -> CodeNameVO.builder().id(brand.getId()).name(brand.getName()).build())
                .toList();
    }

    /**
     * 根据品牌ID查询列表
     *
     * @param ids 品牌ID列表
     * @return 品牌列表
     */
    public List<CodeNameVO> listByIds(Collection<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        Set<Integer> uniqueIds = ids.stream().filter(Objects::nonNull).filter(id -> id > 0).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(uniqueIds)) {
            return Collections.emptyList();
        }

        return brandService.lambdaQuery()
                .select(BrandEntity::getId, BrandEntity::getName)
                .eq(BrandEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(BrandEntity::getId, uniqueIds)
                .list().stream()
                .map(item -> CodeNameVO.builder().id(item.getId()).name(item.getName()).build())
                .toList();
    }

    /**
     * 品牌详情
     *
     * @param id 品牌ID
     * @return 品牌详情
     */
    @AutoTranslate
    public BrandVO getDetail(Integer id) {
        BrandEntity brandEntity = Optional.ofNullable(brandService.getById(id))
                .orElseThrow(() -> new BusinessException("品牌不存在"));
        BrandVO vo = toVo(brandEntity, brandTagService.lambdaQuery().eq(BrandTagEntity::getBrandId, id).list());
        if (Objects.nonNull(vo)) {
            // 设置产品线
            vo.setProductLineNameList(productLineMapper.getProductLineNameList(id));
            //设置社会统一信用代码
            if (Objects.nonNull(brandEntity.getCompanyId())) {
                CompanyEntity entity = companyService.lambdaQuery().eq(CompanyEntity::getId, brandEntity.getCompanyId()).one();
                vo.setCreditCode(Objects.nonNull(entity) ? entity.getCode() : Strings.EMPTY);
            }
        }
        return vo;
    }

    /**
     * 品牌创建或修改
     *
     * @param id    品牌ID
     * @param param 品牌信息
     * @return true: 创建或修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createOrUpdate(Integer id, BrandParam param) {
        // 检查是否已存在
        Set<String> existedNames = getExistedNames(id, param);
        if (CollectionUtils.isNotEmpty(existedNames)) {
            throw new BusinessException(StringUtils.join(existedNames, "、") + "与已有名称重复");
        }

        // 创建或更新品牌
        BrandEntity entity = BrandConvert.INSTANCE.toEntity(param);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());
        entity.setId(id);
        boolean result;
        if (Objects.isNull(id)) {
            entity.setEffectiveStatus(BooleFlagEnum.YES.getCode());
            entity.setEffectiveTime(LocalDateTime.now());
            result = brandService.save(entity);
        } else {
            // 先查出来旧的记录，判断是否需要更新生效时间
            // 只有首次否变成是才更新生效时间
            BrandEntity oldEntity = brandService.getById(id);
            if (BooleFlagEnum.NO.getCode().equals(oldEntity.getEffectiveStatus())
                    && BooleFlagEnum.YES.getCode().equals(entity.getEffectiveStatus())
                    && Objects.isNull(entity.getEffectiveTime())) {
                entity.setEffectiveTime(LocalDateTime.now());
            }
            result = brandService.updateById(entity);
        }

        // 先删除品牌标签，再增加
        if (Objects.nonNull(id)) {
            brandTagService.lambdaUpdate().eq(BrandTagEntity::getBrandId, id).remove();
        }
        if (CollectionUtils.isNotEmpty(param.getTags())) {
            List<BrandTagEntity> tags = param.getTags().stream().map(tag -> {
                BrandTagEntity tagEntity = new BrandTagEntity();
                tagEntity.setBrandId(entity.getId());
                tagEntity.setName(tag);
                return tagEntity;
            }).toList();
            brandTagService.saveBatch(tags);
        }
        return result;
    }

    /**
     * 品牌删除
     *
     * @param id 品牌ID
     * @return true: 删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Integer id) {
        // 检查品牌是否存在
        BrandEntity entity = brandService.lambdaQuery().eq(BrandEntity::getId, id).last("limit 1").one();
        if (Objects.isNull(entity)) {
            throw new BusinessException("品牌不存在，删除失败");
        }

        // 已关联商机
        if (businessService.lambdaQuery()
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getBrandId, id).exists()) {
            throw new BusinessException("该品牌已关联商机，禁止删除");
        }

        // 已经关联签约主体
        if (advertisingSubjectService.lambdaQuery()
                .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(AdvertisingSubjectEntity::getBrandId, id).exists()) {
            throw new BusinessException("该品牌已关联签约主体，禁止删除");
        }

        // 删除品牌的标签
        brandTagService.lambdaUpdate().eq(BrandTagEntity::getBrandId, id).remove();

        // 若发生品牌删除，需同步逻辑删除品牌关联的《产品线数据表》
        this.productLineService.remove(new LambdaQueryWrapper<>(ProductLineEntity.class).eq(ProductLineEntity::getBrandId, id));

        // 置空子品牌的母品牌
        brandService.lambdaUpdate()
                .set(BrandEntity::getParentId, null)
                .eq(BrandEntity::getParentId, id)
                .update();

        // 删除品牌
        return brandService.lambdaUpdate()
                .set(BrandEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(BrandEntity::getId, id)
                .update();
    }

    /**
     * 获取分页对象
     */
    private Page<BrandEntity> getPage(PageRequestVO<?> pageRequest) {
        // 分页查询列表，自定义统计SQL
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));
    }


    /**
     * 转换成VO
     */
    private BrandVO toVo(BrandEntity brand, List<BrandTagEntity> brandTags) {
        // 转换成VO
        BrandVO vo = BrandConvert.INSTANCE.toVo(brand);
        if (CollectionUtils.isEmpty(brandTags)) {
            return vo;
        }

        // 设置用于显示的标签
        vo.setTagNames(brandTags.stream().map(BrandTagEntity::getName).distinct().sorted().collect(Collectors.joining(Constants.COMMA)));
        vo.setTags(brandTags.stream().map(BrandConvert.INSTANCE::toVo).toList());
        return vo;
    }

    /**
     * 检查名称是否重复
     *
     * @param brandId 品牌ID, 修改时排除自己
     * @param brand   品牌及标签
     * @return true: 重复
     */
    private Set<String> getExistedNames(Integer brandId, BrandParam brand) {
        Set<String> names = Sets.newHashSet();
        names.add(brand.getName());

        // 检查未入库前标签是否重复
        Set<String> allExistedNames = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(brand.getTags())) {
            for (String tag : brand.getTags()) {
                if (!names.add(tag)) {
                    allExistedNames.add(tag);
                }
            }
        }

        // 标签与名称重复
        if (CollectionUtils.isNotEmpty(allExistedNames)) {
            return allExistedNames;
        }

        if (CollectionUtils.isEmpty(names)) {
            return Collections.emptySet();
        }

        Set<String> uniqNames = names.stream().filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(names)) {
            return Collections.emptySet();
        }

        // 查品牌主表
        Set<String> existedNames = brandService.lambdaQuery()
                .select(BrandEntity::getName)
                .eq(BrandEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .ne(Objects.nonNull(brandId), BrandEntity::getId, brandId)
                .in(BrandEntity::getName, uniqNames)
                .list().stream().map(BrandEntity::getName).collect(Collectors.toSet());

        // 查标签表
        Set<String> existedTagNames = brandTagService.lambdaQuery()
                .select(BrandTagEntity::getName)
                .ne(Objects.nonNull(brandId), BrandTagEntity::getBrandId, brandId)
                .in(BrandTagEntity::getName, uniqNames)
                .list().stream().map(BrandTagEntity::getName).collect(Collectors.toSet());


        if (CollectionUtils.isNotEmpty(existedNames)) {
            allExistedNames.addAll(existedNames);
        }

        if (CollectionUtils.isNotEmpty(existedTagNames)) {
            allExistedNames.addAll(existedTagNames);
        }
        return allExistedNames;
    }

    /**
     * 品牌H5保存
     */
    @Transactional(rollbackFor = Exception.class)
    public BrandEntity brandH5Save(BrandReportH5Param param) {
        brandCheck(param);
        BrandEntity entity = BrandConvert.INSTANCE.toEntity(param);
        entity.setDepartmentId(UserThreadLocal.getDeptId());
        brandService.save(entity);
        if (CollectionUtils.isNotEmpty(param.getTags())) {
            List<BrandTagEntity> tags = param.getTags().stream().map(tag -> {
                BrandTagEntity tagEntity = new BrandTagEntity();
                tagEntity.setBrandId(entity.getId());
                tagEntity.setName(tag);
                return tagEntity;
            }).toList();
            brandTagService.saveBatch(tags);
        }
        return entity;
    }

    /**
     * 品牌创建参数检验
     */
    private void brandCheck(BrandReportH5Param param) {
        if (StringUtils.isBlank(param.getName())) {
            throw new BusinessException("品牌名称不能为空");
        }
        if (StringUtils.isBlank(param.getIndustryCode())) {
            throw new BusinessException("二级行业编码不能为空");
        }
        if (Objects.isNull(param.getCompanyId())) {
            throw new BusinessException("品牌持有人不能为空");
        }
        // 校验品牌是否存在
        BrandEntity brandEntity = brandService.lambdaQuery()
                .eq(BrandEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BrandEntity::getName, param.getName())
                .last("limit 1")
                .one();
        if (Objects.nonNull(brandEntity) && brandEntity.getEffectiveStatus().equals(BooleFlagEnum.NO.getCode())) {
            throw new BusinessException("该品牌在报备审批中，请勿重复报备");
        }

        // 校验名称是否重复
        Set<String> existedNames = getExistedNames(null, BrandConvert.INSTANCE.toParam(param));
        if (CollectionUtils.isNotEmpty(existedNames)) {
            throw new BusinessException(StringUtils.join(existedNames, "、") + "与已有名称重复");
        }
    }


    /**
     * 查询指定品牌的关联产品线，并包含公司信息
     */
    @AutoTranslate
    public ProductLineDetailVO getProductLinesByBrandId(Integer brandId) {
        // 查询品牌是否存在
        BrandEntity brand = brandService.getById(brandId);
        if (brand == null) {
            throw new BusinessException("品牌不存在");
        }
        // 构造 VO 返回结果
        return new ProductLineDetailVO()
                .setBrandId(brandId)
                .setCompanyId(brand.getCompanyId())
                .setProductLineNames(productLineServiceManager.getProductLineByBrandId(brandId));
    }
}
