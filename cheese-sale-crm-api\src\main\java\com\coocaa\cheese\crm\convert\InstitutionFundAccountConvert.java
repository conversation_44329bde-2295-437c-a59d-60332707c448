package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.entity.InstitutionFundAccountEntity;
import com.coocaa.cheese.crm.vo.InstitutionFundAccountVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 机构资金账户信息转换
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Mapper
public interface InstitutionFundAccountConvert extends PageableConvert<InstitutionFundAccountEntity, InstitutionFundAccountVO> {
    InstitutionFundAccountConvert INSTANCE = Mappers.getMapper(InstitutionFundAccountConvert.class);
    /**
     * Entity 转 VO
     */
    InstitutionFundAccountVO entityToVO(InstitutionFundAccountEntity entity);

    /**
     * Entity列表 转 VO列表
     */
    List<InstitutionFundAccountVO> entityToVO(List<InstitutionFundAccountEntity> entityList);
} 