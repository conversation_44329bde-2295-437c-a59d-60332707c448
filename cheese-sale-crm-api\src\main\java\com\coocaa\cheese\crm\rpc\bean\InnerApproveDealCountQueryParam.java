package com.coocaa.cheese.crm.rpc.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Schema(name = "InnerApproveDealCountQueryParam", description = "查询站内审批待办任务数量入参")
public class InnerApproveDealCountQueryParam {

    @Schema(description = "审批规则编号")
    private List<Integer> codes;

    @Schema(description = "查询用户，不传默认为登陆人")
    private Integer userId;
}
