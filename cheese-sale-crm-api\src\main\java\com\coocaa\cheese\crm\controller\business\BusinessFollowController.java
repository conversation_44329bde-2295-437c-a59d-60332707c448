package com.coocaa.cheese.crm.controller.business;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.BusinessFollowParam;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.BusinessFollowService;
import com.coocaa.cheese.crm.vo.BusinessFollowVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商机跟进记录管理
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@RestController
@RequestMapping("/business-follows")
@Tag(name = "商机跟进记录管理", description = "商机跟进记录管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessFollowController extends BaseController {
    private final BusinessFollowService businessFollowService;

    /**
     * 跟进记录详情
     */
    @Operation(summary = "跟进记录详情")
    @Parameter(name = "id", description = "跟进记录ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<BusinessFollowVO> getFollowDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessFollowService.getFollowDetail(id));
    }

    /**
     * 跟进记录创建
     */
    @Operation(summary = "创建跟进记录")
    @PostMapping
    public ResultTemplate<Boolean> createFollow(@RequestBody @Validated BusinessFollowParam follow) {
        return ResultTemplate.success(businessFollowService.createOrUpdateFollow(null, follow));
    }

    /**
     * 跟进记录修改
     */
    @Operation(summary = "跟进记录修改")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> updateFollow(@PathVariable("id") Integer id, @RequestBody BusinessFollowParam follow) {
        return ResultTemplate.success(businessFollowService.createOrUpdateFollow(id, follow));
    }

    /**
     * 跟进记录删除
     */
    @Operation(summary = "跟进记录删除")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> deleteFollow(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessFollowService.deleteFollow(id));
    }
} 