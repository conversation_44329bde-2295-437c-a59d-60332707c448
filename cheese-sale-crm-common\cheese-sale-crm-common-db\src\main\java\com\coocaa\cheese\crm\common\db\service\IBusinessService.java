package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.bean.BusinessDelayDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerProductLineDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseBusinessDetailDTO;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;

import java.time.LocalDate;
import java.util.List;

/**
 * 商机服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-19
 */
public interface IBusinessService extends IService<BusinessEntity> {
    /**
     * 发起延期申请查询商机信息
     */
    InnerReleaseBusinessDetailDTO queryBusinessDetailForInner(Integer id);

    /**
     * 产品线变更查询商机信息
     */
    InnerProductLineDetailDTO queryDetailForProductLine(Integer id);

    /**
     * 查询待释放商机列表
     */
    List<BusinessDelayDTO> queryBusinessToBeReleased(LocalDate threeDaysLater);
}