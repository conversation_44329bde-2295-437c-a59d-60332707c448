package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.AdvertisingSubjectPublicSeaQueryParam;
import com.coocaa.cheese.crm.common.db.bean.AdvertisingSubjectQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.vo.AdvertisingSubjectPublicSeaVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 签约主体公海转换器
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Mapper
public interface AdvertisingSubjectPublicSeaConvert extends PageableConvert<AdvertisingSubjectEntity, AdvertisingSubjectPublicSeaVO> {
    AdvertisingSubjectPublicSeaConvert INSTANCE = Mappers.getMapper(AdvertisingSubjectPublicSeaConvert.class);

    /**
     * 查询参数转换为DTO
     */
    AdvertisingSubjectQueryDTO toDto(AdvertisingSubjectPublicSeaQueryParam param);

    /**
     * 实体转换为VO
     */
    AdvertisingSubjectPublicSeaVO toVo(AdvertisingSubjectEntity entity);

}
