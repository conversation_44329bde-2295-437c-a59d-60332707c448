package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 站内审批业务关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
public interface IInnerApproveService extends IService<InnerApproveEntity> {

    /*
     * <AUTHOR>
     * @Description 根据签约主体id查询审批code
     * @Date 2025/5/6 
     * @Param [id, bizType]
     * @return java.lang.String
     **/
    String queryInstanceCodeById(Long id, String bizType);

    /*
     * <AUTHOR>
     * @Description 查询审批中的站内审批code对应的签约主体id
     * @Date 2025/5/7
     * @Param [codes, bizType]
     * @return java.util.List<com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity>
     **/
    List<InnerApproveEntity> queryBizId(Set<String> codes, String bizType);
}
