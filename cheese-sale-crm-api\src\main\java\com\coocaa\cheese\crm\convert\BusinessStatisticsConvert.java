package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.vo.BusinessStatisticsVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商机信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BusinessStatisticsConvert extends PageableConvert<BusinessEntity, BusinessStatisticsVO> {
    BusinessStatisticsConvert INSTANCE = Mappers.getMapper(BusinessStatisticsConvert.class);

    /**
     * Entity 转 VO
     */
    List<BusinessStatisticsVO> toList(List<BusinessEntity> entity);
} 