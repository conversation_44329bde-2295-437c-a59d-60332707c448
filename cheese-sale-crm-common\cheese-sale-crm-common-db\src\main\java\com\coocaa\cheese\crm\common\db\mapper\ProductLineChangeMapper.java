package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.cheese.crm.common.db.bean.InnerProductApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineChangePageDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineDTO;
import com.coocaa.cheese.crm.common.db.entity.ProductLineChangeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品线变更记录表接口
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
public interface ProductLineChangeMapper extends BaseMapper<ProductLineChangeEntity> {

    /*
     * <AUTHOR>
     * @Description 产品线变更审批分页查询数据
     * @Date 2025/6/20 
     * @Param [bizIds]
     * @return java.util.List<com.coocaa.cheese.crm.common.db.bean.ProductLineChangePageDTO>
     **/
    List<ProductLineChangePageDTO> queryInnerProductLineChange(@Param("bizIds") List<Long> bizIds);

    /*
     * <AUTHOR>
     * @Description 产品线变更审批详情查询品牌+主体信息
     * @Date 2025/6/20
     * @Param [id]
     * @return com.coocaa.cheese.crm.common.db.bean.InnerProductApproveDetailDTO
     **/
    InnerProductApproveDetailDTO queryInnerApproveDetail(Long id);

    /*
     * <AUTHOR>
     * @Description 根据签约主体查询产品线信息
     * @Date 2025/6/25
     * @Param [id]
     * @return java.util.List<com.coocaa.cheese.crm.common.db.bean.ProductLineDTO>
     **/
    List<ProductLineDTO> queryProductLineBySign(Integer id);
}