package com.coocaa.cheese.crm.service;

import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.audit.service.AbstractApproveService;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTemplateParam;
import com.coocaa.cheese.crm.bean.PublicSeaAuthParam;
import com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.SignSubjectApproveDTO;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaAuthEntity;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaQuotaEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IPublicSeaAuthService;
import com.coocaa.cheese.crm.common.db.service.IPublicSeaQuotaService;
import com.coocaa.cheese.crm.common.tools.config.api.InnerApproveConfig;
import com.coocaa.cheese.crm.common.tools.enums.ApproveFieldTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.ExecuteStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.convert.BusinessConvert;
import com.coocaa.cheese.crm.convert.InnerApproveConvert;
import com.coocaa.cheese.crm.convert.PublicSeaAuthConvert;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveApplyVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveTaskVO;
import com.coocaa.cheese.crm.vo.BusinessHisVO;
import com.coocaa.cheese.crm.vo.InnerApprovePublicSeaAuthDetailVO;
import com.coocaa.cheese.crm.vo.InnerApprovePublicSeaAuthPageVO;
import com.coocaa.cheese.crm.vo.UserDeptInfoVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 打捞申请记录表 业务实现类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
@Service
@Primary
public class PublicSeaAuthService extends AbstractApproveService<
        InnerApprovePublicSeaAuthDetailVO, PublicSeaAuthParam,
        InnerApprovePublicSeaAuthPageVO, InnerApproveTaskQueryParam> {

    @Resource
    private IPublicSeaAuthService iPublicSeaAuthService;
    @Resource
    private ProductLineService productLineServiceManager;
    @Resource
    private IAdvertisingSubjectService iAdvertisingSubjectService;
    @Resource
    private IPublicSeaQuotaService iPublicSeaQuotaService;
    @Resource
    private IBusinessService iBusinessService;

    /**
     * 构造函数
     */
    public PublicSeaAuthService(InnerApproveConfig innerApproveConfig, InnerApproveService approveService,
                                IInnerApproveService innerApproveService) {
        super(innerApproveConfig, approveService, innerApproveService);
    }

    @Override
    protected StationTargetEnum getStationTarget() {
        return StationTargetEnum.PUBLIC_SEA_SALVAGE;
    }

    @Override
    protected Integer getInnerApproveConfig() {
        return innerApproveConfig.getSea();
    }

    @Override
    protected InnerApprovePublicSeaAuthDetailVO getInnerApproveDetail(Long id, Long bizId) {
        //查询投放主体关联品牌
        InnerApproveDetailDTO dto = iAdvertisingSubjectService.queryInnerApproveDetail(bizId.intValue());
        log.info("发起打捞申请查询投放主体id:{}, dto:{}", id, JSONUtil.toJsonStr(dto));
        InnerApprovePublicSeaAuthDetailVO vo = PublicSeaAuthConvert.INSTANCE.dtoToVo(dto);
        vo.setId(id);
        vo.setBizId(bizId.intValue());
        vo.setProductLineNames(productLineServiceManager.getProductLineBySubjectId(bizId.intValue()));
        if (id != null) {
            String remark = iPublicSeaAuthService.lambdaQuery()
                    .select(PublicSeaAuthEntity::getRemark)
                    .eq(PublicSeaAuthEntity::getId, id)
                    .one()
                    .getRemark();
            vo.setDescription(remark);
        }
        return vo;
    }

    @Override
    protected Long getInnerApproveDetailBizId(Long id) {
        return iPublicSeaAuthService.lambdaQuery()
                .select(PublicSeaAuthEntity::getBizId)
                .eq(PublicSeaAuthEntity::getId, id)
                .oneOpt()
                .orElseThrow(() -> new BusinessException("打捞申请记录表不存在！"))
                .getBizId()
                .longValue();
    }

    @Override
    protected Long saveRecord(PublicSeaAuthParam param) {
        //保存打捞申请记录表
        PublicSeaAuthEntity entity = PublicSeaAuthConvert.INSTANCE.toEntity(param);
        entity.setDepartmentId(UserThreadLocal.getDeptId());
        boolean save = iPublicSeaAuthService.save(entity);
        if (!save) {
            throw new BusinessException("打捞申请记录表创建失败!");
        }
        return entity.getId();
    }

    @Override
    protected List<InnerApproveTemplateParam> buildApproveList(PublicSeaAuthParam publicSeaAuthParam) {
        return List.of(new InnerApproveTemplateParam("creator", ApproveFieldTypeEnum.NUMBER.getCode(),
                UserThreadLocal.getUserId().toString()));
    }

    @Override
    protected List<InnerApprovePublicSeaAuthPageVO> setApproveTaskData(List<InnerApproveTaskVO> rowList,
                                                                       PageRequestVO<InnerApproveTaskQueryParam> e,
                                                                       List<InnerApproveEntity> innerApproveEntities) {

        // 查询审批实例关联的申请列表
        List<PublicSeaAuthEntity> publicSeaAuthEntities = getPublicSeaAuthEntities(innerApproveEntities);

        if (publicSeaAuthEntities.isEmpty()) {
            return Collections.emptyList();
        }

        // Step 3: 提取所有 bizId 用于查询
        List<Integer> realBizIds = publicSeaAuthEntities.stream()
                .map(PublicSeaAuthEntity::getBizId)
                .distinct()
                .toList();

        //查询业务数据
        Map<Long, SignSubjectApproveDTO> subjectMap = getSignSubjectApprove(publicSeaAuthEntities, realBizIds);
        //产品线名称
        Map<Integer, String> productLineNames = productLineServiceManager.getProductLineBySubjectId(realBizIds);
        return rowList.stream()
                .map(row -> {
                    InnerApprovePublicSeaAuthPageVO vo = InnerApproveConvert.INSTANCE.toPageVo(row);
                    vo.setDepartmentId(row.getDepartId());
                    return getInnerApprovePublicSeaAuthPageVO(innerApproveEntities, subjectMap, productLineNames, vo, row.getInstanceCode());
                })
                .toList();
    }

    private List<PublicSeaAuthEntity> getPublicSeaAuthEntities(List<InnerApproveEntity> innerApproveEntities) {
        // Step 1: 提取 bizId 列表并构建 bizId -> PublicSeaAuthEntity.id 的映射
        List<Long> bizIds = innerApproveEntities.stream()
                .map(InnerApproveEntity::getBizId)
                .distinct()
                .toList();

        // Step 2: 查询 PublicSeaAuthEntity 列表
        return iPublicSeaAuthService.lambdaQuery()
                .select(PublicSeaAuthEntity::getId, PublicSeaAuthEntity::getBizId)
                .in(PublicSeaAuthEntity::getId, bizIds)
                .list();
    }


    @Override
    protected List<InnerApprovePublicSeaAuthPageVO> setApproveApplyData(List<InnerApproveApplyVO> rowList,
                                                                        PageRequestVO<InnerApproveTaskQueryParam> e,
                                                                        List<InnerApproveEntity> innerApproveEntities) {
        // 查询审批实例关联的申请列表
        List<PublicSeaAuthEntity> publicSeaAuthEntities = getPublicSeaAuthEntities(innerApproveEntities);

        if (publicSeaAuthEntities.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取所有 bizId 用于查询
        List<Integer> realBizIds = publicSeaAuthEntities.stream()
                .map(PublicSeaAuthEntity::getBizId)
                .distinct()
                .toList();

        //查询业务数据
        Map<Long, SignSubjectApproveDTO> subjectMap = getSignSubjectApprove(publicSeaAuthEntities, realBizIds);
        //产品线名称
        Map<Integer, String> productLineNames = productLineServiceManager.getProductLineBySubjectId(realBizIds);

        return rowList.stream()
                .map(row -> {
                    InnerApprovePublicSeaAuthPageVO vo = InnerApproveConvert.INSTANCE.toPageVo(row);
                    vo.setInstanceCreateTime(row.getCreateTime());
                    vo.setDepartmentId(row.getDepartId());
                    return getInnerApprovePublicSeaAuthPageVO(innerApproveEntities, subjectMap, productLineNames, vo, row.getInstanceCode());
                })
                .toList();
    }

    /**
     * 根据 InnerApproveEntities 获取 SignSubjectApproveDTO 的 Map
     *
     * @param publicSeaAuthEntities 审批实体列表
     * @return authId -> SignSubjectApproveDTO 的映射
     */
    private Map<Long, SignSubjectApproveDTO> getSignSubjectApprove(List<PublicSeaAuthEntity> publicSeaAuthEntities, List<Integer> realBizIds) {

        // Step 1: 构建 authId -> bizId 映射
        Map<Long, Integer> authIdToBizId = publicSeaAuthEntities.stream()
                .collect(Collectors.toMap(
                        PublicSeaAuthEntity::getId,
                        PublicSeaAuthEntity::getBizId
                ));
        // Step 2: 查询 SignSubjectApproveDTO 列表
        List<SignSubjectApproveDTO> subjectApproveList = iAdvertisingSubjectService.queryInnerSubjectEntity(realBizIds);

        // Step 3: 构建 bizId -> SignSubjectApproveDTO 映射
        Map<Integer, SignSubjectApproveDTO> bizIdToSubject = subjectApproveList.stream()
                .collect(Collectors.toMap(
                        SignSubjectApproveDTO::getId,
                        dto -> dto
                ));

        // Step 4: 构建 authId -> SignSubjectApproveDTO 映射
        return authIdToBizId.entrySet().stream()
                .filter(entry -> bizIdToSubject.containsKey(entry.getValue()))
                .collect(Collectors.toMap(
                        // authId 作为 Key
                        Map.Entry::getKey,
                        // SignSubjectApproveDTO 作为 Value
                        entry -> bizIdToSubject.get(entry.getValue())
                ));
    }


    private InnerApprovePublicSeaAuthPageVO getInnerApprovePublicSeaAuthPageVO(List<InnerApproveEntity> innerApproveEntities,
                                                                               Map<Long, SignSubjectApproveDTO> subjectMap,
                                                                               Map<Integer, String> productLineNames,
                                                                               InnerApprovePublicSeaAuthPageVO vo,
                                                                               String instanceCode) {
        Map<String, InnerApproveEntity> examineApproveEntityMap = innerApproveEntities.stream()
                .collect(Collectors.toMap(InnerApproveEntity::getInstanceCode, entity -> entity));

        Long bizId = examineApproveEntityMap.get(instanceCode).getBizId();
        if (!examineApproveEntityMap.containsKey(instanceCode) || !subjectMap.containsKey(bizId)) {
            return vo;
        }
        SignSubjectApproveDTO entity = subjectMap.get(bizId);
        vo.setId(bizId);
        vo.setBizId(entity.getId());
        vo.setBrandId(entity.getBrandId());
        vo.setCompanyId(entity.getCompanyId());
        vo.setProductLineNames(productLineNames.get(entity.getId()));
        return vo;
    }

    @Override
    protected void checkInitiate(PublicSeaAuthParam param) {
        log.info("发起打捞申请参数:{}", JSONUtil.toJsonStr(param));
    }

    // 是否可以直接创建商机
    public boolean canBusinessCreate(Integer bizId) {
        PublicSeaQuotaEntity quota = iPublicSeaQuotaService.lambdaQuery()
                .eq(PublicSeaQuotaEntity::getUserId, UserThreadLocal.getUserId())
                .eq(PublicSeaQuotaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .last("limit 1")
                .orderByDesc(PublicSeaQuotaEntity::getUpdateTime)
                .oneOpt()
                .orElse(null);
        if (quota == null || quota.getAuthDay() == null || quota.getAuthDay() == 0) {
            return true;
        }

        boolean canCreate = iPublicSeaAuthService.lambdaQuery()
                .eq(PublicSeaAuthEntity::getBizId, bizId)
                .eq(PublicSeaAuthEntity::getCreator, UserThreadLocal.getUserId())
                .eq(PublicSeaAuthEntity::getExecuteStatus, ExecuteStatusEnum.PENDING.getCode())
                .exists();
        if (canCreate) {
            throw new BusinessException("您已提交过申请，请在审批通过后再打捞");
        }
        // 检查最近是否有审批通过且未超期的记录
        return hasValidApproval(bizId, quota.getAuthDay());
    }

    private boolean hasValidApproval(Integer bizId, int authDay) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime deadline = now.minusDays(authDay);

        return iPublicSeaAuthService.lambdaQuery()
                .eq(PublicSeaAuthEntity::getBizId, bizId)
                .eq(PublicSeaAuthEntity::getExecuteStatus, ExecuteStatusEnum.ALREADY_EXECUTE.getCode())
                .eq(PublicSeaAuthEntity::getCreator, UserThreadLocal.getUserId())
                .ge(PublicSeaAuthEntity::getUpdateTime, deadline)
                .exists();
    }

    @AutoTranslate
    public List<UserDeptInfoVO> getUserDeptInfos(Long id) {

        PublicSeaAuthEntity entity = iPublicSeaAuthService.lambdaQuery()
                .select(PublicSeaAuthEntity::getCreator, PublicSeaAuthEntity::getBizId)
                .eq(PublicSeaAuthEntity::getId, id)
                .oneOpt().orElseThrow(() -> new BusinessException("找不到该申请单"));

        Integer bizId = entity.getBizId();
        Integer currentUserId = entity.getCreator();

        // 1. 查询所有待审批的竞争者（去重）
        List<UserDeptInfoVO> pendingList = iPublicSeaAuthService.lambdaQuery()
                .eq(PublicSeaAuthEntity::getExecuteStatus, ExecuteStatusEnum.PENDING.getCode())
                .eq(PublicSeaAuthEntity::getBizId, bizId)
                .ne(PublicSeaAuthEntity::getCreator, currentUserId)
                .list()
                .stream()
                .map(record -> new UserDeptInfoVO()
                        .setUserId(record.getCreator())
                        .setDepartmentId(record.getDepartmentId()))
                .distinct()
                .toList();

        // 2. 查询已通过但不是自己的记录
        List<PublicSeaAuthEntity> alreadyExecuteList = iPublicSeaAuthService.lambdaQuery()
                .select(PublicSeaAuthEntity::getCreator, PublicSeaAuthEntity::getDepartmentId, PublicSeaAuthEntity::getUpdateTime)
                .eq(PublicSeaAuthEntity::getExecuteStatus, ExecuteStatusEnum.ALREADY_EXECUTE.getCode())
                .ne(PublicSeaAuthEntity::getCreator, currentUserId)
                .eq(PublicSeaAuthEntity::getBizId, bizId)
                .list();

        if (alreadyExecuteList.isEmpty()) {
            return pendingList;
        }

        // 3. 提取所有用户ID
        List<Integer> userIds = alreadyExecuteList.stream()
                .map(PublicSeaAuthEntity::getCreator)
                .distinct()
                .toList();

        // 4. 一次性查询这些用户的 authDay
        List<PublicSeaQuotaEntity> quotaList = iPublicSeaQuotaService.lambdaQuery()
                .in(PublicSeaQuotaEntity::getUserId, userIds)
                .list();

        // 5. 构建 userId -> authDay 映射
        Map<Integer, Integer> userAuthDayMap = quotaList.stream()
                .collect(Collectors.toMap(PublicSeaQuotaEntity::getUserId, quota -> quota.getAuthDay() == null ? 0 : quota.getAuthDay()));

        // 6. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 7. 筛选符合条件的用户并构建 UserDeptInfo 列表
        List<UserDeptInfoVO> executedList = alreadyExecuteList.stream()
                .filter(record -> {
                    Integer userId = record.getCreator();
                    Integer authDay = userAuthDayMap.getOrDefault(userId, 0);
                    LocalDateTime deadline = now.minusDays(authDay);
                    return record.getUpdateTime() != null && !record.getUpdateTime().isBefore(deadline);
                })
                .map(record -> new UserDeptInfoVO()
                        .setUserId(record.getCreator())
                        .setDepartmentId(record.getDepartmentId()))
                .distinct()
                .toList();

        // 8. 合并结果并去重
        List<UserDeptInfoVO> finalResult = new ArrayList<>(pendingList);
        finalResult.addAll(executedList);

        return finalResult.stream().distinct().toList();
    }

    @AutoTranslate
    public List<BusinessHisVO> getBusinessHistoryById(Long id) {
        // 1. 查询打捞记录，获取 bizId 和 createTime
        PublicSeaAuthEntity publicSeaAuth = iPublicSeaAuthService.lambdaQuery()
                .select(PublicSeaAuthEntity::getBizId, PublicSeaAuthEntity::getCreateTime)
                .eq(PublicSeaAuthEntity::getId, id)
                .oneOpt()
                .orElse(null);

        if (publicSeaAuth == null) {
            return Collections.emptyList();
        }

        // 2. 查询商机数据
        List<BusinessEntity> businessList = iBusinessService.lambdaQuery()
                .select(BusinessEntity::getOwnerId, BusinessEntity::getDepartmentId, BusinessEntity::getProgress,
                        BusinessEntity::getAssignTime, BusinessEntity::getAdvertisingReleaseDate)
                .le(BusinessEntity::getCreateTime, publicSeaAuth.getCreateTime())
                .eq(BusinessEntity::getAdvertisingSubjectId, publicSeaAuth.getBizId())
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.YES.getCode())
                .list();

        if (businessList.isEmpty()) {
            return Collections.emptyList();
        }
        List<BusinessHisVO> businessHisList = BusinessConvert.INSTANCE.toHisList(businessList);
        // 3. 手动构建 VO 并计算 followDays
        return businessHisList.stream()
                .peek(vo -> {
                    // 释放日期
                    LocalDate releaseDate = vo.getAdvertisingReleaseDate();
                    // 分配日期
                    LocalDate assignDate = LocalDate.from(vo.getAssignTime());
                    // 计算天数差
                    int followDays = (int) ChronoUnit.DAYS.between(assignDate, releaseDate);
                    vo.setFollowDays(followDays);
                })
                .toList();
    }


}