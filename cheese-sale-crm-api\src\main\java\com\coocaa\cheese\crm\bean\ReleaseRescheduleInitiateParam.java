
package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 发起延期申请入参
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
@Data
@Schema(name = "ReleaseRescheduleInitiateParam", description = "发起延期申请入参")
public class ReleaseRescheduleInitiateParam {

    @NotNull(message = "商机ID不能为空")
    @Schema(description = "商机ID")
    private Integer businessId;

    @NotBlank(message = "商机分配方式不能为空")
    @Schema(description = "商机分配方式(字典0150)")
    private String assignWay;

    @NotNull(message = "商机分配日期不能为空")
    @Schema(description = "商机分配日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate assignTime;

    @Schema(description = "主体释放日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate advertisingReleaseDate;

    @NotBlank(message = "当前商机进度不能为空")
    @Schema(description = "当前商机进度(字典0073)")
    private String progress;

    @NotNull(message = "申请延期至的日期不能为空")
    @Schema(description = "申请延期至的日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate applyDelayDate;

    @NotBlank(message = "情况说明不能为空")
    @Schema(description = "情况说明", maxLength = 100)
    private String remark;
}