package com.coocaa.cheese.crm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 折扣规则详情视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "折扣规则详情视图对象")
public class DiscountRuleDetailVO extends DiscountRuleVO {
    @Schema(description = "折扣区间设置")
    private List<DiscountCostVO> discountCosts;
} 