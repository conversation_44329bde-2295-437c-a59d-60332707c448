
package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品线变更记录表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@TableName("sale_crm_product_line_change")
public class ProductLineChangeEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商机ID
     */
    private Integer businessId;

    /**
     * 商机进度(字典0073)
     */
    private String progress;

    /**
     * 变更前产品线
     */
    private String beforeProductLine;

    /**
     * 变更后产品线
     */
    private String afterProductLine;

    /**
     * 情况说明
     */
    private String remark;

    /**
     * 执行状态(字典0163)
     */
    private String executeStatus;

    /**
     * 状态变更时间
     */
    private LocalDateTime statusChangeTime;

    /**
     * 删除标记  [0:否, 1:是]
     */
    @TableLogic
    private Integer deleteFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}