package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/7
 */
@Data
@Accessors(chain = true)
public class InnerApproveDetailDTO {

    /**
     * 签约主体id
     */
    private Integer id;

    /**
     * 所属公司ID
     */
    private Integer companyId;

    /**
     * 签约主体类型(字典0068)
     */
    private String type;

    /**
     * 是否TOP客户 [0:否, 1:是]
     */
    private Integer topFlag;

    /**
     * 备注说明
     */
    private String description;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 所属行业
     */
    private String industryCode;

    /**
     * 品牌别名
     */
    private String brandTag;

    /**
     * 持有公司ID
     */
    private Integer holdCompanyId;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;
}
