package com.coocaa.cheese.crm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 天眼查公司信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
public class TianyanchaCompanyVO {
    /**
     * 注册号
     */
    @Schema(description = "注册号")
    private String regNumber;

    /**
     * 经营状态
     */
    @Schema(description = "经营状态")
    private String regStatus;

    /**
     * 统一社会信用代码
     */
    @Schema(description = "统一社会信用代码")
    private String creditCode;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    private String estiblishTime;

    /**
     * 注册资本
     */
    @Schema(description = "注册资本")
    private String regCapital;

    /**
     * 机构类型
     */
    @Schema(description = "机构类型-1：公司；2：香港企业；3：社会组织；4：律所；5：事业单位；6：基金会；7-不存在法人、注册资本、统一社会信用代码、经营状态;8：台湾企业；9-新机构")
    private Integer companyType;

    /**
     * 公司名
     */
    @Schema(description = "公司名")
    private String name;

    /**
     * 公司id
     */
    @Schema(description = "公司id")
    private Long id;

    /**
     * 组织机构代码
     */
    @Schema(description = "组织机构代码")
    private String orgNumber;

    /**
     * 1-公司 2-人
     */
    @Schema(description = "1-公司 2-人")
    private Integer type;

    /**
     * 省份
     */
    @Schema(description = "省份")
    private String base;

    /**
     * 法人
     */
    @Schema(description = "法人")
    private String legalPersonName;

    /**
     * 匹配原因
     */
    @Schema(description = "匹配原因")
    private String matchType;
}
