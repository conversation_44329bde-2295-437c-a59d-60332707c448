package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.ad.common.core.handler.EncryptHandler;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 银行帐户
 *
 * <AUTHOR>
 * @since 2025-02-25
 */
@Data
@TableName(value = "sale_comm_bank", autoResultMap = true)
public class BankEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 开户行
     */
    private String name;

    /**
     * 银行帐号(加密存储)
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String account;

    /**
     * 所属公司ID
     */
    private Integer companyId;

    @TableField(exist = false)
    private String companyName;

    /**
     * 帐户类型(字典0078)
     */
    private String type;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
