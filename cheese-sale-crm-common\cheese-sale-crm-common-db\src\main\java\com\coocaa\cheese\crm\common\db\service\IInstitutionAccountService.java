package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;

/**
 * 机构账户 服务类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
public interface IInstitutionAccountService extends IService<InstitutionAccountEntity> {
    /**
     * 分页查询机构账户列表
     *
     * @param page      分页条件
     * @param condition 查询条件
     * @return 机构账户数据列表
     */
    IPage<InstitutionAccountEntity> pageList(IPage<InstitutionAccountEntity> page, Object condition);
} 