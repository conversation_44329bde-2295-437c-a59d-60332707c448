package com.coocaa.cheese.crm.listener.strategy;

import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaAuthEntity;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IPublicSeaAuthService;
import com.coocaa.cheese.crm.common.tools.enums.ExecuteStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveOpinionTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.listener.event.InnerApproveEvent;
import com.coocaa.cheese.crm.listener.strategy.approval.ApprovalStrategy;
import com.coocaa.cheese.crm.listener.strategy.approval.StationTargetAware;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/5/7
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PublicSeaAuthStrategy implements ApprovalStrategy, StationTargetAware {


    private final IPublicSeaAuthService publicSeaAuthService;
    private final IInnerApproveService innerApproveService;

    @Override
    public void execute(InnerApproveEvent event) {

        log.info("公海打捞审批通知信息:{}", JSONUtil.toJsonStr(event));
        Long id = event.getId();
        if (Objects.isNull(id)) {
            throw new BusinessException("公海打捞记录id为空");
        }

        PublicSeaAuthEntity publicSeaAuth = Optional.ofNullable(publicSeaAuthService.lambdaQuery()
                .eq(PublicSeaAuthEntity::getId, id)
                .one()).orElseThrow(() -> new BusinessException("公海打捞记录不存在!"));
        log.info("打捞申请审批通知公海打捞信息查询:{}", JSONUtil.toJsonStr(publicSeaAuth));
        if (InnerApproveOpinionTypeEnum.AGREE.equals(event.getOpinionTypeEnum())
                && (StringUtils.isNotBlank(event.getInnerTaskOperateVO().getApprovalStatus())
                && InnerApproveStatusEnum.ALREADY_FINISH.getCode().equals(event.getInnerTaskOperateVO().getApprovalStatus()))) {
            // 审批通过
            agree(publicSeaAuth);
        } else if (InnerApproveOpinionTypeEnum.REJECT.equals(event.getOpinionTypeEnum())) {
            // 审批不通过
            reject(publicSeaAuth);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void agree(PublicSeaAuthEntity publicSeaAuth) {

        log.info("打捞申请审批通知:{}", JSONUtil.toJsonStr(publicSeaAuth));

        //更新公海打捞执行状态
        boolean updated = publicSeaAuthService.lambdaUpdate()
                .eq(PublicSeaAuthEntity::getId, publicSeaAuth.getId())
                .set(PublicSeaAuthEntity::getExecuteStatus, ExecuteStatusEnum.ALREADY_EXECUTE.getCode())
                .update();
        if (!updated) {
            throw new BusinessException("打捞申请审批通知更新公海打捞记录失败!");

        }
        //修改站内审批业务关联表
        updateInnerApprove(publicSeaAuth.getId(), InnerApproveOpinionTypeEnum.AGREE);
    }

    public void reject(PublicSeaAuthEntity publicSeaAuth) {

        publicSeaAuthService.lambdaUpdate()
                .eq(PublicSeaAuthEntity::getId, publicSeaAuth.getId())
                .set(PublicSeaAuthEntity::getExecuteStatus, ExecuteStatusEnum.ALREADY_CANCEL.getCode())
                .update();

        //修改站内审批业务关联表
        updateInnerApprove(publicSeaAuth.getId(), InnerApproveOpinionTypeEnum.REJECT);
    }

    private void updateInnerApprove(Long id, InnerApproveOpinionTypeEnum innerApproveOpinionTypeEnum) {

        innerApproveService.lambdaUpdate()
                .set(InnerApproveEntity::getResult, innerApproveOpinionTypeEnum.getCode())
                .set(InnerApproveEntity::getCloseTime, LocalDateTime.now())
                .eq(InnerApproveEntity::getBizId, id)
                .update();
    }

    @Override
    public StationTargetEnum getTargetEnum() {
        return StationTargetEnum.PUBLIC_SEA_SALVAGE;
    }
}
