package com.coocaa.cheese.crm.controller;


import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.DashboardCustomerReportQueryParam;
import com.coocaa.cheese.crm.common.tools.constant.BusinessConstants;
import com.coocaa.cheese.crm.service.DashboardService;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportCardVO;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportDepartmentDetailVO;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportDetailVO;
import com.coocaa.cheese.crm.vo.DashboardCustomerReportSearchVO;
import com.coocaa.cheese.crm.vo.DashboardWeekVO;
import com.coocaa.cheese.permission.core.annotation.DataPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * 数据看板管理控制
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-08
 */
@Slf4j
@RestController
@RequestMapping("/dashboard")
@Tag(name = "数据看板管理", description = "数据看板管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DashboardController {

    private final DashboardService dashboardService;

    /**
     * 客户报备顶部数据
     */
    @GetMapping("/customer-report-top")
    @Operation(summary = "客户报备顶部数据")
    public ResultTemplate<List<DashboardWeekVO>> top(HttpServletRequest request) {
        // 校验请求头
        String code = checkHeader(request);
        return ResultTemplate.success(dashboardService.top(code));
    }

    /**
     * 客户报备底部统计卡片
     */
    @GetMapping("/customer-report-card")
    @Operation(summary = "客户报备底部统计卡片")
    public ResultTemplate<List<DashboardCustomerReportCardVO>> card(HttpServletRequest request,
                                                                    @RequestParam(name = "startTime") LocalDate startTime,
                                                                    @RequestParam(name = "endTime") LocalDate endTime) {
        // 校验请求头
        String code = checkHeader(request);
        return ResultTemplate.success(dashboardService.card(startTime, endTime, code));
    }

    /**
     * 客户报备部门详情
     */
    @GetMapping("/customer-report-department-detail")
    @Operation(summary = "客户报备部门详情")
    public ResultTemplate<DashboardCustomerReportDepartmentDetailVO> departmentDetail(
            HttpServletRequest request,
            @RequestParam(name = "departmentId", required = false) String departmentId,
            @RequestParam(name = "startTime") LocalDate startTime,
            @RequestParam(name = "endTime") LocalDate endTime) {
        // 校验请求头
        String code = checkHeader(request);
        return ResultTemplate.success(dashboardService.departmentDetail(startTime, endTime, code, departmentId));
    }

    /**
     * 客户报备分页查询
     */
    @PostMapping("/customer-report-page")
    @Operation(summary = "客户报备分页查询")
    public ResultTemplate<PageResponseVO<DashboardCustomerReportDetailVO>> page(
            @RequestBody PageRequestVO<DashboardCustomerReportQueryParam> pageRequest,
            HttpServletRequest request) {
        // 校验请求头
        String code = checkHeader(request);
        return ResultTemplate.success(dashboardService.page(code, pageRequest));
    }

    /**
     * 客户报备搜索
     */
    @PostMapping("/customer-report-search")
    @Operation(summary = "客户报备搜索")
    @DataPermission(tableName = BusinessConstants.ADVERTISING_SUBJECT_TABLE_NAME,
            userColumn = "creator")
    public ResultTemplate<PageResponseVO<DashboardCustomerReportSearchVO>> search(@RequestBody PageRequestVO<String> param) {
        return ResultTemplate.success(dashboardService.search(param));
    }

    /**
     * 刷新主体表创建人名称(上线后下个版本可以直接删了)
     */
    @GetMapping("/customer-report-refresh")
    @Operation(summary = "刷新主体表创建人名称")
    public ResultTemplate<Void> refresh() {
        dashboardService.refresh();
        return ResultTemplate.success();
    }

    /**
     * 校验请求头
     *
     * @param request 请求对象
     * @return 资源编码
     */
    private String checkHeader(HttpServletRequest request) {
        String code = request.getHeader("Code");
        if (StringUtils.isBlank(code)) {
            throw new BusinessException("缺少资源参数");
        }
        return code;
    }
}
