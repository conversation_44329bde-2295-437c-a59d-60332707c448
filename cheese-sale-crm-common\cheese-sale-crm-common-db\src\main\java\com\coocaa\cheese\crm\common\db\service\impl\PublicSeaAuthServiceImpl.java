package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.bean.PublicSeaAuthDTO;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaAuthEntity;
import com.coocaa.cheese.crm.common.db.mapper.PublicSeaAuthMapper;
import com.coocaa.cheese.crm.common.db.service.IPublicSeaAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 公海打捞授权表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PublicSeaAuthServiceImpl extends ServiceImpl<PublicSeaAuthMapper, PublicSeaAuthEntity> implements IPublicSeaAuthService {

    @Override
    public IPage<PublicSeaAuthEntity> pageList(IPage<PublicSeaAuthEntity> page, PublicSeaAuthDTO publicSeaAuth) {
        return baseMapper.pageList(page, publicSeaAuth);
    }
}