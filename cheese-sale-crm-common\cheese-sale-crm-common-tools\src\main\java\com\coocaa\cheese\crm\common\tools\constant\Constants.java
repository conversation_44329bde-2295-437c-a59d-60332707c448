package com.coocaa.cheese.crm.common.tools.constant;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 系统公共常量
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02
 */
public interface Constants {

    // ===================== 数字常量 =====================
    Integer NUMBER_SIX = 6;
    int FOREVER_DAYS = 9999;
    /**
     * 默认分页大小
     */
    Integer DEFAULT_PAGE_SIZE = 20;

    // ===================== 符号常量 =====================
    String COMMA = ",";
    String SLASH = "/";


    // ===================== 日期格式常量 =====================
    String DATE_FORMAT = "yyyy-MM-dd";
    String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    String DATE_TIME_FORMAT_DOT = "yyyy.MM.dd HH:mm:ss";
    /**
     * 永久日期
     */
    LocalDate FOREVER_DATE = LocalDate.of(2099, 12, 31);

    /**
     * 默认时间
     */
    LocalDateTime CONSTANT_DEFAULT_TIME = LocalDateTime.of(2025, 1, 1, 0, 0, 0);

    // ===================== 金额常量 =====================
    BigDecimal ZERO_POINT_ONE = new BigDecimal("0.1");
    BigDecimal ZERO_POINT_ZERO_ONE = new BigDecimal("0.01");
    BigDecimal BIG_DECIMAL_SEVEN = new BigDecimal("7");
    BigDecimal BIG_DECIMAL_ONE_HUNDRED = new BigDecimal("100");
}
