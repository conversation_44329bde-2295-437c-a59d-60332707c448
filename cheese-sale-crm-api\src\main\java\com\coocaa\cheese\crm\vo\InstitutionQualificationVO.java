package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 机构资质视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构资质视图对象")
public class InstitutionQualificationVO {
    @Schema(description = "资质ID")
    private Integer id;

    @Schema(description = "机构账户ID")
    private Integer institutionId;

    @Schema(description = "资质名称")
    private String qualificationName;

    @Schema(description = "资质文件URL")
    private String fileUrl;

    @Schema(description = "备注说明")
    private String description;

    @Schema(description = "上传人ID")
    @TransField(type = TransTypes.USER)
    private Integer operator;
    private String operatorName;

    @Schema(description = "上传时间")
    private LocalDateTime createTime;

} 