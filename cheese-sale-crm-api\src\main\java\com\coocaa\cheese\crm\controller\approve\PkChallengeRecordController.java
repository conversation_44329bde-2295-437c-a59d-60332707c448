package com.coocaa.cheese.crm.controller.approve;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.audit.service.AbstractApproveService;
import com.coocaa.cheese.crm.audit.template.BaseApproveTemplate;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.bean.PkChallengeRecordParam;
import com.coocaa.cheese.crm.service.PkChallengeRecordService;
import com.coocaa.cheese.crm.vo.InnerApprovePkChallengeDetailVO;
import com.coocaa.cheese.crm.vo.InnerApprovePkChallengePageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * PK挑战记录审批控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */

@Getter
@RestController
@RequestMapping("/pk-challenge")
@Tag(name = "PK挑战", description = "PK挑战申请管理")
public class PkChallengeRecordController extends BaseApproveTemplate<InnerApprovePkChallengeDetailVO,
        PkChallengeRecordParam, InnerApprovePkChallengePageVO, InnerApproveTaskQueryParam> {
    private final PkChallengeRecordService pkChallengeRecordService;

    protected PkChallengeRecordController(
            @Qualifier("pkChallengeRecordService") AbstractApproveService<InnerApprovePkChallengeDetailVO, PkChallengeRecordParam,
                    InnerApprovePkChallengePageVO, InnerApproveTaskQueryParam> abstractApproveService) {
        super(abstractApproveService);
        this.pkChallengeRecordService = (PkChallengeRecordService) abstractApproveService;
    }

    /**
     * 是否可以直接发起PK挑战申请
     */
    @Operation(summary = "是否可以直接发起PK挑战申请")
    @GetMapping("/{bizId}/can-pk-create")
    public ResultTemplate<Boolean> canPkChallengeCreate(@PathVariable(name = "bizId") Integer bizId) {
        return ResultTemplate.success(pkChallengeRecordService.canPkChallengeCreate(bizId));
    }

    /**
     * 申请详情查询
     */
    @Operation(summary = "申请详情查询")
    @Parameter(name = "bizId", description = "bizId", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{bizId}/approve")
    public ResultTemplate<InnerApprovePkChallengeDetailVO> approveDetail(@PathVariable("bizId") Long bizId) {
        return ResultTemplate.success(pkChallengeRecordService.approveDetail(bizId));
    }
}
