package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.cheese.crm.bean.PublicSeaQuotaParam;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.PublicSeaQuotaEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IPublicSeaQuotaService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.convert.PublicSeaQuotaConvert;
import com.coocaa.cheese.crm.vo.PublicSeaQuotaVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;


/**
 * 公海配额服务接口
 *
 * <AUTHOR>
 * @since 2025-04-30
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PublicSeaQuotaService {

    private final IPublicSeaQuotaService publicSeaQuotaService;
    private final IAdvertisingSubjectService advertisingSubjectService;
    private final RedissonClient redisson;

    /**
     * 根据ID获取公海配额视图对象
     *
     * @param id 公共海域配额的ID
     * @return 如果ID对应的公共海域配额存在，则返回转换后的PublicSeaQuotaVO对象；否则返回null
     */
    public PublicSeaQuotaVO getById(Integer id) {
        return Optional.ofNullable(publicSeaQuotaService.getById(id)).map(PublicSeaQuotaConvert.INSTANCE::toVo).orElse(null);
    }

    /**
     * 删除公海配额
     *
     * @param id 公海配额的ID
     * @return 如果删除成功，则返回true；否则返回false
     */
    public Boolean deleteById(Integer id) {
        return publicSeaQuotaService.lambdaUpdate()
                .set(PublicSeaQuotaEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(PublicSeaQuotaEntity::getId, id)
                .update();
    }

    /**
     * 创建或更新公共海域配额
     *
     * @param id    公共海域配额的ID，如果为null则创建新记录；否则更新现有记录
     * @param param 公共海域配额的参数对象
     * @return 如果创建或更新成功，则返回true；
     */
    public Boolean createOrUpdate(Integer id, PublicSeaQuotaParam param) {
        PublicSeaQuotaEntity entity = PublicSeaQuotaConvert.INSTANCE.toEntity(param);
        // 校验
        check(id, param);
        if (Objects.isNull(id)) {
            publicSeaQuotaService.save(entity);
        } else {
            entity.setId(id);
            publicSeaQuotaService.updateById(entity);
        }
        return true;
    }

    /**
     * 校验参数
     * @param id 公海配额的ID
     * @param param 公海配额的参数对象
     */
    private void check(Integer id, PublicSeaQuotaParam param) {
        // 校验每月配额是否小于每日配额
        if (param.getMonthlyQuota() < param.getDailyQuota()) {
            throw new BusinessException("每月配额不能小于每日配额");
        }

        // 检查是否已存在
        LambdaQueryWrapper<PublicSeaQuotaEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PublicSeaQuotaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .ne(Objects.nonNull(id), PublicSeaQuotaEntity::getId, id)
                .eq(PublicSeaQuotaEntity::getUserId, param.getUserId());
        if (publicSeaQuotaService.exists(queryWrapper)) {
            throw new BusinessException("已存在该用户的公共海域配额");
        }
    }

    /**
     * 分页查询公共海域配额
     *
     * @param pageRequest 分页请求对象，包含查询参数和分页信息
     * @return 分页响应对象
     */
    public PageResponseVO<PublicSeaQuotaVO> pageList(PageRequestVO<String> pageRequest) {
        LambdaQueryWrapper<PublicSeaQuotaEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PublicSeaQuotaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(PublicSeaQuotaEntity::getCreateTime)
                .like(StringUtils.isNotBlank(pageRequest.getQuery()), PublicSeaQuotaEntity::getUserName, pageRequest.getQuery());
        return PublicSeaQuotaConvert.INSTANCE.toPageResponse(publicSeaQuotaService.page(getPage(pageRequest), queryWrapper));

    }

    /**
     * 获取分页对象
     */
    private Page<PublicSeaQuotaEntity> getPage(PageRequestVO<?> pageRequest) {
        // 分页查询列表，自定义统计SQL
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));
    }

    /**
     * 检查并扣除公海配额
     *
     * @param userId               用户ID
     * @param advertisingSubjectId 签约主体ID
     */
    public void checkAndDeductPublicSeaQuota(Integer userId, Integer advertisingSubjectId) {
        log.info("检查并扣除公海配额，用户ID：{}，签约主体ID：{}", userId, advertisingSubjectId);
        // 检查签约主体是否满足【申请人】=当前用户 & 保护期标志为YES
        boolean isRecentlyApplied = advertisingSubjectService.lambdaQuery()
                .eq(AdvertisingSubjectEntity::getCreator, userId)
                .eq(AdvertisingSubjectEntity::getId, advertisingSubjectId)
                .eq(AdvertisingSubjectEntity::getProtectionPeriodFlag, BooleFlagEnum.YES.getCode())
                .exists();

        // 如果不是最近申请的，则需要检查并扣除公海配额
        if (!isRecentlyApplied) {
            // 获取用户的公海配额信息
            PublicSeaQuotaEntity quota = publicSeaQuotaService.lambdaQuery()
                    .eq(PublicSeaQuotaEntity::getUserId, userId)
                    .eq(PublicSeaQuotaEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .last("limit 1")
                    .orderByDesc(PublicSeaQuotaEntity::getUpdateTime)
                    .one();

            if (quota == null) {
                throw new BusinessException("未找到用户公海配额信息");
            }
            //获取分布式锁
            RLock lock = redisson.getLock("checkAndDeductPublicSeaQuota:" + userId + ":" + advertisingSubjectId);
            //判断未获取到锁的情况
            if (lock == null || !lock.tryLock()) {
                //当前用户正在扣除进行额度扣除，请稍后再试。
                throw new BusinessException("当前用户正在扣除进行额度扣除，请稍后再试。");
            }
            try {
                // 检查月度配额
                if (quota.getMonthlyQuota() - quota.getMonthlyUsed() > 0) {
                    // 检查日度配额
                    if (quota.getDailyQuota() - quota.getDailyUsed() > 0) {
                        // 更新月度和日度已用配额
                        PublicSeaQuotaEntity updateQuota = new PublicSeaQuotaEntity();
                        updateQuota.setId(quota.getId());
                        updateQuota.setMonthlyUsed(quota.getMonthlyUsed() + 1);
                        updateQuota.setDailyUsed(quota.getDailyUsed() + 1);
                        publicSeaQuotaService.updateById(updateQuota);
                    } else if (quota.getTemporaryQuota() - quota.getTempUsed() > 0) {
                        // 使用临时配额
                        PublicSeaQuotaEntity updateQuota = new PublicSeaQuotaEntity();
                        updateQuota.setId(quota.getId());
                        updateQuota.setTempUsed(quota.getTempUsed() + 1);
                        publicSeaQuotaService.updateById(updateQuota);
                    } else {
                        throw new BusinessException("该签约主体为公海客户，您的本日配额已不足，请次日再试或申请临时配额。");
                    }
                } else if (quota.getTemporaryQuota() - quota.getTempUsed() > 0) {
                    // 使用临时配额
                    PublicSeaQuotaEntity updateQuota = new PublicSeaQuotaEntity();
                    updateQuota.setId(quota.getId());
                    updateQuota.setTempUsed(quota.getTempUsed() + 1);
                    publicSeaQuotaService.updateById(updateQuota);
                } else {
                    throw new BusinessException("该签约主体为公海客户，您的本月配额已不足，请次月再试或申请临时配额。");
                }
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    //释放锁
                    lock.unlock();
                }
            }
        }
    }
}
