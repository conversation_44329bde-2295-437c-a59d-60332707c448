package com.coocaa.cheese.crm.handler.message;

import com.coocaa.ad.common.core.redission.model.DelayedMessage;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.tools.enums.DelayQueueEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 任务类消息处理器
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class TaskMessageHandler implements MessageHandler {

    private final IAdvertisingSubjectService advertisingSubjectService;

    @Override
    public DelayQueueEnum getBizType() {
        return DelayQueueEnum.ADVERTISING_SUBJECT_QUEUE;
    }

    @Override
    public void handle(DelayedMessage message) {
        log.info("处理签约主体保护期消息: {}", message.getContent());
        try {
            // 从消息内容中获取签约主体ID
            Integer subjectId = Integer.parseInt(message.getContent());

            // 更新签约主体的保护期标记为有效(否)
            // 批量更新保护期标记
            advertisingSubjectService.lambdaUpdate()
                    .set(AdvertisingSubjectEntity::getProtectionPeriodFlag, BooleFlagEnum.NO.getCode())
                    .eq(AdvertisingSubjectEntity::getId, subjectId).update();
            log.info("签约主体 ID {} 保护期标记更新", subjectId);
        } catch (Exception e) {
            log.error("处理签约主体保护期消息失败", e);
        }
    }
} 