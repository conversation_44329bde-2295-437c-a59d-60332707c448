package com.coocaa.cheese.crm.task;

import com.coocaa.cheese.crm.common.db.entity.DiscountRuleEntity;
import com.coocaa.cheese.crm.common.db.service.IDiscountRuleService;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.tools.enums.DiscountRuleStatusEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 折扣策略生效定时任务
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DiscountRuleEffectiveTask {

    private final IDiscountRuleService discountRuleService;

    @XxlJob("discountRuleEffectiveJob")
    public void execute() {
        log.info("开始执行折扣策略生效定时任务");
        try {
            // 更新已到生效时间的策略状态
            boolean success = discountRuleService.lambdaUpdate()
                    .set(DiscountRuleEntity::getStatus, DiscountRuleStatusEnum.EFFECTIVE.getCode())
                    .eq(DiscountRuleEntity::getStatus, DiscountRuleStatusEnum.NOT_EFFECTIVE.getCode())
                    .eq(DiscountRuleEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .le(DiscountRuleEntity::getEffectiveTime, LocalDateTime.now())
                    .update();
            
            log.info("折扣策略生效定时任务执行完成, 更新结果: {}", success);
        } catch (Exception e) {
            log.error("折扣策略生效定时任务执行异常", e);
        }
    }
} 