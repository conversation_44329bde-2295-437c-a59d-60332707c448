package com.coocaa.cheese.crm.listener.strategy.approval;

import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 审批策略工厂类
 * <AUTHOR>
 * @since 2025/5/7
 */
@Component
public class ApprovalStrategyFactory {
    private final Map<StationTargetEnum, ApprovalStrategy> strategyMap;

    /**
     * 构造方法，初始化策略映射表
     *
     * @param strategies 所有审批策略实现列表
     */
    @Autowired
    public ApprovalStrategyFactory(List<ApprovalStrategy> strategies) {
        this.strategyMap = new EnumMap<>(StationTargetEnum.class);
        strategies.forEach(strategy -> {
            if (strategy instanceof StationTargetAware) {
                StationTargetEnum targetEnum = ((StationTargetAware) strategy).getTargetEnum();
                if (strategyMap.containsKey(targetEnum)) {
                    throw new IllegalStateException("发现重复的策略实现 for: " + targetEnum);
                }
                strategyMap.put(targetEnum, strategy);
            }
        });
    }

    /**
     * 根据枚举获取对应的审批策略实现
     *
     * @param targetEnum 枚举
     * @return 审批策略实现
     */
    public ApprovalStrategy getStrategy(StationTargetEnum targetEnum) {
        return Optional.ofNullable(strategyMap.get(targetEnum))
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("未找到对应的审批策略: %s(%s)", targetEnum.name(), targetEnum.getCode())));
    }
}

