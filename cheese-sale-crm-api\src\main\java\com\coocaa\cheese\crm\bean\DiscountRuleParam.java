package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 折扣规则参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "折扣规则参数")
public class DiscountRuleParam {
    @Schema(description = "折扣规则名称", type = "String", example = "春节促销折扣")
    @NotBlank(message = "折扣策略名称不能为空")
    private String name;

    @Schema(description = "是否立即生效 [0:否, 1:是]", example = "0")
    private Integer immediateEffectiveFlag;

    @Schema(description = "生效时间", example = "2025-01-01 00:00:00")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime effectiveTime;

    @Schema(description = "适用城市ID", type = "String", example = "110100,440100")
    @NotBlank(message = "适用城市不能为空")
    private String cityIds;

    @Schema(description = "备注", type = "String", example = "春节期间特别折扣")
    private String description;

    @Schema(description = "折扣区间设置", type = "List")
    @NotEmpty(message = "折扣区间设置不能为空")
    @Valid
    private List<DiscountCostParam> discountCosts;
} 