package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.InstitutionAccountListParam;
import com.coocaa.cheese.crm.bean.InstitutionAccountParam;
import com.coocaa.cheese.crm.bean.InstitutionAccountQueryParam;
import com.coocaa.cheese.crm.common.db.entity.CompanyEntity;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;
import com.coocaa.cheese.crm.common.db.entity.InstitutionFundAccountEntity;
import com.coocaa.cheese.crm.common.db.service.ICompanyService;
import com.coocaa.cheese.crm.common.db.service.IInstitutionAccountService;
import com.coocaa.cheese.crm.common.db.service.IInstitutionFundAccountService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.AccountStatusEnum;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.tools.enums.EvidenceBillingTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.PricingMethodEnum;
import com.coocaa.cheese.crm.convert.InstitutionAccountConvert;
import com.coocaa.cheese.crm.util.CodeGenerator;
import com.coocaa.cheese.crm.vo.InstitutionAccountDetailVO;
import com.coocaa.cheese.crm.vo.InstitutionAccountSimpleVO;
import com.coocaa.cheese.crm.vo.InstitutionAccountVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 机构账户服务
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionAccountService {
    private final IInstitutionAccountService institutionAccountService;
    private final IInstitutionFundAccountService institutionFundAccountService;
    private final ICompanyService companyService;
    private final CodeGenerator codeGenerator;

    /**
     * 分页查询机构账户列表
     *
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    @AutoTranslate
    public PageResponseVO<InstitutionAccountVO> pageListInstitutionAccount(PageRequestVO<InstitutionAccountQueryParam> pageRequest) {
        // 构建分页对象
        Page<InstitutionAccountEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));

        // 获取查询参数
        InstitutionAccountQueryParam queryParam = pageRequest.getQuery();

        // 查询数据
        IPage<InstitutionAccountEntity> pageResult = institutionAccountService.lambdaQuery()
                .eq(Objects.nonNull(queryParam.getCompanyId()),
                        InstitutionAccountEntity::getCompanyId, queryParam.getCompanyId())
                .eq(StringUtils.isNotBlank(queryParam.getAccountStatus()),
                        InstitutionAccountEntity::getAccountStatus, queryParam.getAccountStatus())
                .eq(StringUtils.isNotBlank(queryParam.getOwnerDepartmentId()),
                        InstitutionAccountEntity::getOwnerDepartmentId, queryParam.getOwnerDepartmentId())
                .eq(Objects.nonNull(queryParam.getOwnerId()),
                        InstitutionAccountEntity::getOwnerId, queryParam.getOwnerId())
                .eq(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(InstitutionAccountEntity::getUpdateTime)
                .page(page);

        // 使用Convert转换为VO
        return InstitutionAccountConvert.INSTANCE.toPageResponse(pageResult);
    }

    /**
     * 获取机构账户详情
     *
     * @param id 机构账户ID
     * @return 机构账户详情
     */
    @AutoTranslate
    public InstitutionAccountDetailVO getInstitutionAccountDetail(Integer id) {
        // 查询机构账户
        InstitutionAccountEntity entity = institutionAccountService.getById(id);
        if (entity == null) {
            throw new BusinessException("机构账户不存在");
        }
        // 使用Convert转换为DetailVO
        InstitutionAccountDetailVO detailVO = InstitutionAccountConvert.INSTANCE.toDetailVo(entity);
        // 查询公司信息获取统一社会信用代码
        if (entity.getCompanyId() != null) {
            CompanyEntity company = companyService.lambdaQuery()
                    .select(CompanyEntity::getCode, CompanyEntity::getLicenseUrl)
                    .eq(CompanyEntity::getId, entity.getCompanyId())
                    .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .last("limit 1")
                    .one();
            if (company != null) {
                detailVO.setCreditCode(company.getCode());
                detailVO.setLicenseUrl(company.getLicenseUrl());
            }
        }
        // 特殊处理，解决前端回显问题
        if (detailVO.getParentId() == 0) {
            detailVO.setParentId(null);
        }
        return detailVO;
    }

    /**
     * 创建机构账户
     *
     * @param param 机构账户参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createInstitutionAccount(InstitutionAccountParam param) {
        // 校验企业是否已开户
        boolean exists = institutionAccountService.lambdaQuery()
                .eq(InstitutionAccountEntity::getCompanyId, param.getCompanyId())
                .eq(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .exists();
        if (exists) {
            throw new BusinessException("该企业已开户，不能重复开户");
        }

        // 生成账户编码
        String accountCode = generateAccountCode(3);

        // 使用Convert转换为实体
        InstitutionAccountEntity entity = InstitutionAccountConvert.INSTANCE.toEntity(param);
        entity.setAccountCode(accountCode);
        entity.setAccountStatus(AccountStatusEnum.NORMAL.getCode());
        entity.setOwnerId(param.getOwnerId());
        entity.setOwnerDepartmentId(param.getOwnerDepartmentId());
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());

        // 设置授权过期状态
        if (param.getAuthEndDate() != null) {
            LocalDate today = LocalDate.now();
            if (param.getAuthEndDate().isBefore(today)) {
                entity.setAccountStatus(AccountStatusEnum.DISABLED.getCode());
            } else {
                entity.setAccountStatus(AccountStatusEnum.NORMAL.getCode());
            }
        } else {
            entity.setAccountStatus(AccountStatusEnum.NORMAL.getCode());
        }

        // 保存机构账户
        boolean success = institutionAccountService.save(entity);

        // 创建资金账户
        if (success) {
            // 使用辅助方法创建资金账户
            InstitutionFundAccountEntity fundAccount = createNewFundAccount(entity.getId());
            institutionFundAccountService.save(fundAccount);
        }

        return success;
    }

    /**
     * 创建新的资金账户，并设置默认值
     *
     * @param institutionId 机构账户ID
     * @return 新的资金账户实体
     */
    private InstitutionFundAccountEntity createNewFundAccount(Integer institutionId) {
        InstitutionFundAccountEntity entity = new InstitutionFundAccountEntity();
        entity.setInstitutionId(institutionId);
        entity.setBalance(BigDecimal.ZERO);
        entity.setTotalFrozen(BigDecimal.ZERO);
        entity.setTotalCashRecharge(BigDecimal.ZERO);
        entity.setTotalNonCashRecharge(BigDecimal.ZERO);
        entity.setTotalCompensation(BigDecimal.ZERO);
        entity.setTotalConsumption(BigDecimal.ZERO);
        entity.setTotalRefund(BigDecimal.ZERO);
        entity.setTotalCancel(BigDecimal.ZERO);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());
        return entity;
    }

    /**
     * 检查父企业是否已有父级关系
     *
     * @param parentId 父企业ID
     * @return true if parent exists and has parent, false otherwise
     */
    private boolean checkParentHasParent(Integer parentId) {
        if (parentId == null || parentId == 0) {
            return false;
        }

        // 查询父企业对应的机构账户
        InstitutionAccountEntity parentInstitution = institutionAccountService.lambdaQuery()
                .eq(InstitutionAccountEntity::getCompanyId, parentId)
                .eq(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .last("limit 1")
                .one();

        // 如果父企业有对应的机构账户，且机构账户有父ID，则不允许设置
        return parentInstitution != null && parentInstitution.getParentId() != null && parentInstitution.getParentId() != 0;
    }

    /**
     * 更新机构账户
     *
     * @param id    机构账户ID
     * @param param 机构账户参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateInstitutionAccount(Integer id, InstitutionAccountParam param) {
        // 查询机构账户
        InstitutionAccountEntity entity = institutionAccountService.getById(id);
        if (entity == null) {
            throw new BusinessException("机构账户不存在");
        }

        // 检查companyId是否存在
        if (param.getCompanyId() != null && !companyService.lambdaQuery()
                .eq(CompanyEntity::getId, param.getCompanyId())
                .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .exists()) {
            throw new BusinessException("关联的企业不存在");
        }


        // 不允许自己设置自己
        if (param.getParentId() != null && param.getParentId() != 0) {
            if (Objects.equals(param.getCompanyId(), param.getParentId())) {
                throw new BusinessException("不允许设置自己作为上级机构");
            }
            // 检查父企业是否已有父级关系
            if (checkParentHasParent(param.getParentId())) {
                throw new BusinessException("父企业已有上级企业，不允许设置");
            }
        }

        // 使用Convert转换为实体并更新字段
        InstitutionAccountEntity updateEntity = InstitutionAccountConvert.INSTANCE.toEntity(param);
        updateEntity.setId(id);

        // 特殊处理取消商机机构
        if (param.getParentId() == null) {
            updateEntity.setParentId(0);
        }

        // 处理账户状态变更
        if (AccountStatusEnum.DISABLED.getCode().equals(param.getAccountStatus())) {
            if (param.getDisableReason() == null) {
                throw new BusinessException("禁用原因不能为空");
            }
            updateEntity.setDisableReason(param.getDisableReason());
        } else if (AccountStatusEnum.NORMAL.getCode().equals(param.getAccountStatus())) {
            updateEntity.setDisableReason("");
        }

        return institutionAccountService.updateById(updateEntity);
    }

    /**
     * 生成账户编码
     */
    private String generateAccountCode(int i) {
        if (i == 0) {
            throw new BusinessException("编码生成失败，请重试！");
        }
        // 生成编码
        String code = codeGenerator.generateInstitutionAccountCode();

        // 检查编码是否存在
        boolean exists = institutionAccountService.lambdaQuery()
                .eq(InstitutionAccountEntity::getAccountCode, code)
                .eq(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .exists();

        // 如果存在则重新生成
        if (exists) {
            return generateAccountCode(i - 1);
        }
        return code;
    }

    /**
     * 获取机构账户列表
     *
     * @param param 是否停用（true-停用，false-启用，null-全部）
     * @return 机构账户列表
     */
    public List<InstitutionAccountSimpleVO> listInstitutionAccounts(InstitutionAccountListParam param) {
        Boolean disabled = param.getDisabled();
        List<Integer> accountIds = param.getAccountIds();
        String companyName = param.getCompanyName();
        // 构建查询条件
        LambdaQueryWrapper<InstitutionAccountEntity> queryWrapper = new LambdaQueryWrapper<InstitutionAccountEntity>()
                .eq(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());

        // 根据停用状态查询
        if (disabled != null) {
            queryWrapper.eq(InstitutionAccountEntity::getAccountStatus,
                    disabled ? AccountStatusEnum.DISABLED.getCode() : AccountStatusEnum.NORMAL.getCode());
        }

        // 根据ID列表查询
        if (CollectionUtil.isNotEmpty(accountIds)) {
            queryWrapper.in(InstitutionAccountEntity::getId, accountIds);
        }

        // 根据公司名称查询
        if (StringUtils.isNotBlank(companyName)) {
            List<CompanyEntity> companies = companyService.lambdaQuery()
                    .select(CompanyEntity::getId)
                    .like(CompanyEntity::getName, companyName)
                    .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .list();

            List<Integer> companyIds = companies.stream()
                    .map(CompanyEntity::getId)
                    .toList();
            if (CollectionUtil.isEmpty(companyIds)) {
                //保证查不出来
                companyIds = Collections.singletonList(0);
            }
            queryWrapper.in(InstitutionAccountEntity::getCompanyId, companyIds);
        }

        // 查询机构账户
        List<InstitutionAccountEntity> accountList = institutionAccountService.list(queryWrapper);

        if (CollectionUtil.isEmpty(accountList)) {
            return new ArrayList<>();
        }

        // 获取所有公司ID
        List<Integer> companyIds = accountList.stream()
                .map(InstitutionAccountEntity::getCompanyId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 查询公司信息
        Map<Integer, String> companyNameMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(companyIds)) {
            List<CompanyEntity> companies = companyService.lambdaQuery()
                    .select(CompanyEntity::getId, CompanyEntity::getName)
                    .in(CompanyEntity::getId, companyIds)
                    .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .list();

            companyNameMap = companies.stream()
                    .collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getName, (k1, k2) -> k1));
        }

        // 转换为VO
        Map<Integer, String> finalCompanyNameMap = companyNameMap;
        return accountList.stream()
                .map(entity -> {
                    InstitutionAccountSimpleVO vo = new InstitutionAccountSimpleVO();
                    vo.setId(entity.getId());
                    vo.setCompanyId(entity.getCompanyId());
                    vo.setCompanyName(finalCompanyNameMap.getOrDefault(entity.getCompanyId(), ""));
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取机构账户的计费方式和公司名称
     *
     * @param institutionId 机构账户ID
     * @return 计费方式和公司名称信息
     */
    public Map<String, Object> getInstitutionBillingTypesAndCompanyName(Integer institutionId) {
        // 查询机构账户
        InstitutionAccountEntity entity = institutionAccountService.getById(institutionId);
        if (entity == null) {
            throw new BusinessException("机构账户不存在");
        }

        Map<String, Object> result = new HashMap<>();

        // 获取公司名称
        if (entity.getCompanyId() != null) {
            CompanyEntity company = companyService.lambdaQuery()
                    .select(CompanyEntity::getName)
                    .eq(CompanyEntity::getId, entity.getCompanyId())
                    .eq(CompanyEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .last("limit 1")
                    .one();
            if (company != null) {
                result.put("companyName", company.getName());
            }
        }

        // 获取计费方式
        List<Map<String, String>> billingTypes = new ArrayList<>();

        // 获取机构当前的计价方式
        String currentPricingMethod = entity.getPricingMethod();
        PricingMethodEnum pricingMethod = PricingMethodEnum.parse(currentPricingMethod);

        if (pricingMethod == null) {
            // 如果无法解析计价方式，返回空列表
            result.put("billingTypes", billingTypes);
            return result;
        }

        // 使用增强型switch表达式处理不同的计价方式
        switch (pricingMethod) {
            case SELF_SELECT -> {
                // 自助选择：返回固定按天和固定按周两个选项
                addBillingType(billingTypes, EvidenceBillingTypeEnum.DAY);
                addBillingType(billingTypes, EvidenceBillingTypeEnum.WEEK);
            }
            case FIXED_DAILY -> addBillingType(billingTypes, EvidenceBillingTypeEnum.DAY);
            case FIXED_WEEKLY -> addBillingType(billingTypes, EvidenceBillingTypeEnum.WEEK);
        }

        result.put("billingTypes", billingTypes);

        return result;
    }

    /**
     * 添加计费方式到列表中
     *
     * @param billingTypes 计费方式列表
     * @param billingType  计费方式枚举
     */
    private void addBillingType(List<Map<String, String>> billingTypes, EvidenceBillingTypeEnum billingType) {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("code", billingType.getCode());
        typeMap.put("name", billingType.getDesc());
        billingTypes.add(typeMap);
    }
} 