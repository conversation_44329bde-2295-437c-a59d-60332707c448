package com.coocaa.cheese.crm.util.translate;

import com.coocaa.ad.translate.Translator;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.ad.translate.util.TransUtils;
import com.coocaa.cheese.crm.common.db.entity.CompanyEntity;
import com.coocaa.cheese.crm.common.db.service.ICompanyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 企业相关数据翻译
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CompanyTranslator implements Translator<Integer> {
    private final ICompanyService companyService;

    @Override
    public String getTransType() {
        return TransTypes.COMPANY;
    }

    @Override
    public Class<Integer> getDataType() {
        return Integer.class;
    }

    @Override
    public Map<Integer, String> getMapping(Collection<Integer> sourceValues) {
        return TransUtils.toNumValMap(sourceValues, ids -> companyService.lambdaQuery()
                .select(CompanyEntity::getId, CompanyEntity::getName)
                .in(CompanyEntity::getId, ids)
                .list().stream()
                .collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getName, (o, n) -> n)));
    }
}
