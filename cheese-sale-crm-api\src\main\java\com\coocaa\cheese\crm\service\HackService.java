package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.entity.BankEntity;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.ChannelEntity;
import com.coocaa.cheese.crm.common.db.entity.CompanyEntity;
import com.coocaa.cheese.crm.common.db.entity.DiscountRuleEntity;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineEntity;
import com.coocaa.cheese.crm.common.db.entity.ProductLineSignSubjectEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBankService;
import com.coocaa.cheese.crm.common.db.service.IBrandService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IChannelService;
import com.coocaa.cheese.crm.common.db.service.ICompanyService;
import com.coocaa.cheese.crm.common.db.service.IDiscountRuleService;
import com.coocaa.cheese.crm.common.db.service.IInstitutionAccountService;
import com.coocaa.cheese.crm.common.db.service.IProductLineService;
import com.coocaa.cheese.crm.common.db.service.IProductLineSignSubjectService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用于特殊处理数据
 * - 清理线上环境测试数据
 * - 对某些数据进行特殊处理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class HackService {
    private final ConfigService configService;

    private final IBankService bankService;
    private final IBrandService brandService;
    private final IChannelService channelService;
    private final ICompanyService companyService;
    private final IBusinessService businessService;
    private final IProductLineService productLineService;
    private final IDiscountRuleService discountRuleService;
    private final IInstitutionAccountService institutionAccountService;
    private final IAdvertisingSubjectService advertisingSubjectService;
    private final IProductLineSignSubjectService productLineSignSubjectService;


    /**
     * 检查线上数据清理权限
     * 只有配置在白名单中的用户ID才能删除
     *
     * @throws BusinessException 未配置或无权限操作
     */
    public void checkDeletePermission() throws BusinessException {
        String configKey = "clean_verify_data_allow_users";
        String configValue = configService.getConfigValue(configKey);
        if (StrUtil.isBlank(configValue)) {
            throw new BusinessException("未配置清理测试数据白名单");
        }

        // 配置: 允许操作的用户
        Set<Integer> allowUserIds = Arrays.stream(StringUtils.split(configValue, Constants.COMMA))
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
        if (!allowUserIds.contains(UserThreadLocal.getUserId())) {
            throw new BusinessException("无权清理测试数据");
        }
    }


    /**
     * 删除折扣规则
     *
     * @param ruleId 规则ID
     * @return true: 删除成功
     */
    public boolean deleteDiscountRule(Integer ruleId) {
        return discountRuleService.lambdaUpdate()
                .set(DiscountRuleEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .set(DiscountRuleEntity::getOperator, UserThreadLocal.getUserId())
                .set(DiscountRuleEntity::getUpdateTime, LocalDateTime.now())
                .eq(DiscountRuleEntity::getId, ruleId)
                .update();
    }


    /**
     * 删除机构账户
     *
     * @param accountId 机构账户ID
     * @return true: 删除成功
     */
    public boolean deleteInstitutionAccount(Integer accountId) {
        return institutionAccountService.lambdaUpdate()
                .set(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .set(InstitutionAccountEntity::getOperator, UserThreadLocal.getUserId())
                .set(InstitutionAccountEntity::getUpdateTime, LocalDateTime.now())
                .eq(InstitutionAccountEntity::getId, accountId)
                .update();
    }


    /**
     * 批量删除所有测试数据
     *
     * @param businessId 商机ID
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBusiness(Integer businessId) {
        // 查询指定商机
        BusinessEntity business = Optional.ofNullable(businessService.getById(businessId))
                .orElseThrow(() -> new BusinessException("商机(" + businessId + ")不存在"));
        log.info("[测试数据] 根据商机({})查询关联品牌、投放主体、渠道", businessId);

        // 删除品牌 & 品牌下公司 & 产品线
        if (Objects.nonNull(business.getBrandId())) {
            deleteBrands(List.of(business.getBrandId()));
        }

        // 删除渠道
        if (Objects.nonNull(business.getChannelId())) {
            deleteChannels(List.of(business.getChannelId()));
        }

        // 删除投放主体
        if (Objects.nonNull(business.getAdvertisingSubjectId())) {
            deleteAdvertisingSubjects(List.of(business.getAdvertisingSubjectId()));
        }

        // 删除商机
        businessService.lambdaUpdate()
                .set(BusinessEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .set(BusinessEntity::getOperator, UserThreadLocal.getUserId())
                .set(BusinessEntity::getUpdateTime, LocalDateTime.now())
                .eq(BusinessEntity::getId, businessId)
                .update();

        return true;
    }


    // ========================= 开始删除基础数据 =========================


    /**
     * 删除品牌
     */
    private boolean deleteBrands(Collection<Integer> brandIds) {
        if (CollUtil.isEmpty(brandIds)) {
            return false;
        }

        // 当前登陆用户ID
        Integer userId = UserThreadLocal.getUserId();
        LocalDateTime now = LocalDateTime.now();

        // 删除品牌下公司
        boolean success = false;
        Set<Integer> companyIds = brandService.lambdaQuery()
                .select(BrandEntity::getId, BrandEntity::getCompanyId)
                .in(BrandEntity::getId, brandIds)
                .list().stream()
                .map(BrandEntity::getCompanyId)
                .collect(Collectors.toSet());
        log.info("[测试数据] 根据品牌({})找到公司({})", StrUtil.join(Constants.COMMA, brandIds), StrUtil.join(Constants.COMMA, companyIds));

        // 删除品牌关联的公司
        success |= deleteCompanies(companyIds);

        // 删除产品线
        Set<Long> productLineIds = productLineService.lambdaQuery()
                .select(ProductLineEntity::getId)
                .in(ProductLineEntity::getBrandId, brandIds)
                .list().stream()
                .map(ProductLineEntity::getId)
                .collect(Collectors.toSet());
        log.info("[测试数据] 根据品牌({})找到产品线({})", StrUtil.join(Constants.COMMA, brandIds), StrUtil.join(Constants.COMMA, productLineIds));

        if (CollUtil.isNotEmpty(productLineIds)) {
            success |= productLineSignSubjectService.lambdaUpdate()
                    .set(ProductLineSignSubjectEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                    .in(ProductLineSignSubjectEntity::getProductLineId, productLineIds)
                    .update();
            log.info("[测试数据] 删除签约公司与产品线({})关系", StrUtil.join(Constants.COMMA, productLineIds));

            success |= productLineService.lambdaUpdate()
                    .set(ProductLineEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                    .set(ProductLineEntity::getOperator, userId)
                    .set(ProductLineEntity::getUpdateTime, now)
                    .in(ProductLineEntity::getId, productLineIds)
                    .update();
            log.info("[测试数据] 删除产品线({})", StrUtil.join(Constants.COMMA, productLineIds));
        }

        // 删除品牌
        success |= brandService.lambdaUpdate()
                .set(BrandEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .set(BrandEntity::getOperator, userId)
                .set(BrandEntity::getUpdateTime, now)
                .in(BrandEntity::getId, brandIds)
                .update();

        return success;
    }


    /**
     * 删除渠道
     */
    private boolean deleteChannels(Collection<Integer> channelIds) {
        if (CollUtil.isEmpty(channelIds)) {
            return false;
        }

        boolean success = channelService.lambdaUpdate()
                .set(ChannelEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .set(ChannelEntity::getOperator, UserThreadLocal.getUserId())
                .set(ChannelEntity::getUpdateTime, LocalDateTime.now())
                .in(ChannelEntity::getId, channelIds)
                .update();
        log.info("[测试数据] 删除渠道({})", StrUtil.join(Constants.COMMA, channelIds));

        return success;
    }

    /**
     * 删除投放主体
     */
    private boolean deleteAdvertisingSubjects(Collection<Integer> advertisingSubjectIds) {
        if (CollUtil.isEmpty(advertisingSubjectIds)) {
            return false;
        }

        // 删除投放主体下公司
        boolean success = false;
        Set<Integer> companyIds = advertisingSubjectService.lambdaQuery()
                .select(AdvertisingSubjectEntity::getId, AdvertisingSubjectEntity::getCompanyId)
                .in(AdvertisingSubjectEntity::getId, advertisingSubjectIds)
                .list().stream()
                .map(AdvertisingSubjectEntity::getCompanyId)
                .collect(Collectors.toSet());
        log.info("[测试数据] 根据投放主体({})找到公司({})", StrUtil.join(Constants.COMMA, advertisingSubjectIds), StrUtil.join(Constants.COMMA, companyIds));

        // 删除投放主体关联的公司
        success |= deleteCompanies(companyIds);

        // 删除投放主体
        success |= advertisingSubjectService.lambdaUpdate()
                .set(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .in(AdvertisingSubjectEntity::getId, advertisingSubjectIds)
                .update();
        log.info("[测试数据] 删除投放主体({})", StrUtil.join(Constants.COMMA, advertisingSubjectIds));

        return success;
    }

    /**
     * 删除公司
     */
    private boolean deleteCompanies(Collection<Integer> companyIds) {
        if (CollUtil.isEmpty(companyIds)) {
            return false;
        }

        // 当前登陆用户ID
        Integer userId = UserThreadLocal.getUserId();
        LocalDateTime now = LocalDateTime.now();

        boolean success = companyService.lambdaUpdate()
                .set(CompanyEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .set(CompanyEntity::getOperator, userId)
                .set(CompanyEntity::getUpdateTime, now)
                .in(CompanyEntity::getId, companyIds)
                .update();
        log.info("[测试数据] 删除公司({})", StrUtil.join(Constants.COMMA, companyIds));

        // 删除银行
        success |= bankService.lambdaUpdate()
                .set(BankEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .in(BankEntity::getCompanyId, companyIds)
                .update();
        log.info("[测试数据] 根据公司({})删除银行", StrUtil.join(Constants.COMMA, companyIds));

        // 删除机构账户
        success |=  institutionAccountService.lambdaUpdate()
                .set(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .set(InstitutionAccountEntity::getOperator, userId)
                .set(InstitutionAccountEntity::getUpdateTime, now)
                .in(InstitutionAccountEntity::getCompanyId, companyIds)
                .update();

        return success;
    }
}


