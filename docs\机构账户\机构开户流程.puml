@startuml
title 机构开户流程

actor "销管内勤" as Admin
participant "前端页面" as Frontend
participant "机构账户管理\n/api/crm/institution-account/*" as AccountController
participant "机构账户Service" as AccountService
participant "资金账户Service" as FundAccountService
database "机构账户表\ninstitution_account" as AccountDB
database "资金账户表\ninstitution_fund_account" as FundDB

== 开户流程 ==

Admin -> Frontend: 提交开户信息
Frontend -> AccountController: POST /api/crm/institution-account
note right: 创建机构账户

AccountController -> AccountService: 调用创建账户服务

AccountService -> AccountService: 参数校验
note right: 验证企业ID、授权日期、归属人等必填信息

AccountService -> AccountService: 生成账户编号
note right: 生成10位数字+大写英文随机编码

AccountService -> AccountDB: 保存机构账户信息
note right
- 账户编号
- 企业ID
- 授权开始/截止日期
- 账户状态(NORMAL)
- 归属人信息
- 创建人信息
end note

AccountDB --> AccountService: 返回账户ID

AccountService -> FundAccountService: 创建资金账户

FundAccountService -> FundDB: 保存资金账户信息
note right
- 关联机构账户ID
- 初始化账户余额为0
- 初始化各类累计金额为0
- 账户状态(NORMAL)
end note

FundDB --> FundAccountService: 返回操作结果
FundAccountService --> AccountService: 返回创建结果

AccountService --> AccountController: 返回账户ID
AccountController --> Frontend: 返回开户结果
Frontend --> Admin: 显示开户成功提示

@enduml