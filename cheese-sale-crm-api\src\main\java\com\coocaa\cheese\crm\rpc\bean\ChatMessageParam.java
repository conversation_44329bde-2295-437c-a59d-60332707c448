package com.coocaa.cheese.crm.rpc.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
public class ChatMessageParam {

    @Schema(description = "应用类型编码，字典0124")
    private String appCode;

    @Schema(description = "标题,默认为消息通知")
    private String title;

    @Schema(description = "内容")
    @NotBlank(message = "内容不能为空")
    private String content;

    @Schema(description = "群组id，默认为异常消息群")
    private String chatId;

    @Schema(description = "是否需要记录")
    private Boolean recordFlag;
}
