package com.coocaa.cheese.crm.service;

import com.coocaa.cheese.crm.common.tools.util.CosUtils;
import com.coocaa.cheese.crm.convert.CosConvert;
import com.coocaa.cheese.crm.vo.CosVO;
import com.tencent.cloud.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CosService {
    private final CosConvert cosConvert;

    @Value("${cos.region}")
    private String region;

    @Value("${cos.bucket-name}")
    private String bucket;

    @Value("${cos.domain:default}")
    private String domain;

    public CosVO getTempKey() {
        // 通过mapstruct将Response转换为CosVO
        Response response = CosUtils.getCosSts();
        CosVO cosVO = cosConvert.toVo(response);
        cosVO.setRegion(region);
        cosVO.setBucket(bucket);
        if ("default".equals(domain)) {
            cosVO.setDomain(String.format("https://%s.cos.%s.myqcloud.com/", bucket, region));
        } else {
            cosVO.setDomain(domain);
        }
        return cosVO;
    }
}
