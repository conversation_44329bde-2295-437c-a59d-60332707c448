package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户报备查询DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-14
 */
@Data
public class DashboardCustomerReportSearchDTO {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 所属公司ID
     */
    private Integer companyId;

    /**
     * 所属公司名称
     */
    private String companyName;

    /**
     * 创建人ID
     */
    private Integer creator;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 管理部门ID
     */
    private String departmentId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 生效状态(1:生效,0:不生效)
     */
    private Integer effectiveStatus;

    /**
     * 删除标志(0:未删除,1:已删除)
     */
    private Integer deleteFlag;
}
