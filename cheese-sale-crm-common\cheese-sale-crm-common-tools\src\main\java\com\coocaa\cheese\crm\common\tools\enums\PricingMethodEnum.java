package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 计价方式枚举
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Getter
@AllArgsConstructor
public enum PricingMethodEnum implements IEnumType<String> {
    /**
     * 自助选择
     */
    SELF_SELECT("0119-1", "自助选择"),

    /**
     * 固定按天
     */
    FIXED_DAILY("0119-2", "按天计价"),

    /**
     * 固定按周
     */
    FIXED_WEEKLY("0119-3", "按周计价");

    private final String code;
    private final String desc;

    private static final Map<String, PricingMethodEnum> BY_CODE_MAP =
            Arrays.stream(PricingMethodEnum.values())
                    .collect(Collectors.toMap(PricingMethodEnum::getCode, item -> item));

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * 将代码转成枚举
     */
    public static PricingMethodEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code         代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static PricingMethodEnum parse(String code, PricingMethodEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(PricingMethodEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 