package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 转移原因枚举
 * 离职移交, 在职移交
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Getter
@AllArgsConstructor
public enum TransferReasonEnum implements IEnumType<String> {
    RESIGNATION("0075-1", "离职移交"),
    ON_JOB("0075-2", "在职移交"),
    PK_CHALLENGE("0075-3", "PK挑战");

    private final String code;
    private final String desc;

    private static final Map<String, TransferReasonEnum> BY_CODE_MAP =
            Arrays.stream(TransferReasonEnum.values())
                    .collect(Collectors.toMap(TransferReasonEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static TransferReasonEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static TransferReasonEnum parse(String code, TransferReasonEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(TransferReasonEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 