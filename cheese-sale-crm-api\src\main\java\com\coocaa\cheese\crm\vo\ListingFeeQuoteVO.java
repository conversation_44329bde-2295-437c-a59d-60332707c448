package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-09
 */
@Data
public class ListingFeeQuoteVO {
    /**
     * 资金变动记录ID
     */
    @Schema(description = "资金变动记录ID", type = "Integer", example = "1")
    private Integer fundChangeRecordId;

    /**
     * 投放城市id
     */
    @Schema(description = "投放城市id", type = "Integer", example = "1")
    private Integer cityId;

    /**
     * 投放城市名称
     */
    @Schema(description = "投放城市名称", type = "String", example = "北京")
    private String cityName;

    /**
     * 计费方式 (0087)
     */
    @Schema(description = "计费方式 (0087)", type = "String", example = "0087-1")
    @TransField(type = TransTypes.DICT)
    private String billingType;
    @Schema(description = "计费方式 ", type = "String", example = "刊例费")
    private String billingTypeName;

    /**
     * 点位数量
     */
    @Schema(description = "点位数量", type = "Integer", example = "1")
    private Integer pointCount;

    /**
     * 计费点位数量
     */
    @Schema(description = "计费点位数量", type = "Integer", example = "1")
    private Integer billingPointCount;

    /**
     * 点位单价
     */
    @Schema(description = "点位单价", type = "BigDecimal", example = "1.00")
    private BigDecimal pointUnitPrice;

    /**
     * 计费周期
     */
    @Schema(description = "计费周期", type = "Integer", example = "1")
    private Integer billingCycle;

    /**
     * 适用折扣
     */
    @Schema(description = "适用折扣", type = "BigDecimal", example = "1.00")
    private BigDecimal applicableDiscount;

    /**
     * 折前总价
     */
    @Schema(description = "折前总价", type = "BigDecimal", example = "1.00")
    private BigDecimal preDiscountTotal;

    /**
     * 折后总价
     */
    @Schema(description = "折后总价", type = "BigDecimal", example = "1.00")
    private BigDecimal postDiscountTotal;

    /**
     * 实际发生
     */
    @Schema(description = "实际发生", type = "BigDecimal", example = "1.00")
    private BigDecimal actualAmount;

    /**
     * 占位费用
     */
    @Schema(description = "占位费用", type = "BigDecimal", example = "1.00")
    private BigDecimal occupyAmount;

}
