package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.BankParam;
import com.coocaa.cheese.crm.bean.BankQueryParam;
import com.coocaa.cheese.crm.common.db.bean.BankQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.BankEntity;
import com.coocaa.cheese.crm.vo.BankVO;
import com.coocaa.cheese.crm.vo.BrandVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 银行账户信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Mapper
public interface BankConvert extends PageableConvert<BankEntity, BankVO> {
    BankConvert INSTANCE = Mappers.getMapper(BankConvert.class);

    /**
     * Entity 转 VO
     */
    BankVO toVo(BankEntity entity);

    /**
     * 转换查询参数
     */
    BankQueryDTO toDto(BankQueryParam param);

    /**
     * VO 转 Entity
     */
    BankEntity toEntity(BrandVO vo);

    /**
     * VO 转 Entity
     */
    BankEntity toEntity(BankParam param);

}
