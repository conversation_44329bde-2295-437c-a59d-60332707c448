@startuml
title 机构账户转移流程

actor "销管内勤" as Admin
participant "前端页面" as Frontend
participant "机构账户管理\n/api/crm/institution-account/transfer" as AccountController
participant "机构账户Service" as AccountService
database "机构账户表\ninstitution_account" as AccountDB
database "转移记录表\ninstitution_transfer_record" as TransferDB

== 账户转移流程 ==

Admin -> Frontend: 选择要转移的账户
note right: 可以批量选择多个账户

Admin -> Frontend: 点击"转移"按钮
Frontend -> Frontend: 显示转移表单

Admin -> Frontend: 选择目标部门和归属人
note right: 填写转移原因和备注说明

Admin -> Frontend: 提交转移申请
Frontend -> AccountController: POST /api/crm/institution-account/transfer
note right: 发送转移请求

AccountController -> AccountService: 调用转移服务

AccountService -> AccountService: 参数校验
note right: 验证账户ID列表、目标归属人等必填信息

AccountService -> AccountDB: 查询账户信息
AccountDB --> AccountService: 返回账户列表

loop 对每个选中的账户
    AccountService -> AccountService: 保存原始归属信息
    AccountService -> AccountDB: 更新账户归属信息
    AccountService -> TransferDB: 创建转移记录
end

AccountService --> AccountController: 返回转移结果
AccountController --> Frontend: 返回转移结果
Frontend --> Admin: 显示转移成功提示

@enduml