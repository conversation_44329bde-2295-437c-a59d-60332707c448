package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.entity.InstitutionFundChangeRecordEntity;

/**
 * 机构资金变动记录 服务类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
public interface IInstitutionFundChangeRecordService extends IService<InstitutionFundChangeRecordEntity> {
    /**
     * 分页查询资金变动记录列表
     *
     * @param page      分页条件
     * @param condition 查询条件
     * @return 资金变动记录数据列表
     */
    IPage<InstitutionFundChangeRecordEntity> pageList(IPage<InstitutionFundChangeRecordEntity> page, Object condition);

    /**
     * 根据方案id查询资金变动记录
     * @param planId 方案id
     * @return 资金变动记录
     */
    InstitutionFundChangeRecordEntity getPlanTransferDeductionRecord(Integer planId);
} 