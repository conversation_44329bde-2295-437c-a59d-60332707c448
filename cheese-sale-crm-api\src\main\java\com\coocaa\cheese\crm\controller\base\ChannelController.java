package com.coocaa.cheese.crm.controller.base;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.ChannelParam;
import com.coocaa.cheese.crm.bean.ChannelQueryParam;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.ChannelService;
import com.coocaa.cheese.crm.vo.ChannelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@RestController
@RequestMapping("/channels")
@Tag(name = "渠道管理", description = "渠道管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ChannelController extends BaseController {
    private final ChannelService channelService;

    /**
     * 一级渠道列表
     */
    @Operation(summary = "一级渠道列表")
    @GetMapping("/first-level")
    public ResultTemplate<List<ChannelVO>> getFirstLevelChannels() {
        return ResultTemplate.success(channelService.listByLevel(true));
    }

    /**
     * 二级渠道列表
     */
    @Operation(summary = "二级渠道列表")
    @GetMapping("/last-level")
    public ResultTemplate<List<ChannelVO>> getLastLevelChannels() {
        return ResultTemplate.success(channelService.listByLevel(false));
    }

    /**
     * 二级渠道下拉列表
     */
    @Operation(summary = "二级渠道下拉列表")
    @GetMapping("/dropdown")
    public ResultTemplate<List<CodeNameVO>> listChannels(
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "alias", required = false, defaultValue = "false") boolean alias) {
        return ResultTemplate.success(channelService.listChannels(name, alias));
    }

    /**
     * 根据ID批量查询列表
     */
    @Operation(summary = "根据ID批量查询列表")
    @PostMapping("/list/ids")
    public ResultTemplate<List<CodeNameVO>> listByIds(@RequestBody List<Integer> ids) {
        return ResultTemplate.success(channelService.listByIds(ids));
    }

    /**
     * 渠道列表(分页)
     */
    @Operation(summary = "渠道列表(分页)")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<ChannelVO>> pageList(@RequestBody PageRequestVO<ChannelQueryParam> pageRequest) {
        return ResultTemplate.success(channelService.pageList(pageRequest));
    }

    /**
     * 渠道详情
     */
    @Operation(summary = "渠道详情")
    @Parameter(name = "id", description = "渠道ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<ChannelVO> getDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(channelService.getDetail(id));
    }

    /**
     * 渠道创建
     */
    @Operation(summary = "创建渠道")
    @PostMapping
    public ResultTemplate<Boolean> create(@RequestBody @Validated ChannelParam channel) {
        return ResultTemplate.success(channelService.createOrUpdate(null, channel));
    }

    /**
     * 渠道修改
     */
    @Operation(summary = "渠道修改")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> update(@PathVariable("id") Integer id, @RequestBody ChannelParam channel) {
        return ResultTemplate.success(channelService.createOrUpdate(id, channel));
    }

    /**
     * 渠道删除
     */
    @Operation(summary = "渠道删除")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> delete(@PathVariable("id") Integer id) {
        return ResultTemplate.success(channelService.delete(id));
    }
}
