package com.coocaa.cheese.crm.controller;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.common.tools.enums.DelayQueueEnum;
import com.coocaa.cheese.crm.task.DelayQueueConsumerTask;
import com.coocaa.cheese.crm.util.MessageProducerUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Tag(name = "延迟消息", description = "延迟消息管理")
@RestController
@RequestMapping("/delayed-messages")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DelayedMessageController {

    private final MessageProducerUtils messageProducerUtils;
    private final DelayQueueConsumerTask delayQueueConsumerTask;

    @Operation(summary = "发送简单延迟消息")
    @PostMapping("/simple")
    public ResultTemplate<Void> sendSimpleMessage(
            @Parameter(description = "消息内容") @RequestParam String content,
            @Parameter(description = "消息类型") @RequestParam String messageType) {

        DelayQueueEnum queueEnum = DelayQueueEnum.getByMessageType(messageType);
        if (queueEnum == null) {
            return ResultTemplate.fail("未找到指定的消息类型: " + messageType);
        }

        messageProducerUtils.sendMessage(queueEnum, content);
        return ResultTemplate.success();
    }

    @Operation(summary = "立即处理指定类型的延迟消息")
    @PostMapping("/process")
    public ResultTemplate<Void> processMessages(
            @Parameter(description = "消息类型，为空则处理所有类型")
            @RequestParam(required = false) String messageType) {

        delayQueueConsumerTask.processQueues(messageType);
        return ResultTemplate.success();
    }

    @Operation(summary = "立即处理指定类型的全部数据")
    @PostMapping("/advertisingSubjectPeriodJob")
    public ResultTemplate<Void> advertisingSubjectPeriodJob() {

        delayQueueConsumerTask.advertisingSubjectPeriodJob();
        return ResultTemplate.success();
    }
} 