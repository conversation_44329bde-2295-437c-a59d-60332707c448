package com.coocaa.cheese.crm.controller.institution;

import com.coocaa.ad.common.annotation.RepeatStrategy;
import com.coocaa.ad.common.annotation.RepeatSubmit;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.InstitutionAccountListParam;
import com.coocaa.cheese.crm.bean.InstitutionAccountParam;
import com.coocaa.cheese.crm.bean.InstitutionAccountQueryParam;
import com.coocaa.cheese.crm.common.tools.constant.BusinessConstants;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.InstitutionAccountService;
import com.coocaa.cheese.crm.vo.InstitutionAccountDetailVO;
import com.coocaa.cheese.crm.vo.InstitutionAccountSimpleVO;
import com.coocaa.cheese.crm.vo.InstitutionAccountVO;
import com.coocaa.cheese.permission.core.annotation.DataPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 机构账户管理
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@RestController
@RequestMapping("/institution-accounts")
@Tag(name = "机构账户管理", description = "机构账户管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionAccountController extends BaseController {
    private final InstitutionAccountService institutionAccountService;

    /**
     * 机构账户列表(分页)
     */
    @Operation(summary = "机构账户列表(分页)")
    @PostMapping("/page")
    @DataPermission(tableName = BusinessConstants.INSTITUTION_ACCOUNT_TABLE_NAME)
    public ResultTemplate<PageResponseVO<InstitutionAccountVO>> pageListInstitutionAccount(@RequestBody PageRequestVO<InstitutionAccountQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(InstitutionAccountQueryParam::new));
        return ResultTemplate.success(institutionAccountService.pageListInstitutionAccount(pageRequest));
    }

    /**
     * 机构账户详情
     */
    @Operation(summary = "机构账户详情")
    @Parameter(name = "id", description = "机构账户ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<InstitutionAccountDetailVO> getInstitutionAccountDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(institutionAccountService.getInstitutionAccountDetail(id));
    }

    /**
     * 创建机构账户
     */
    @Operation(summary = "创建机构账户")
    @PostMapping
    @RepeatSubmit(strategy = RepeatStrategy.ALL_PARAMS)
    public ResultTemplate<Boolean> createInstitutionAccount(@RequestBody @Validated InstitutionAccountParam param) {
        return ResultTemplate.success(institutionAccountService.createInstitutionAccount(param));
    }

    /**
     * 更新机构账户
     */
    @Operation(summary = "更新机构账户")
    @Parameter(name = "id", description = "机构账户ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> updateInstitutionAccount(
            @PathVariable("id") Integer id,
            @RequestBody @Validated InstitutionAccountParam param) {
        return ResultTemplate.success(institutionAccountService.updateInstitutionAccount(id, param));
    }

    /**
     * 获取机构账户列表
     */
    @Operation(summary = "获取机构账户列表")
    @PostMapping("/list")
    public ResultTemplate<List<InstitutionAccountSimpleVO>> listInstitutionAccounts(
            @RequestBody InstitutionAccountListParam param) {
        return ResultTemplate.success(institutionAccountService.listInstitutionAccounts(param));
    }

    /**
     * 获取机构账户列表-带权限
     */
    @Operation(summary = "获取机构账户列表")
    @PostMapping("/list-permission")
    @DataPermission(tableName = BusinessConstants.INSTITUTION_ACCOUNT_TABLE_NAME)
    public ResultTemplate<List<InstitutionAccountSimpleVO>> listInstitutionAccountsPermission(
            @RequestBody InstitutionAccountListParam param) {
        return ResultTemplate.success(institutionAccountService.listInstitutionAccounts(param));
    }

    /**
     * 获取机构账户的计费方式和公司名称
     */
    @Operation(summary = "获取机构账户的计费方式和公司名称")
    @Parameter(name = "id", description = "机构账户ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}/billing-types")
    public ResultTemplate<Map<String, Object>> getInstitutionBillingTypesAndCompanyName(@PathVariable("id") Integer id) {
        return ResultTemplate.success(institutionAccountService.getInstitutionBillingTypesAndCompanyName(id));
    }

} 