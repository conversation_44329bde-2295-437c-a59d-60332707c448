package com.coocaa.cheese.crm.common.tools.enums;

import lombok.Getter;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-24
 */
@Getter
public enum OperateTypeEnum {
    OWNER_DEPARTMENT_CHANGE("0167-1", "归属人部门变更"),
    PRODUCT_LINE_STATUS_CHANGE("0167-2", "产品线状态变更");

    private final String code;
    private final String description;

    OperateTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
