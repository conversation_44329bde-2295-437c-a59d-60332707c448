package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.BusinessProgressEntity;
import com.coocaa.cheese.crm.common.db.mapper.BusinessProgressMapper;
import com.coocaa.cheese.crm.common.db.service.IBusinessProgressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 商机进度服务实现类
 *
 * <AUTHOR>
 * @since 2025/5/13
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessProgressServiceImpl
        extends ServiceImpl<BusinessProgressMapper, BusinessProgressEntity>
        implements IBusinessProgressService {
}
