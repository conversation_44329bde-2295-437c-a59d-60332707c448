package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 品牌报备H5参数
 * <AUTHOR>
 * @since 2025/4/29
 */
@Data
public class BrandReportH5Param {
    @Schema(description = "品牌ID", type = "Integer", example = "123")
    private Integer id;

    @Size(max = 20, message = "品牌名称不能超过{max}个字符")
    @Schema(description = "品牌名称", type = "String", example = "梅赛德斯")
    private String name;

    @Schema(description = "二级行业编码", type = "String", example = "001-02")
    private String industryCode;

    @Schema(description = "持有公司ID", type = "Integer", example = "123")
    private Integer companyId;

    @Schema(description = "品牌标签", type = "List<String>", example = "奔驰")
    private List<String> tags;
}
