package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 公海配额VO
 *
 * <AUTHOR>
 * @since 2025/4/30
 */
@Data
@Accessors(chain = true)
@Schema(name = "PublicSeaQuotaVO", description = "公海配额VO")
public class PublicSeaQuotaVO {

    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "用户ID", type = "Integer", example = "1")
    private Integer userId;

    @Schema(description = "用户名称", type = "String", example = "张三")
    private String userName;

    @Schema(description = "每月配额", type = "Integer", example = "10")
    private Integer monthlyQuota;

    @Schema(description = "每日配额", type = "Integer", example = "10")
    private Integer dailyQuota;

    @Schema(description = "临时配额", type = "Integer", example = "10")
    private Integer temporaryQuota;

    @Schema(description = "本月已用", type = "Integer", example = "10")
    private Integer monthlyUsed;

    @Schema(description = "本日已用", type = "Integer", example = "10")
    private Integer dailyUsed;

    @Schema(description = "临额已用", type = "Integer", example = "10")
    private Integer tempUsed;

    @Schema(description = "授权天数", type = "Integer", example = "10")
    private Integer authDay;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "操作人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer operator;

    @Schema(description = "操作人", type = "Integer", example = "1")
    private String operatorName;
}
