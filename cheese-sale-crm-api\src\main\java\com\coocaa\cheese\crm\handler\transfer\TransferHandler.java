package com.coocaa.cheese.crm.handler.transfer;

import com.coocaa.cheese.crm.bean.TransferParam;
import com.coocaa.cheese.crm.common.tools.enums.TransferBizTypeEnum;

/**
 * 转移处理器接口
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
public interface TransferHandler {

    /**
     * 获取转移业务类型
     *
     * @return 转移业务类型枚举
     */
    TransferBizTypeEnum getTransferBizType();

    /**
     * 处理转移操作
     *
     * @param param 转移参数
     * @return 处理结果
     */
    Boolean handleTransfer(TransferParam param);
} 