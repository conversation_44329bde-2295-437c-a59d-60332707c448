package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 站内审批任务pk申请列表详情返回参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Schema(name = "InnerApprovePkChallengeDetailVO", description = "站内审批任务pk申请列表详情返回参数")
public class InnerApprovePkChallengeDetailVO extends BaseInnerApproveDetailVO {

    @Schema(description = "签约主体类型(与品牌的关系，字典0068)")
    @TransField(type = TransTypes.DICT)
    private String type;
    private String typeName;

    @Schema(description = "品牌")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;

    @Schema(description = "所属行业")
    @TransField(type = TransTypes.INDUSTRY)
    private String industryCode;
    private String industryName;

    @Schema(description = "品牌别名")
    private String brandTag;

    @Schema(description = "是否TOP客户 [0:否, 1:是]")
    private Integer topFlag;

    @Schema(description = "产品线", type = "String", example = "1")
    private String productLineNames;

    @Schema(description = "商机进度(字典0073)", type = "String", example = "0073-1")
    @TransField(type = TransTypes.DICT)
    private String progress;
    private String progressName;

    @Schema(description = "当前归属人(归属人名/部门名)", type = "String")
    private String ownerNameAndDeptName;

    @Schema(description = "Pk挑战记录表参数", type = "PkChallengeRecordDetailVO")
    @TransField
    private PkChallengeRecordDetailVO pkChallengeRecordDetailVO;

}
