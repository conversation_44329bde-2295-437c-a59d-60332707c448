package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 渠道信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "ChannelVO", description = "渠道信息VO")
public class ChannelVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "父渠道ID", type = "Integer", example = "0")
    @TransField(type = TransTypes.CHANNEL)
    private Integer parentId;
    private String parentName;

    @Schema(description = "渠道名称", type = "String", example = "线下门店")
    private String name;

    @Schema(description = "渠道别名", type = "String", example = "门店")
    private String nickName;

    @Schema(description = "备注说明", type = "String", example = "线下实体门店渠道")
    private String description;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @TransField(type = TransTypes.USER)
    @Schema(description = "操作人", type = "Integer", example = "1")
    private Integer operator;

    @Schema(description = "操作人", type = "Integer", example = "1")
    private String operatorName;
} 