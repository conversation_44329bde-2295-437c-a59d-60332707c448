package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
@Data
@Accessors(chain = true)
@Schema(name = "InnerApproveDetailVO", description = "站内审批任务详情返回参数")
public class InnerApproveDetailVO {

    @Schema(description = "签约主体id")
    private Integer id;

    @Schema(description = "审批任务名称")
    private String approvalName;

    @Schema(description = "申请人")
    @TransField(type = TransTypes.USER, target = "instanceUserName")
    private Integer instanceUserId;
    private String instanceUserName;

    @Schema(description = "申请部门")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime instanceCreateTime;

    @Schema(description = "完成时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime endTime;

    @Schema(description = "审批结果，字典(0138)")
    @TransField(type = TransTypes.DICT)
    private String approvalResult;
    private String approvalResultName;

    @Schema(description = "品牌")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "既有还是新增品牌(1:新增 2:既有)")
    private Integer brandTimeFlag;

    @Schema(description = "所属行业")
    @TransField(type = TransTypes.INDUSTRY)
    private String industryCode;
    private String industryName;

    @Schema(description = "品牌别名")
    private String brandTag;

    @Schema(description = "持有公司ID")
    @TransField(type = TransTypes.COMPANY, target = "holdCompanyName")
    private Integer holdCompanyId;
    private String holdCompanyName;

    @Schema(description = "产品线")
    private String productLine;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY, target = "advertisingSubjectName")
    private Integer companyId;
    private String advertisingSubjectName;

    @Schema(description = "签约主体类型(与品牌的关系，字典0068)")
    @TransField(type = TransTypes.DICT)
    private String type;
    private String typeName;

    @Schema(description = "是否TOP客户 [0:否, 1:是]")
    private Integer topFlag;

    @Schema(description = "备注说明(情况说明)")
    private String description;
}
