package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.bean.AdvertisingSubjectQueryDTO;
import com.coocaa.cheese.crm.common.db.bean.DashboardCustomerReportSearchDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.SignSubjectApproveDTO;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.mapper.AdvertisingSubjectMapper;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 签约主体 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AdvertisingSubjectServiceImpl
        extends ServiceImpl<AdvertisingSubjectMapper, AdvertisingSubjectEntity>
        implements IAdvertisingSubjectService {

    @Override
    public IPage<DashboardCustomerReportSearchDTO> customerReportPageList(Page<DashboardCustomerReportSearchDTO> page, String query) {
        return getBaseMapper().customerReportPageList(page, query);
    }

    @Override
    public IPage<AdvertisingSubjectEntity> pageList(IPage<AdvertisingSubjectEntity> page, AdvertisingSubjectQueryDTO condition) {
        // 结束日期+1天
        if (Objects.nonNull(condition.getCreateEndDate())) {
            condition.setCreateEndDate(condition.getCreateEndDate().plusDays(1));
        }
        return getBaseMapper().pageList(page, condition);
    }

    @Override
    public IPage<AdvertisingSubjectEntity> h5PageList(IPage<AdvertisingSubjectEntity> page, AdvertisingSubjectQueryDTO condition) {
        return getBaseMapper().h5PageList(page, condition);
    }

    @Override
    public List<SignSubjectApproveDTO> queryInnerSubjectEntity(List<Integer> bizIds) {
        return getBaseMapper().queryInnerSubjectEntity(bizIds);
    }

    @Override
    public InnerApproveDetailDTO queryInnerApproveDetail(Integer id) {
        return getBaseMapper().queryInnerApproveDetail(id);
    }

    @Override
    public List<SignSubjectApproveDTO> innerSubjectEntity(List<Integer> advertisingSubjectIds) {
        return getBaseMapper().innerSubjectEntity(advertisingSubjectIds);
    }

    @Override
    public IPage<AdvertisingSubjectEntity> publicSeaPageList(IPage<AdvertisingSubjectEntity> page,
                                                             AdvertisingSubjectQueryDTO condition) {
        return getBaseMapper().publicSeaPageList(page, condition);
    }
}
