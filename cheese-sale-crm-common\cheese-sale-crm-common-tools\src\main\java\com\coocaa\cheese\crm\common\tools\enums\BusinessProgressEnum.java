package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 商机进度枚举
 * 初始, 初步接洽, 合同推进, 已签约
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Getter
@AllArgsConstructor
public enum BusinessProgressEnum implements IEnumType<String> {
    INITIAL("0073-1", "初步接洽"),
    FOLLOWING("0073-2", "高意向"),
    CONTRACT_PUSHING("0073-3", "商务推进"),
    SIGNED("0073-4", "成交");

    private final String code;
    private final String desc;

    private static final Map<String, BusinessProgressEnum> BY_CODE_MAP =
            Arrays.stream(BusinessProgressEnum.values())
                    .collect(Collectors.toMap(BusinessProgressEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static BusinessProgressEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static BusinessProgressEnum parse(String code, BusinessProgressEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(BusinessProgressEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 