package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.vo.BusinessIndexVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商机信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Mapper
public interface BusinessIndexConvert extends PageableConvert<BusinessEntity, BusinessIndexVO> {
    BusinessIndexConvert INSTANCE = Mappers.getMapper(BusinessIndexConvert.class);

    /**
     * Entity 转 VO
     */
    BusinessIndexVO toVo(BusinessEntity entity);
} 