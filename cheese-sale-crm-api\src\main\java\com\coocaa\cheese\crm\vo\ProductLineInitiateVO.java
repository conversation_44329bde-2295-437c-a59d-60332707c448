
package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 产品线变更发起查询VO
 *
 * <AUTHOR>
 * @since 2025-6-18
 */
@Data
@Accessors(chain = true)
@Schema(name = "ProductLineInitiateVO", description = "产品线变更发起查询VO")
public class ProductLineInitiateVO {

    @Schema(description = "商机ID")
    private Integer id;

    @Schema(description = "品牌")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "签约主体ID")
    private Integer signSubjectId;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY, target = "signSubjectName")
    private Integer companyId;
    private String signSubjectName;

    @Schema(description = "当前商机进度(字典0073)")
    @TransField(type = TransTypes.DICT)
    private String progress;
    private String progressName;

    @Schema(description = "产品线")
    private List<String> productLines;
}