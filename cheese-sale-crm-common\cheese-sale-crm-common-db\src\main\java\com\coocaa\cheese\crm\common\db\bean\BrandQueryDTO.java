package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 品牌查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
public class BrandQueryDTO {
    /**
     * 品牌名标签名称
     */
    private String name;

    /**
     * 是否精确查询 (1:精确, 0:模糊)
     */
    private Integer exactFlag;

    /**
     * 行业
     */
    private String industryCode;

    /**
     * 持有公司
     */
    private String companyName;

    /**
     * 生效状态
     */
    private Integer effectiveStatus;

    /**
     * 创建开始日期
     */
    private LocalDate createStartDate;

    /**
     * 创建结束日期
     */
    private LocalDate createEndDate;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 创建人数组
     */
    private List<Integer> creatorList;
}
