package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-09
 */
@Data
public class ListingFeeQuoteParam {
    @Schema(description = "资金变动记录id", type = "Integer", example = "1")
    @NotNull(message = "资金变动记录id不能为空")
    private Integer fundChangeRecordId;
}
