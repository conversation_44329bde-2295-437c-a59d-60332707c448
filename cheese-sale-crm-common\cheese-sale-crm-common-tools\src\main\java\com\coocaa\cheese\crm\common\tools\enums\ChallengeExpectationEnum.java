package com.coocaa.cheese.crm.common.tools.enums;

import lombok.Getter;

/**
 * 挑战期望枚举类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-01
 */
@Getter
public enum ChallengeExpectationEnum {

    CUSTOMER_TRANSFER("0165-1", "客户转让给我"),
    ALLOW_COLLABORATION("0165-2", "我要参与协作");

    private final String code;
    private final String description;

    ChallengeExpectationEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}
