package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售-站内审批对象类型(字典0145)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum StationTargetEnum implements IEnumType<String> {

    CUSTOMER_REPORT("0145-1", "客户报备"),
    BUSINESS_DELAY("0145-2", "商机延期"),
    CONTRACT_APPLY("0145-3", "合同申请"),
    PUBLIC_SEA_SALVAGE("0145-4", "公海打捞"),
    PK_CHALLENGE("0145-5", "PK挑战"),
    PRODUCT_LINE_CHANGE("0145-6", "产品线变更");

    private final String code;
    private final String desc;

    private static final Map<String, StationTargetEnum> BY_CODE_MAP =
            Arrays.stream(StationTargetEnum.values())
                    .collect(Collectors.toMap(StationTargetEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static StationTargetEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static StationTargetEnum parse(String code, StationTargetEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(StationTargetEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 