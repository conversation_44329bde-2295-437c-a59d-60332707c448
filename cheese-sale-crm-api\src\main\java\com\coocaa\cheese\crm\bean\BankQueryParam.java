package com.coocaa.cheese.crm.bean;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.EncryptDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 银行管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Data
public class BankQueryParam {
    @Schema(description = "企业名称", type = "String", example = "深圳金拱门食品有限公司")
    private String companyName;

    @Schema(description = "开户行", type = "String", example = "招商银行")
    private String bankName;

    @Schema(description = "银行帐号(加密)", type = "String", example = "1234xx456")
    @JSONField(deserializeUsing = EncryptDeserializer.class)
    private String account;

    @Schema(description = "帐户类型(字典0078)", type = "String", example = "0078-1")
    private String type;
}
