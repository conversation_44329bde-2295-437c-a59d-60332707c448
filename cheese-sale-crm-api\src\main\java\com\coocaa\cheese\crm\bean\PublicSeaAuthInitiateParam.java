package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 发起打捞申请入参
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Schema(name = "PublicSeaAuthInitiateParam", description = "发起打捞申请入参")
public class PublicSeaAuthInitiateParam {

    @NotNull(message = "商机ID不能为空")
    @Schema(description = "商机ID")
    private Integer businessId;

    @NotBlank(message = "情况说明不能为空")
    @Schema(description = "情况说明", maxLength = 100)
    private String remark;
}