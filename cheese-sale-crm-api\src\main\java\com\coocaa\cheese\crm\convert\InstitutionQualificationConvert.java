package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.InstitutionQualificationParam;
import com.coocaa.cheese.crm.common.db.entity.InstitutionQualificationEntity;
import com.coocaa.cheese.crm.vo.InstitutionQualificationVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 机构资质信息转换
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Mapper
public interface InstitutionQualificationConvert extends PageableConvert<InstitutionQualificationEntity, InstitutionQualificationVO> {
    InstitutionQualificationConvert INSTANCE = Mappers.getMapper(InstitutionQualificationConvert.class);

    /**
     * Entity 转 VO
     */
    InstitutionQualificationVO toVo(InstitutionQualificationEntity entity);

    /**
     * Entity列表 转 VO列表
     */
    List<InstitutionQualificationVO> entityToVO(List<InstitutionQualificationEntity> entityList);

    /**
     * Param 转 Entity
     */
    InstitutionQualificationEntity toEntity(InstitutionQualificationParam param);
} 