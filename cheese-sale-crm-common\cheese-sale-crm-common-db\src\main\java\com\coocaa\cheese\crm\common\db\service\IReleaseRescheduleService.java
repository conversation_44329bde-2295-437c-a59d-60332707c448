package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveTaskPageDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseHistoryDTO;
import com.coocaa.cheese.crm.common.db.entity.ReleaseRescheduleEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 释放改期记录表服务接口
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
public interface IReleaseRescheduleService extends IService<ReleaseRescheduleEntity> {

    /**
     * 延期申请查询详情
     */
    InnerReleaseApproveDetailDTO queryInnerApproveDetail(Integer id);

    /**
     * 申请延期查询商机信息
     */
    List<InnerReleaseApproveTaskPageDTO> queryInnerBusiness(List<Integer> bizIds);

    /**
     * 释放申请查询详情
     */
    List<InnerReleaseHistoryDTO> queryReleaseHistory(Integer id, LocalDateTime createTime, Integer businessId);
}