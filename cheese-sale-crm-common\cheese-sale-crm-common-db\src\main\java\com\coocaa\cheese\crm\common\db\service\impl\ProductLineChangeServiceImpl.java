package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.bean.InnerProductApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineChangePageDTO;
import com.coocaa.cheese.crm.common.db.bean.ProductLineDTO;
import com.coocaa.cheese.crm.common.db.entity.ProductLineChangeEntity;
import com.coocaa.cheese.crm.common.db.mapper.ProductLineChangeMapper;
import com.coocaa.cheese.crm.common.db.service.IProductLineChangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品线变更记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ProductLineChangeServiceImpl
        extends ServiceImpl<ProductLineChangeMapper, ProductLineChangeEntity>
        implements IProductLineChangeService {

    @Override
    public List<ProductLineChangePageDTO> queryInnerProductLineChange(List<Long> bizIds) {
        return getBaseMapper().queryInnerProductLineChange(bizIds);
    }

    @Override
    public InnerProductApproveDetailDTO queryInnerApproveDetail(Long id) {
        return getBaseMapper().queryInnerApproveDetail(id);
    }

    @Override
    public List<ProductLineDTO> queryProductLineBySign(Integer id) {
        return getBaseMapper().queryProductLineBySign(id);
    }
}