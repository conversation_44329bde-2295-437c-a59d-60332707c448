package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 决策态度枚举
 * 支持, 中立, 反对
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Getter
@AllArgsConstructor
public enum DecisionAttitudeEnum implements IEnumType<String> {
    SUPPORT("0070-1", "支持"),
    NEUTRAL("0070-2", "中立"),
    OPPOSE("0070-3", "反对");

    private final String code;
    private final String desc;

    private static final Map<String, DecisionAttitudeEnum> BY_CODE_MAP =
            Arrays.stream(DecisionAttitudeEnum.values())
                    .collect(Collectors.toMap(DecisionAttitudeEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static DecisionAttitudeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static DecisionAttitudeEnum parse(String code, DecisionAttitudeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(DecisionAttitudeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 