package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.BusinessStatusChangeLogEntity;
import com.coocaa.cheese.crm.common.db.mapper.BusinessStatusChangeLogMapper;
import com.coocaa.cheese.crm.common.db.service.IBusinessStatusChangeLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 商机状态变更记录 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessStatusChangeLogServiceImpl
        extends ServiceImpl<BusinessStatusChangeLogMapper, BusinessStatusChangeLogEntity>
        implements IBusinessStatusChangeLogService {

}
