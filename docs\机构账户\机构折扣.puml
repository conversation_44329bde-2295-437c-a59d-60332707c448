@startuml
title 机构折扣计算流程

participant "资金账户管理" as FundService
participant "折扣管理\n/api/crm/discount-rule/*" as DiscountRuleController
participant "折扣计算Service" as CalculateService
database "折扣规则表\nsale_crm_discount_rule" as RuleDB
database "累计消耗折扣表\nsale_crm_discount_cost" as CostDB

== 折扣计算流程 ==

FundService -> DiscountRuleController: POST /api/crm/discount-rule/calculate-discount
note right
请求参数：
- 城市ID
- 累计消费金额
end note

DiscountRuleController -> CalculateService: 调用折扣计算服务

CalculateService -> RuleDB: 查询适用于该城市的有效规则
note right: 查询条件：城市匹配、状态为生效、未删除
RuleDB --> CalculateService: 返回规则列表

alt 未找到适用规则
    CalculateService --> DiscountRuleController: 返回默认折扣1.4
else 找到适用规则
    CalculateService -> CalculateService: 按生效时间排序，取最新规则

    CalculateService -> CostDB: 查询该规则下的消耗折扣区间
    note right: 查询条件：rule_id = 规则ID
    CostDB --> CalculateService: 返回消耗折扣区间列表

    CalculateService -> CalculateService: 查找适用的消耗折扣区间
    note right
    根据消费金额确定适用区间:
    - 如果closed_flag=0，则 cost <= 消费金额
    - 如果closed_flag=1，则 cost < 消费金额
    end note

    alt 未找到适用区间
        CalculateService --> DiscountRuleController: 返回默认折扣1.4
    else 找到适用区间
        CalculateService -> CalculateService: 判断是否有阶梯设置

        alt 有阶梯设置(cost_step>0)
            CalculateService -> CalculateService: 计算阶梯折扣
            note right
            1. 计算超出基准消费的部分：
               超出部分 = 消费金额 - 区间基准金额(cost)

            2. 计算阶梯数：
               阶梯数 = 超出部分 ÷ 阶梯步长(cost_step)

            3. 计算折扣减少值：
               折扣减少值 = 阶梯数 × 折扣系数降低值(discount_decrease)

            4. 计算最终折扣：
               最终折扣 = 基础折扣(discount) - 折扣减少值

            5. 应用折扣下限：
               如果最终折扣 < 折扣下限(discount_limit)且下限>0，
               则最终折扣 = 折扣下限
            end note
        else 无阶梯设置(cost_step=0)
            CalculateService -> CalculateService: 使用区间固定折扣
            note right: 最终折扣 = 基础折扣(discount)
        end
    end
end

CalculateService --> DiscountRuleController: 返回计算结果
DiscountRuleController --> FundService: 返回折扣值

@enduml