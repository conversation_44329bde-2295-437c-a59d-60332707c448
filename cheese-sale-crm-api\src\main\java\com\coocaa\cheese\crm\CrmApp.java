package com.coocaa.cheese.crm;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 销售 CRM 系统
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-22
 */
@EnableAsync
@EnableScheduling
@EnableAspectJAutoProxy
@SpringBootApplication(scanBasePackages = "com.coocaa")
@MapperScan(basePackages = {"com.coocaa.cheese.crm.common.db"})
@EnableFeignClients(basePackages = {"com.coocaa.cheese.crm.rpc", "com.coocaa.ad.translate.rpc"})
public class CrmApp {

    /**
     * 销售CRM启动类
     */
    public static void main(String[] args) {
        System.setProperty("java.util.Arrays.useLegacyMergeSort", "true");
        SpringApplication.run(CrmApp.class, args);
    }
}
