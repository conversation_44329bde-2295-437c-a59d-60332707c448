package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 机构账户表
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@TableName("sale_crm_institution_account")
public class InstitutionAccountEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 账户编号(10位数字+大写英文随机编码)
     */
    private String accountCode;

    /**
     * 企业ID
     */
    private Integer companyId;
    /**
     * 授权开始日期
     */
    private LocalDate authStartDate;

    /**
     * 授权截止日期
     */
    private LocalDate authEndDate;

    /**
     * 分配时间
     */
    private LocalDateTime assignTime;

    /**
     * 账户状态 (字典0109)
     */
    private String accountStatus;

    /**
     * 停用原因 (字典0110)
     */
    private String disableReason;

    /**
     * 上级机构ID
     */
    private Integer parentId;

    /**
     * 业绩归属人ID
     */
    private Integer ownerId;
    /**
     * 归属人部门ID (飞书openId)
     */
    private String ownerDepartmentId;

    /**
     * 删除标记[0:未删除, 1:已删除]
     */
    private Integer deleteFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 最后维护人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 计价方式 (字典0119)
     */
    private String pricingMethod;

    /**
     * 调剂比例
     */
    private Integer adjustmentRatio;
} 