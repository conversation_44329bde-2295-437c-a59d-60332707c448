package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Pk挑战记录表详情VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-30
 */
@Data
@Schema(name = "PkChallengeRecordDetailVO", description = "Pk挑战记录表详情VO")
public class PkChallengeRecordDetailVO {

    @Schema(description = "主体ID")
    private Integer advertisingSubjectId;

    @Schema(description = "预算金额")
    private Integer budgetAmount;

    @Schema(description = "挑战期望")
    @TransField(type = TransTypes.DICT)
    private String challengeExpectation;
    private String challengeExpectationName;

    @Schema(description = "商机ID")
    private Integer businessId;

    @Schema(description = "情况说明", maxLength = 20)
    private String remark;
}
