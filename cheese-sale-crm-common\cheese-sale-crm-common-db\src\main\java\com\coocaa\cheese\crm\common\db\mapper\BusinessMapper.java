package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.cheese.crm.common.db.bean.BusinessDelayDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerProductLineDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseBusinessDetailDTO;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;

import java.time.LocalDate;
import java.util.List;

/**
 * 商机数据表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface BusinessMapper extends BaseMapper<BusinessEntity> {

    /**
     * 发起延期申请查询商机信息
     */
    InnerReleaseBusinessDetailDTO queryBusinessDetailForInner(Integer id);

    /**
     * 产品线变更查询商机信息
     */
    InnerProductLineDetailDTO queryDetailForProductLine(Integer id);

    /**
     * 查询待释放商机
     */
    List<BusinessDelayDTO> queryBusinessToBeReleased(LocalDate threeDaysLater);
}
