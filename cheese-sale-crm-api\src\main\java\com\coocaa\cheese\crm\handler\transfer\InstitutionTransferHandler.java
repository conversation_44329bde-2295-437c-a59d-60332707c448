package com.coocaa.cheese.crm.handler.transfer;

import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.bean.TransferParam;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;
import com.coocaa.cheese.crm.common.db.entity.TransferRecordEntity;
import com.coocaa.cheese.crm.common.db.service.IInstitutionAccountService;
import com.coocaa.cheese.crm.common.db.service.ITransferRecordService;
import com.coocaa.cheese.crm.common.tools.enums.TransferBizTypeEnum;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 机构账户转移处理器
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionTransferHandler implements TransferHandler {

    private final IInstitutionAccountService institutionAccountService;
    private final ITransferRecordService transferRecordService;
    private final FeignAuthorityRpc feignAuthorityRpc;

    @Override
    public TransferBizTypeEnum getTransferBizType() {
        return TransferBizTypeEnum.INSTITUTION;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleTransfer(TransferParam param) {
        // 1. 验证目标归属人
        validateTargetOwner(param);

        // 2. 批量更新机构账户
        List<InstitutionAccountEntity> institutions = institutionAccountService.listByIds(param.getBizIds());
        if (institutions.isEmpty()) {
            throw new BusinessException("机构账户不存在");
        }

        List<TransferRecordEntity> batchSaves = new ArrayList<>();
        for (InstitutionAccountEntity institution : institutions) {
            // 更新机构账户归属信息
            updateInstitutionOwnership(institution, param);
            
            // 创建转移记录
            TransferRecordEntity record = createTransferRecord(param, institution);
            batchSaves.add(record);
        }

        // 3. 批量保存数据
        saveTransferData(institutions, batchSaves);

        return true;
    }

    /**
     * 验证目标归属人
     */
    private void validateTargetOwner(TransferParam param) {
        List<Integer> userIds = feignAuthorityRpc.getSubordinates(param.getTargetOwnerId()).getData();
        if (CollectionUtils.isEmpty(userIds)) {
            throw new BusinessException("目标归属人不存在！");
        }
    }

    /**
     * 更新机构账户归属信息
     */
    private void updateInstitutionOwnership(InstitutionAccountEntity institution, TransferParam param) {
        institution.setOwnerId(param.getTargetOwnerId());
        institution.setOwnerDepartmentId(param.getTargetDepartmentId());
    }

    /**
     * 创建转移记录
     */
    private TransferRecordEntity createTransferRecord(TransferParam param, InstitutionAccountEntity institution) {
        TransferRecordEntity record = new TransferRecordEntity();
        record.setBizId(institution.getId());
        record.setBizType(TransferBizTypeEnum.INSTITUTION.getCode());
        record.setSourceOwnerId(institution.getOwnerId());
        record.setSourceDepartmentId(institution.getOwnerDepartmentId());
        record.setTargetOwnerId(param.getTargetOwnerId());
        record.setTargetDepartmentId(param.getTargetDepartmentId());
        record.setTransferReason(param.getTransferReason());
        record.setDescription(param.getDescription());
        return record;
    }

    /**
     * 批量保存转移数据
     */
    private void saveTransferData(List<InstitutionAccountEntity> institutions, List<TransferRecordEntity> batchSaves) {
        // 批量更新机构账户
        institutionAccountService.updateBatchById(institutions);
        // 批量保存转移记录
        transferRecordService.saveBatch(batchSaves);
    }
} 