package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 签约主体H5查询参数
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
@Accessors(chain = true)
@Schema(name = "AdvertisingSubjectQueryH5Param", description = "签约主体H5查询参数")
public class AdvertisingSubjectQueryH5Param {

    @Schema(description = "品牌ID", type = "Integer", example = "1")
    private Integer brandId;

    @Schema(description = "签约主体名称(模糊查询)", type = "String", example = "XX公司")
    private String companyName;

} 