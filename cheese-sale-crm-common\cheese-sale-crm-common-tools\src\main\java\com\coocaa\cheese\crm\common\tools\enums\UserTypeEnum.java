package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户类型
 * 1:内部用户, 2:外部代理商
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-06
 */
@Getter
@AllArgsConstructor
public enum UserTypeEnum implements IEnumType<Integer> {
    INNER(1, "内部用户"),
    AGENT(2, "外部代理商");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, UserTypeEnum> BY_CODE_MAP =
            Arrays.stream(UserTypeEnum.values())
                    .collect(Collectors.toMap(UserTypeEnum::getCode, item -> item));


    /**
     * 将代码转成枚举
     */
    public static UserTypeEnum parse(Integer code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static UserTypeEnum parse(Integer code, UserTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code) {
        return Optional.ofNullable(parse(code)).map(UserTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}
