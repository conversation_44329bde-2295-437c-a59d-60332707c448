package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.ad.common.core.handler.EncryptHandler;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 联系人数据表
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName(value = "sale_crm_business_contact", autoResultMap = true)
public class BusinessContactEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商机ID
     */
    private Integer businessId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话(加密存储)
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String mobile;

    /**
     * 性别：[1-男;2-女]
     */
    private Integer gender;

    /**
     * 部门
     */
    private String department;

    /**
     * 职务
     */
    private String position;

    /**
     * 决策态度 [支持, 中立, 反对]
     */
    private String decisionAttitude;

    /**
     * 决策影响力 %N
     */
    private Integer decisionInfluence;

    /**
     * 邮箱(加密存储)
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String email;

    /**
     * 公司地址(加密存储)
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String companyAddress;

    /**
     * 生日(月日)
     */
    private String birthdayMonthDay;

    /**
     * 生日(年)
     */
    private String birthdayYear;

    /**
     * 年龄段 [70前, 70后, 80后, 90后, 00后]
     */
    private String ageGroup;

    /**
     * 收件地址(加密存储)
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String address;

    /**
     * 兴趣爱好
     */
    private String hobbies;

    /**
     * 教育信息
     */
    private String education;

    /**
     * 家庭信息
     */
    private String familyInfo;

    /**
     * 人际信息
     */
    private String socialInfo;

    /**
     * 事业信息
     */
    private String careerInfo;

    /**
     * 生活信息
     */
    private String lifeInfo;

    /**
     * 信用价值观信息
     */
    private String creditValues;

    /**
     * 名片文件地址
     */
    private String businessCardUrl;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
