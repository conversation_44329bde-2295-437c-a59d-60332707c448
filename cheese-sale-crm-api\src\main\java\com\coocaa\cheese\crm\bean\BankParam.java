package com.coocaa.cheese.crm.bean;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.EncryptDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 银行管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Data
public class BankParam {
    @NotNull(message = "持有公司不能为空")
    @Schema(description = "持有公司ID", type = "Integer", example = "123")
    private Integer companyId;

    @NotBlank(message = "开户行不能为空")
    @Size(max = 30, message = "开户行不能超过{max}个字符")
    @Schema(description = "开户行", type = "String", example = "招商银行")
    private String name;

    @NotBlank(message = "银行帐号不能为空")
    @Schema(description = "银行帐号", type = "String", example = "001-02")
    @JSONField(deserializeUsing = EncryptDeserializer.class)
    private String account;

    @Schema(description = "帐户类型(字典0078)", type = "String", example = "0078-1")
    private String type;
}
