package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PK挑战记录表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Data
@TableName(value = "sale_crm_pk_challenge_record")
public class PkChallengeRecordEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 签约主体ID
     */
    @TableField(value = "advertising_subject_id")
    private Integer advertisingSubjectId;

    /**
     * 预算金额
     */
    @TableField(value = "budget_amount")
    private Integer budgetAmount;

    /**
     * 挑战发起部门id
     */
    @TableField(value = "challenger_department")
    private String challengerDepartment;

    /**
     * 部门名称
     */
    @TableField(value = "department_name")
    private String departmentName;

    /**
     * 商机ID
     */
    @TableField(value = "business_id")
    private Integer businessId;

    /**
     * 挑战期望
     */
    @TableField(value = "challenge_expectation")
    private String challengeExpectation;

    /**
     * 情况说明
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 执行状态
     */
    @TableField(value = "execute_status")
    private String executeStatus;

    /**
     * 删除标记  [0:否, 1:是]
     */
    @TableField(value = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建人姓名
     */
    @TableField(value = "creator_name")
    private String creatorName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
