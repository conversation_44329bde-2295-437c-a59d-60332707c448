package com.coocaa.cheese.crm.config.aspect;

import com.alibaba.fastjson2.JSONObject;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.cheese.crm.common.tools.util.operatelog.OperateLogDTO;
import com.coocaa.cheese.crm.common.tools.util.operatelog.OperateLogUtils;
import com.coocaa.cheese.crm.config.annotation.OperateLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class OperateLogAspect {
    private static final ExpressionParser EXPRESSION_PARSER = new SpelExpressionParser();


    /**
     * 记录操作日志
     *
     * @param joinPoint  切点
     * @param operateLog 注解
     */
    @After("@annotation(operateLog)")
    public void around(JoinPoint joinPoint, OperateLog operateLog) {
        try {
            // 记录日志
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            EvaluationContext context = new StandardEvaluationContext();
            Object[] args = joinPoint.getArgs();
            String[] paramNames = signature.getParameterNames();
            Map<String, Object> paramMap = new HashMap<>();
            if (ArrayUtils.isEmpty(args) || ArrayUtils.isEmpty(paramNames)) {
                log.warn("记录操作日志失败，参数为空");
                return;
            }
            // 将方法参数绑定到 SpEL 上下文
            for (int i = 0; i < args.length; i++) {
                context.setVariable(paramNames[i], args[i]);
                paramMap.put(paramNames[i], args[i]);
            }
            // 解析 SpEL 表达式
            Integer entityId = EXPRESSION_PARSER.parseExpression(operateLog.entityId()).getValue(context, Integer.class);
            // 记录操作日志
            OperateLogDTO logDTO = OperateLogDTO.builder()
                    .functionName(operateLog.functionName())
                    .entityCode(operateLog.entityCode())
                    .entityId(entityId)
                    .content(JSONObject.toJSONString(paramMap))
                    .operator(UserThreadLocal.getUserId())
                    .build();

            OperateLogUtils.log(logDTO);
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

}