package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 机构账户查询参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构账户查询参数")
public class InstitutionAccountQueryParam {
    @Schema(description = "机构名称id", type = "Integer", example = "12")
    private Integer companyId;

    @Schema(description = "账户状态(字典0109)", type = "String", example = "0109-1")
    private String accountStatus;

    @Schema(description = "所属部门ID", type = "String", example = "dept_123")
    private String ownerDepartmentId;

    @Schema(description = "所属人员ID", type = "Integer", example = "101")
    private Integer ownerId;
} 