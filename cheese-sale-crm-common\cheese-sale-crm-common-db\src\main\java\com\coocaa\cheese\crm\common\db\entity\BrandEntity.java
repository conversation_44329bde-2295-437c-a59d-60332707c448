package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 主品牌信息
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName("sale_comm_brand")
public class BrandEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 母品牌
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer parentId;

    /**
     * 品牌名称: 2-50个字符
     */
    private String name;

    /**
     * 所属行业
     */
    private String industryCode;

    /**
     * 所属公司ID
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer companyId;

    /**
     * 所属公司名称
     */
    @TableField(exist = false)
    private String companyName;

    /**
     * 品牌Logo
     */
    private String logoUrl;

    /**
     * 生效状态
     */
    private Integer effectiveStatus;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 部门id
     */
    private String departmentId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
