package com.coocaa.cheese.crm.bean;

import com.coocaa.ad.common.validation.EnumType;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.BusinessProgressEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 商机管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
@Schema(name = "BusinessWebQueryParam", description = "商机Web查询参数")
public class BusinessWebQueryParam {

    @Schema(description = "品牌ID", type = "Integer", example = "1")
    private Integer brandId;

    @Schema(description = "签约主体ID", type = "Integer", example = "1")
    private Integer advertisingSubjectId;

    @Schema(description = "渠道ID", type = "Integer", example = "1")
    private Integer channelId;

    @Schema(description = "归属人ID", type = "Integer", example = "1")
    private Integer ownerId;

    @Schema(description = "管理部门ID", type = "Integer", example = "1")
    private String departmentId;

    @Schema(description = "商机编码", type = "String", example = "BIZ001")
    private String code;

    @Schema(description = "商机进度(字典0073)", type = "String", example = "0073-1")
    @EnumType(message = "商机进度不正确", value = BusinessProgressEnum.class)
    private String progress;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "创建开始日期", type = "String", example = "2025-01-01")
    private LocalDate createStartDate;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "创建结束日期", type = "String", example = "2025-12-31")
    private LocalDate createEndDate;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "分配开始日期", type = "String", example = "2025-01-01")
    private LocalDate assignStartDate;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "分配结束日期", type = "String", example = "2025-12-31")
    private LocalDate assignEndDate;
} 