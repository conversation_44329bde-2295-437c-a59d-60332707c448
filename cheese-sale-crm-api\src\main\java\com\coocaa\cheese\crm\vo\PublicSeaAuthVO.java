
package com.coocaa.cheese.crm.vo;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 公海打捞授权表VO
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "PublicSeaAuthVO", description = "公海打捞授权表VO")
public class PublicSeaAuthVO {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "业务ID")
    private Integer bizId;

    @Schema(description = "情况说明", maxLength = 100)
    private String remark;

    @Schema(description = "执行状态(字典0151)", maxLength = 10)
    private String executeStatus;

    @Schema(description = "删除标记  [0:否, 1:是]", maxLength = 1)
    private Boolean deleteFlag;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "操作人")
    private Integer operator;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;
}