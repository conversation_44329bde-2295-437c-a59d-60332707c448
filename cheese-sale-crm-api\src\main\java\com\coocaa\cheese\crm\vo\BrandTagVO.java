package com.coocaa.cheese.crm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 品牌标签信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BrandTagVO", description = "品牌标签信息VO")
public class BrandTagVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "品牌名称", type = "String", example = "可口可乐")
    private String name;
}