package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 机构资金账户视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构资金账户视图对象")
public class InstitutionFundAccountVO {
    @Schema(description = "账户ID")
    private Integer id;

    @Schema(description = "机构账户ID")
    private Integer institutionId;

    @Schema(description = "账户余额")
    private BigDecimal balance;

    @Schema(description = "冻结金额")
    private BigDecimal totalFrozen;

    @Schema(description = "累计现金充值")
    private BigDecimal totalCashRecharge;

    @Schema(description = "累计非现金充值")
    private BigDecimal totalNonCashRecharge;

    @Schema(description = "累计补偿金额")
    private BigDecimal totalCompensation;

    @Schema(description = "累计消费金额")
    private BigDecimal totalConsumption;

    @Schema(description = "累计退款金额")
    private BigDecimal totalRefund;

    @Schema(description = "累计取消金额")
    private BigDecimal totalCancel;

    @Schema(description = "账户状态(字典0109)")
    @TransField(type = TransTypes.DICT)
    private String accountStatus;
    private String accountStatusName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 