package com.coocaa.cheese.crm.rpc;

import com.coocaa.ad.common.config.FeignConfig;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.rpc.bean.ApprovalInitiateParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveApplyPageQueryParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveDealCountQueryParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveOpinionParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveTaskPageQueryParam;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveNodeVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveDealCountVO;
import com.coocaa.cheese.crm.rpc.vo.InnerInstanceTaskVO;
import com.coocaa.cheese.crm.rpc.vo.InnerTaskOperateVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 站内审批系统接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@FeignClient(
//        name = "cheese-authority-api",
        value = "cheese-authority-api", path = "/sys/approve/internal",
        //url = "http://beta-cheese-ssp.coocaa.com",
        configuration = FeignConfig.class)
public interface FeignInnerApprovalRpc {

    /**
     * 发起审批
     */
    @PostMapping("/instance")
    ResultTemplate<String> initiateApproval(@RequestBody ApprovalInitiateParam param);

    /**
     * 查询审批实例节点
     */
    @GetMapping("/instance/node")
    ResultTemplate<List<InnerApproveNodeVO>> queryNodes(@RequestParam String instanceCode);

    /**
     * 用户审批任务列表(分页)
     */
    @PostMapping("/user/task")
    ResultTemplate<PageResponseVO<?>> queryTaskPage(@RequestBody PageRequestVO<InnerApproveTaskPageQueryParam> page);

    /**
     * 用户申请审批列表(分页)
     */
    @PostMapping("/user/instance")
    ResultTemplate<PageResponseVO<?>> queryApplyPage(@RequestBody PageRequestVO<InnerApproveApplyPageQueryParam> page);

    /**
     * 同意审批
     */
    @PostMapping("/task/approve")
    ResultTemplate<InnerTaskOperateVO> agreeApproval(@RequestBody InnerApproveOpinionParam param);

    /**
     * 拒绝审批
     */
    @PostMapping("/task/reject")
    ResultTemplate<InnerTaskOperateVO> rejectApproval(@RequestBody InnerApproveOpinionParam param);

    /**
     * 查询审批实例待完成的任务
     */
    @GetMapping("/instance/task")
    ResultTemplate<InnerInstanceTaskVO> queryTask(@RequestParam String instanceCode);

    /**
     * 查询审批单详情
     */
    @GetMapping("/instance/detail")
    ResultTemplate<InnerApproveInstanceVO> queryDetail(@RequestParam String instanceCode);

    /**
     * 查询审批-用户待办统计
     */
    @PostMapping("/deal-count")
    ResultTemplate<List<InnerApproveDealCountVO>> queryDealCount(@RequestBody InnerApproveDealCountQueryParam param);

    /**
     * 批量查询数据接口（洗数据用,后续版本删除）
     */
    @PostMapping("/history/instance")
    ResultTemplate<List<InnerApproveInstanceVO>> queryHistory(@RequestBody List<Integer> ruleCodes);
} 