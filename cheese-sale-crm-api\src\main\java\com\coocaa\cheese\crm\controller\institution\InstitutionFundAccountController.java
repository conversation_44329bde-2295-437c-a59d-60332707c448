package com.coocaa.cheese.crm.controller.institution;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.InstitutionFundChangeParam;
import com.coocaa.cheese.crm.bean.InstitutionFundFreezeParam;
import com.coocaa.cheese.crm.bean.InstitutionFundQueryParam;
import com.coocaa.cheese.crm.bean.ListingFeeQuoteParam;
import com.coocaa.cheese.crm.bean.PlanFreezeParam;
import com.coocaa.cheese.crm.bean.PlanUnfreezeParam;
import com.coocaa.cheese.crm.common.db.entity.InstitutionFundAccountEntity;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.InstitutionFundAccountService;
import com.coocaa.cheese.crm.vo.InstitutionFundAccountVO;
import com.coocaa.cheese.crm.vo.InstitutionFundChangeRecordVO;
import com.coocaa.cheese.crm.vo.ListingFeeQuoteVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机构资金账户管理
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@RestController
@RequestMapping("/institution-fund-accounts")
@Tag(name = "机构资金账户管理", description = "机构资金账户管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionFundAccountController extends BaseController {
    private final InstitutionFundAccountService institutionFundAccountService;

    /**
     * 获取机构资金账户
     */
    @Operation(summary = "获取机构资金账户")
    @Parameter(name = "institutionCode", description = "机构账户ID", required = true, in = ParameterIn.QUERY, schema = @Schema(type = "int"))
    @GetMapping
    public ResultTemplate<InstitutionFundAccountVO> getInstitutionFundAccount(@RequestParam(value = "institutionCode", required = false) String institutionCode) {
        return ResultTemplate.success(institutionFundAccountService.getInstitutionFundAccount(institutionCode));
    }

    /**
     * 分页查询资金变动记录
     */
    @Operation(summary = "分页查询资金变动记录")
    @PostMapping("/page-list")
    public ResultTemplate<PageResponseVO<InstitutionFundChangeRecordVO>> pageListFundChangeRecord(@RequestBody PageRequestVO<InstitutionFundQueryParam> pageRequest) {
        return ResultTemplate.success(institutionFundAccountService.pageListFundChangeRecord(pageRequest));
    }

    /**
     * 资金操作（增加、减少、冻结）
     */
    @Operation(summary = "资金操作")
    @PostMapping("/fundAccount/change")
    public ResultTemplate<Boolean> changeFund(@RequestBody @Validated InstitutionFundChangeParam param) {
        // 查询资金账户
        InstitutionFundAccountEntity fundAccount = institutionFundAccountService.getFundAccountByInstitutionCode(param.getFundAccountCode());
        return ResultTemplate.success(institutionFundAccountService.changeFund(param, fundAccount));
    }

    /**
     * 处理冻结资金
     */
    @Operation(summary = "处理冻结资金")
    @Parameter(name = "id", description = "冻结记录ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @PostMapping("/frozen/{id}/process")
    public ResultTemplate<Boolean> processFrozenFund(
            @PathVariable("id") Integer id,
            @RequestBody @Validated InstitutionFundFreezeParam param) {
        // 查询资金账户
        InstitutionFundAccountEntity fundAccount = institutionFundAccountService.getFundAccountByInstitutionCode(param.getFundAccountCode());
        param.setFundAccountId(fundAccount.getId());
        return ResultTemplate.success(institutionFundAccountService.processFrozenFund(id, param));
    }

    /**
     * 供售卖平台的投放方案【状态】转销售前调用
     * 计算刊例费 + 冻结资金
     */
    @Operation(summary = "方案冻结")
    @PostMapping("/plan-freeze")
    public ResultTemplate<Boolean> planFreeze(@RequestBody @Validated PlanFreezeParam param) {
        InstitutionFundAccountEntity fundAccount = institutionFundAccountService.getFundAccountByInstitutionId(param.getInstitutionId(), true);
        return ResultTemplate.success(institutionFundAccountService.planFreeze(param, fundAccount));
    }

    /**
     * 供售卖平台的投放方案【状态】转销售取消时调用
     * 解冻金额  并且删除对应的刊例明细
     */
    @Operation(summary = "方案解冻")
    @PostMapping("/plan-unfreeze")
    public ResultTemplate<Boolean> planUnfreeze(@RequestBody @Validated PlanUnfreezeParam param) {
        InstitutionFundAccountEntity fundAccount = institutionFundAccountService.getFundAccountByInstitutionId(param.getInstitutionId(), false);
        return ResultTemplate.success(institutionFundAccountService.planUnfreeze(param, fundAccount));
    }


    /**
     * 分页查询刊例账单
     */
    @Operation(summary = "分页查询刊例账单")
    @PostMapping("/listing-fee/page-list")
    public ResultTemplate<PageResponseVO<ListingFeeQuoteVO>> pageListListingFee(@RequestBody PageRequestVO<ListingFeeQuoteParam> pageRequest) {
        return ResultTemplate.success(institutionFundAccountService.pageListListingFee(pageRequest));
    }

    /**
     * 获取刊例账单
     */
    @Operation(summary = "刊例费下载")
    @GetMapping("/listing-fee/download")
    public ResultTemplate<String> downloadListingFee(@RequestParam("cityId") Integer cityId, @RequestParam("planId") Integer planId) {
        return ResultTemplate.success(institutionFundAccountService.downloadListingFee(planId, cityId));
    }
} 