package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 主体报备H5参数
 *
 * <AUTHOR>
 * @since 2025/4/29
 */

@Data
public class AdvertisingSubjectReportH5Param {
    @Schema(description = "主体id", type = "Integer", example = "1")
    private Integer id;

    @NotBlank(message = "签约主体类型不能为空")
    @Schema(description = "签约主体类型(字典0068)", type = "String", example = "0068-6")
    private String type;

    @NotNull(message = "所属公司ID不能为空")
    @Schema(description = "所属公司ID", type = "Integer", example = "1")
    private Integer companyId;

    @Schema(description = "是否TOP客户 [0:否, 1:是]", type = "Integer", example = "0")
    private Integer topFlag;

    @Size(max = 20, message = "备注说明不能超过{max}个字符")
    @Schema(description = "备注说明", type = "String", example = "重点客户签约主体")
    private String description;
}
