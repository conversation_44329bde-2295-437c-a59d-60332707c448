package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.PkChallengeRecordEntity;
import com.coocaa.cheese.crm.common.db.mapper.PkChallengeRecordMapper;
import com.coocaa.cheese.crm.common.db.service.IPkChallengeRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * PK挑战记录服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PkChallengeRecordServiceImpl
        extends ServiceImpl<PkChallengeRecordMapper, PkChallengeRecordEntity>
        implements IPkChallengeRecordService {

}
