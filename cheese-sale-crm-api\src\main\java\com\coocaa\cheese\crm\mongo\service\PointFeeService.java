package com.coocaa.cheese.crm.mongo.service;

import com.coocaa.cheese.crm.mongo.PointFeeDocument;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 点位费用服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PointFeeService {
    private final MongoTemplate mongoTemplate;

    /**
     * 查询点位费用信息
     *
     * @param cityId  城市ID
     * @param planId 方案ID
     * @return 点位费用信息
     */
    public List<PointFeeDocument> findPointFee(Integer cityId, Integer planId) {
        Query query = new Query(Criteria.where("planId").is(planId)
                .and("cityId").is(cityId));
        return mongoTemplate.find(query, PointFeeDocument.class);
    }

    /**
     * 批量保存点位费用信息
     *
     * @param pointFees 点位费用信息列表
     */
    public void savePointFees(List<PointFeeDocument> pointFees) {
        mongoTemplate.insert(pointFees, PointFeeDocument.class);
    }
} 