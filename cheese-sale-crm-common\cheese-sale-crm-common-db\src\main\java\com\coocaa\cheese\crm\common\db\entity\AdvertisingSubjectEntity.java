package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 签约主体
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName("sale_comm_advertising_subject")
public class AdvertisingSubjectEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 签约主体类型(字典0068)
     */
    private String type;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 品牌名称
     */
    @TableField(exist = false)
    private String brandName;

    /**
     * 所属公司ID
     */
    private Integer companyId;

    /**
     * 所属公司名称
     */
    @TableField(exist = false)
    private String companyName;

    /**
     * 是否TOP客户 [0:否, 1:是]
     */
    private Integer topFlag;

    /**
     * 证明材料
     */
    private String evidenceUrl;

    /**
     * 生效状态
     */
    private Integer effectiveStatus;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 是否公海保护期 [0:否, 1:是]
     */
    private Integer protectionPeriodFlag;

    /**
     * 备注说明
     */
    private String description;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /*
     * 创建人姓名
     */
    @TableField(fill = FieldFill.INSERT)
    private String creatorName;

    /**
     * 部门id
     */
    private String departmentId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
