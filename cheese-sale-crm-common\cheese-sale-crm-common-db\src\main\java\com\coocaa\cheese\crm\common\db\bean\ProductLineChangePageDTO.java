
package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 产品线变更记录表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
public class ProductLineChangePageDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 签约主体对应公司id
     */
    private Integer companyId;

    /**
     * 变更前产品线
     */
    private String productLine;
}