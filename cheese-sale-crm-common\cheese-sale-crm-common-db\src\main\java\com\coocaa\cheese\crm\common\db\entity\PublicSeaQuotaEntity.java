
package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("sale_comm_public_sea_quota")
public class PublicSeaQuotaEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 每月配额
     */
    private Integer monthlyQuota;
    /**
     * 每日配额
     */
    private Integer dailyQuota;

    /**
     * 临时配额
     */
    private Integer temporaryQuota;

    /**
     * 本月已用
     */
    private Integer monthlyUsed;

    /**
     * 本日已用
     */
    private Integer dailyUsed;

    /**
     * 临额已用
     */
    private Integer tempUsed;

    /**
     * 授权天数
     */
    private Integer authDay;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 操作人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 删除标志位
     */
    private Integer deleteFlag;
}
