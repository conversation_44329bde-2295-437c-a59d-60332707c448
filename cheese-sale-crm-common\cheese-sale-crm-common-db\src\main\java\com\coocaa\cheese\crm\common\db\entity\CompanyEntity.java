package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.ad.common.core.handler.EncryptHandler;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 公司信息
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName(value = "sale_comm_company", autoResultMap = true)
public class CompanyEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 公司名称: 2-50个字符
     */
    private String name;

    /**
     * 公司简称
     */
    private String nickName;

    /**
     * 统一社会信用代码, 需要加密存储
     */
    @TableField(typeHandler = EncryptHandler.class)
    private String code;

    /**
     * 营业执照附件地址
     */
    private String licenseUrl;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
