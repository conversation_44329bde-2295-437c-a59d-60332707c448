package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 附件参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "附件参数")
public class AttachmentParam {
    @Schema(description = "文件名称", type = "String", example = "资质证书.pdf")
    private String name;

    @Schema(description = "文件大小(KB)", type = "Long", example = "1024")
    private Long size;

    @Schema(description = "文件类型", type = "String", example = "pdf")
    private String type;

    @Schema(description = "文件URL", type = "String", example = "https://example.com/files/certificate.pdf")
    private String url;
} 