CREATE TABLE `sale_crm_discount_cost`
(
    `id`                INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `rule_id`           INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机构折扣规则ID',
    `cost`              DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '当前累计消耗金额(元)',
    `closed_flag`       TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '区间标记[0:开, 1:闭, 2:正无穷]',
    `discount`          DECIMAL(5, 3)    NOT NULL DEFAULT '1.000' COMMENT '折扣系数',
    `cost_step`         DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '增加累计消耗金额(元)',
    `discount_decrease` DECIMAL(5, 3)    NOT NULL DEFAULT '0.000' COMMENT '折扣系数降低',
    `discount_limit`    DECIMAL(5, 3)    NOT NULL DEFAULT '0.000' COMMENT '不超过的系数',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='折扣累计消耗金额表';



CREATE TABLE `sale_crm_discount_rule`
(
    `id`             INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`           VARCHAR(30)      NOT NULL DEFAULT '' COMMENT '折扣策略名称',
    `code`           VARCHAR(15)      NOT NULL DEFAULT '' COMMENT '折扣策略编码',
    `status`         VARCHAR(10)      NOT NULL DEFAULT '0081-1' COMMENT '策略状态 (字典0081)',
    `city_ids`       VARCHAR(1024)    NOT NULL DEFAULT '' COMMENT '适用城市(多个id拼接)',
    `creator`        INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_time`    DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`       INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `update_time`    DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_flag`    TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '删除标记[0:未删除, 1:已删除]',
    `effective_time` DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='机构折扣规则表';



CREATE TABLE `sale_crm_institution_account`
(
    `id`                  int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `account_code`        varchar(10) NOT NULL DEFAULT '' COMMENT '账户编号(10位数字+大写英文随机编码)',
    `company_id`          int(11) unsigned NOT NULL DEFAULT '0' COMMENT '企业ID',
    `auth_start_date`     date                 DEFAULT NULL COMMENT '授权开始日期',
    `auth_end_date`       date                 DEFAULT NULL COMMENT '授权截止日期',
    `assign_time`         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    `pricing_method`      varchar(10) NOT NULL DEFAULT '0119-1' COMMENT '计价方式 (字典0119)',
    `adjustment_ratio`    smallint(3) unsigned NOT NULL DEFAULT '0' COMMENT '调剂比例',
    `account_status`      varchar(10) NOT NULL DEFAULT '0109-1' COMMENT '账户状态 (字典0109)',
    `disable_reason`      varchar(10) NOT NULL DEFAULT '0110-1' COMMENT '停用原因 (字典0110)',
    `parent_id`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上级机构ID',
    `owner_id`            int(11) unsigned NOT NULL DEFAULT '0' COMMENT '业绩归属人ID',
    `owner_department_id` varchar(64) NOT NULL DEFAULT '' COMMENT '归属人部门ID (飞书openId)',
    `creator`             int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `operator`            int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后维护人ID',
    `create_time`         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_flag`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记[0:未删除, 1:已删除]',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='机构账户表';

CREATE TABLE `sale_crm_institution_transfer_record`
(
    `id`                   int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `institution_id`       int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '机构账户ID',
    `source_owner_id`      int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '来源归属人ID',
    `source_department_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '来源管理部门ID',
    `target_owner_id`      int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标归属人ID',
    `target_department_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标管理部门ID',
    `transfer_reason`      varchar(10) NOT NULL DEFAULT '0075-1' COMMENT '转移原因 (0075)',
    `description`          varchar(20) NOT NULL DEFAULT '' COMMENT '备注说明',
    `creator`              int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `create_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`             int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作人ID',
    `update_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_flag`          tinyint(1)       NOT NULL DEFAULT '0' COMMENT '删除标记[0:未删除, 1:已删除]',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='机构账户转移记录表';

CREATE TABLE `sale_crm_institution_contract`
(
    `id`             INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `institution_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机构账户ID',
    `contract_code`  VARCHAR(30)      NOT NULL DEFAULT '' COMMENT '合同编号',
    `description`    VARCHAR(50)      NULL     DEFAULT '' COMMENT '备注说明',
    `effective_flag` TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '当前生效[0:否, 1:是]',
    `sign_date`      DATE             NOT NULL DEFAULT '1970-01-01' COMMENT '签约日期',
    `file_url`       VARCHAR(255)     NOT NULL DEFAULT '' COMMENT '文件附件地址',
    `creator`        INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_time`    DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`       INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `update_time`    DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_flag`    TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '删除标记[0:未删除, 1:已删除]',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='机构合同表';



CREATE TABLE `sale_crm_institution_fund_account`
(
    `id`                      INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `institution_id`          INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机构账户ID',
    `balance`                 DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '账户余额',
    `total_frozen`            DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '累计冻结',
    `total_cash_recharge`     DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '累计现金充值',
    `total_non_cash_recharge` DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '累计非现金充值',
    `total_compensation`      DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '累计补偿',
    `total_consumption`       DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '累计消耗',
    `total_refund`            DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '累计退款',
    `total_cancel`            DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '累计作废',
    `account_status`          VARCHAR(10)      NOT NULL DEFAULT '0109-1' COMMENT '账户状态 (字典0109)',
    `creator`                 INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_time`             DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`                INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `update_time`             DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_flag`             TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '删除标记[0:未删除, 1:已删除]',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='机构资金账户表';



CREATE TABLE `sale_crm_institution_fund_change_record`
(
    `id`                     INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `fund_account_id`        INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '资金账户ID',
    `change_amount`          DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '变动金额',
    `change_type`            VARCHAR(10)      NOT NULL DEFAULT '0111-1' COMMENT '变动类型 (0111)',
    `change_reason`          VARCHAR(10)      NOT NULL DEFAULT '0112-1' COMMENT '变动原因 (0112)',
    `voucher_url`            VARCHAR(255)              DEFAULT '' COMMENT '变动凭据URL',
    `adjustment_voucher_url` VARCHAR(255)              DEFAULT '' COMMENT '变动调整凭据URL',
    `plan_id`                INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '投放方案ID',
    `creation_method`        TINYINT(1) UNSIGNED       DEFAULT '0' COMMENT '创建方式[0:系统, 1:人工]',
    `change_time`            DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变动时间',
    `description`            VARCHAR(100)     NOT NULL DEFAULT '' COMMENT '变动备注',
    `creator`                INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_time`            DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`               INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `update_time`            DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_flag`            TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '删除标记[0:未删除, 1:已删除]',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='机构资金变动记录表';


CREATE TABLE `sale_crm_institution_qualification`
(
    `id`                 INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `institution_id`     INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '机构账户ID',
    `qualification_name` VARCHAR(30)      NOT NULL DEFAULT '' COMMENT '资质名称',
    `description`        VARCHAR(50)      NOT NULL DEFAULT '' COMMENT '备注说明',
    `file_url`           VARCHAR(255)     NOT NULL DEFAULT '' COMMENT '文件附件地址',
    `creator`            INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_time`        DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`           INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `update_time`        DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_flag`        TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '删除标记[0:未删除, 1:已删除]',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='机构资质表';



CREATE TABLE `sale_crm_listing_fee_quote`
(
    `id`                    INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `fund_change_record_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '资金变动记录ID',
    `city_id`               INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '投放城市id',
    `city_name`             VARCHAR(20)      NOT NULL DEFAULT '' COMMENT '投放城市名称',
    `billing_type`          VARCHAR(10)      NOT NULL DEFAULT '0087-1' COMMENT '计费方式 (0087)',
    `point_count`           INT(11)          NOT NULL DEFAULT '0' COMMENT '点位数量',
    `billing_point_count`   INT(11)          NOT NULL DEFAULT '0' COMMENT '计费点位数量',
    `point_unit_price`      DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '点位单价',
    `billing_cycle`         INT(11)          NOT NULL DEFAULT '0' COMMENT '计费周期',
    `applicable_discount`   DECIMAL(5, 3)    NOT NULL DEFAULT '0.000' COMMENT '适用折扣',
    `pre_discount_total`    DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '折前总价',
    `post_discount_total`   DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '折后总价',
    `actual_amount`         DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '实际发生',
    `occupy_amount`         DECIMAL(12, 2)   NOT NULL DEFAULT '0.00' COMMENT '占位费用',
    `creator`               INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_time`           DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`              INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `update_time`           DATETIME         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_flag`           TINYINT(1)       NOT NULL DEFAULT '0' COMMENT '删除标记[0:未删除, 1:已删除]',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='刊例费报价表';



CREATE TABLE `sale_crm_transfer_record`
(
    `id`                   INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `biz_type`             VARCHAR(10)      NOT NULL DEFAULT '0113-1' COMMENT '业务类型 (字典0113)',
    `biz_id`               INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '业务ID（商机ID或机构账户ID）',
    `source_owner_id`      INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '来源归属人',
    `source_department_id` VARCHAR(64)               DEFAULT '' COMMENT '来源管理部门',
    `target_owner_id`      INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '目标归属人',
    `target_department_id` VARCHAR(64)               DEFAULT '' COMMENT '目标管理部门',
    `transfer_reason`      VARCHAR(10)      NOT NULL DEFAULT '0075-1' COMMENT '转移原因 (字典0075)',
    `description`          VARCHAR(200)              DEFAULT '' COMMENT '备注说明',
    `delete_flag`          TINYINT(1)                DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`              INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`          DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`             INT(11) UNSIGNED          DEFAULT '0' COMMENT '操作人',
    `update_time`          DATETIME                  DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='数据转移记录表';

-- 公司增加营业执照
ALTER TABLE `sale_comm_company`
    ADD COLUMN `license_url` VARCHAR(200) NULL COMMENT '营业执照附件URL' AFTER `code`;


