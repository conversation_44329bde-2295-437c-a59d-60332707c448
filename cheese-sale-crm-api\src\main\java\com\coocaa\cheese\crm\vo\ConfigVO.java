package com.coocaa.cheese.crm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 配置信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-02
 */
@Data
@Accessors(chain = true)
@Schema(name = "ConfigVO", description = "配置信息VO")
public class ConfigVO {
    @NotBlank(message = "名称不能为空")
    @Schema(description = "名称", type = "String", example = "自动审核")
    private String name;

    @NotBlank(message = "编码不能为空")
    @Schema(description = "编码", type = "String", example = "auto_audit")
    private String code;

    @NotBlank(message = "父编码不能为空")
    @Schema(description = "编码", type = "String", example = "city")
    private String parentCode;

    @NotBlank(message = "配置值不能为空")
    @Schema(description = "配置值", type = "String", example = "1")
    private String value;

    @Schema(description = "扩展数据1", type = "String", example = "1")
    private String ext1;

    @Schema(description = "扩展数据3", type = "String", example = "1")
    private String ext2;

    @Schema(description = "扩展数据3", type = "String", example = "1")
    private String ext3;

    @Schema(description = "描述", type = "String", example = "1")
    private String description;

    @Schema(description = "配置状态 [0:禁用, 1:启用]", type = "Integer", example = "1")
    private Integer status;
}
