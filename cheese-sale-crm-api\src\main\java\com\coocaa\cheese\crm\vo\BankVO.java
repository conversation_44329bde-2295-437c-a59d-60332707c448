package com.coocaa.cheese.crm.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.DesensitizeSerializer;
import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 银行帐户
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Data
@Accessors(chain = true)
@Schema(name = "BankVO", description = "银行帐户VO")
public class BankVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "开户行", type = "String", example = "招商银行")
    private String name;

    @JSONField(serializeUsing = DesensitizeSerializer.class)
    @Schema(description = "银行帐号", type = "String", example = "xxx")
    private String account;

    @TransField(type = TransTypes.COMPANY, target = "companyName")
    @Schema(description = "所属公司ID", type = "Integer", example = "1")
    private Integer companyId;
    private String companyName;

    @TransField(type = TransTypes.DICT)
    @Schema(description = "帐户类型(字典0078)", type = "String", example = "0078-01")
    private String type;
    private String typeName;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @TransField(type = TransTypes.USER)
    @Schema(description = "操作人", type = "Integer", example = "1")
    private Integer operator;

    @Schema(description = "操作人", type = "Integer", example = "1")
    private String operatorName;
}
