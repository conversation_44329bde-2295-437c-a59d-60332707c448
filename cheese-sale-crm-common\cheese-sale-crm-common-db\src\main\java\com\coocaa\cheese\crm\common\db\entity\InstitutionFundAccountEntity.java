package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 机构资金账户表
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@TableName("sale_crm_institution_fund_account")
public class InstitutionFundAccountEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构账户ID
     */
    private Integer institutionId;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 冻结金额
     */
    private BigDecimal totalFrozen;

    /**
     * 累计现金充值
     */
    private BigDecimal totalCashRecharge;

    /**
     * 累计非现金充值
     */
    private BigDecimal totalNonCashRecharge;

    /**
     * 累计补偿金额
     */
    private BigDecimal totalCompensation;

    /**
     * 累计消费金额
     */
    private BigDecimal totalConsumption;

    /**
     * 累计退款金额
     */
    private BigDecimal totalRefund;

    /**
     * 累计取消金额
     */
    private BigDecimal totalCancel;

    /**
     * 账户状态 (字典0109)
     */
    private String accountStatus;

    /**
     * 删除标记[0:未删除, 1:已删除]
     */
    private Integer deleteFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

} 