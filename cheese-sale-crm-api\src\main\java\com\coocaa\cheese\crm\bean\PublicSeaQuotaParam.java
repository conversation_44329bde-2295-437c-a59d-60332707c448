package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/30
 */
@Data
@Schema(description = "公海配额参数")
public class PublicSeaQuotaParam {

    @Schema(description = "用户id", type = "Integer", example = "1")
    @NotNull(message = "用户id不能为空")
    private Integer userId;

    @Schema(description = "用户名称", type = "String", example = "张三")
    @NotNull(message = "用户名称不能为空")
    private String userName;

    @Schema(description = "每月配额", type = "Integer", example = "10")
    @NotNull(message = "每月配额不能为空")
    private Integer monthlyQuota;

    @Schema(description = "每日配额", type = "Integer", example = "10")
    @NotNull(message = "每日配额不能为空")
    private Integer dailyQuota;

    @Schema(description = "临时配额", type = "Integer", example = "10")
    private Integer temporaryQuota;

    @Schema(description = "授权天数", type = "Integer", example = "10")
    private Integer authDay;

}
