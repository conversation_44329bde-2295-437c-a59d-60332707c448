package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 站内审批pk申请列表分页返回参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Schema(name = "InnerApprovePkChallengePageVO", description = "站内审批pk申请列表分页返回参数")
public class InnerApprovePkChallengePageVO extends BaseInnerApproveVO {

    @Schema(description = "实际业务id")
    private Integer bizId;

    @Schema(description = "品牌")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "签约主体名称")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;

    @Schema(description = "产品线", type = "String", example = "1")
    private String productLineNames;

}
