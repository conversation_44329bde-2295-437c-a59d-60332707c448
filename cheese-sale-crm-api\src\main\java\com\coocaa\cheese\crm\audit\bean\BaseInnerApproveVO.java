package com.coocaa.cheese.crm.audit.bean;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 站内审批任务列表分页返回参数
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@Accessors(chain = true)
@Schema(name = "BaseInnerApproveVO", description = "站内审批任务列表分页返回参数")
public class BaseInnerApproveVO {

    @Schema(description = "审批业务id")
    private Long id;

    @Schema(description = "审批任务名称")
    private String approvalName;

    @Schema(description = "申请人")
    @TransField(type = TransTypes.USER)
    private Integer instanceUserId;
    private String instanceUserName;

    @Schema(description = "申请部门")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime instanceCreateTime;

    @Schema(description = "节点开始时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime startTime;

    @Schema(description = "节点完成时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime endTime;

    @Schema(description = "审批结果，字典(0138)")
    @TransField(type = TransTypes.DICT)
    private String approvalResult;
    private String approvalResultName;

    @Schema(description = "审批意见")
    private String comment;
}
