package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 机构账户视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构账户视图对象")
public class InstitutionAccountVO {
    @Schema(description = "机构账户ID")
    private Integer id;

    @Schema(description = "机构账户编码")
    private String accountCode;

    @Schema(description = "公司ID")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;

    @Schema(description = "账户状态(字典0109)")
    @TransField(type = TransTypes.DICT)
    private String accountStatus;
    private String accountStatusName;

    @Schema(description = "停用原因(字典0110)")
    @TransField(type = TransTypes.DICT)
    private String disableReason;
    private String disableReasonName;

    @Schema(description = "上级机构ID")
    @TransField(type = TransTypes.COMPANY)
    private Integer parentId;
    private String parentName;

    @Schema(description = "归属人ID")
    @TransField(type = TransTypes.USER)
    private Integer ownerId;
    private String ownerName;

    @Schema(description = "管理部门ID")
    @TransField(type = TransTypes.DEPARTMENT)
    private String ownerDepartmentId;
    private String ownerDepartmentName;

    @Schema(description = "分配时间")
    private LocalDateTime assignTime;

    @Schema(description = "授权开始日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate authStartDate;

    @Schema(description = "授权截止日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate authEndDate;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "计价方式")
    @TransField(type = TransTypes.DICT)
    private String pricingMethod;
    private String pricingMethodName;

    @Schema(description = "调剂比例")
    private Integer adjustmentRatio;
} 