package com.coocaa.cheese.crm.common.tools.util.operatelog;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.function.BiConsumer;

/**
 * 操作日志工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04
 */
@Slf4j
public final class OperateLogUtils {
    private static final ObjectMapper JSON_MAPPER = new ObjectMapper();
    private volatile KafkaProducer<String, String> kafkaProducer;
    private static String topicName = "cheese-operate-log";
    private static Properties queueConfig = null;


    /**
     * 记录操作日志
     *
     * @param operateLog 操作日志
     * @return true:调用成功
     */
    public static boolean log(IOperateLog operateLog) {
        return log(operateLog, null);
    }

    /**
     * 记录操作日志
     *
     * @param operateLog 操作日志
     * @param callback   回调函数
     * @return true:调用成功
     */
    public static boolean log(IOperateLog operateLog, BiConsumer<RecordMetadata, Exception> callback) {
        if (Objects.isNull(operateLog) || !isInvalid(operateLog)) {
            return false;
        }

        try {
            ProducerRecord<String, String> record = new ProducerRecord<>(topicName, operateLog.getSystemCode(),
                    JSON_MAPPER.writeValueAsString(operateLog));
            Optional.ofNullable(instance().getKafkaProducer()).ifPresent((producer) -> {
                log.info("记录合同系统操作日志: {}", record.value());
                if (Objects.isNull(callback)) {
                    producer.send(record);
                } else {
                    producer.send(record, callback::accept);
                }
            });
            return true;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * 设置队列配置
     */
    static void setQueueConfig(Properties queueConfig, String topicName) {
        OperateLogUtils.queueConfig = queueConfig;
        OperateLogUtils.topicName = topicName;
    }

    private OperateLogUtils() {
    }

    private static OperateLogUtils instance() {
        return InstanceHolder.INSTANCE;
    }

    private KafkaProducer<String, String> getKafkaProducer() {
        if (Objects.isNull(this.kafkaProducer)) {
            synchronized (this) {
                if (Objects.isNull(this.kafkaProducer)) {
                    if (Objects.isNull(queueConfig) || !queueConfig.containsKey("bootstrap.servers")) {
                        log.error("[操作日志] - 读取消息队列配置失败！");
                        return null;
                    }
                    this.kafkaProducer = new KafkaProducer<>(queueConfig);
                }
            }
        }

        return this.kafkaProducer;
    }

    /**
     * 检查操作日志是否有效
     */
    private static boolean isInvalid(IOperateLog bizLog) {
        if (StringUtils.isBlank(bizLog.getSystemCode())
                || StringUtils.isBlank(bizLog.getFunctionName())
                || StringUtils.isBlank(bizLog.getEntityCode())
                || Objects.isNull(bizLog.getEntityId())) {
            log.warn("[操作日志] - 系统名称/功能名称/实体类型/实体ID 不能为空！ 详情: {}", bizLog);
            return false;
        }

        return true;
    }

    /**
     * 静态内部类实现单例
     */
    private static final class InstanceHolder {
        public static final OperateLogUtils INSTANCE = new OperateLogUtils();

        private InstanceHolder() {
        }
    }
}
