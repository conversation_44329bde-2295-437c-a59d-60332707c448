package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 休眠商机判定参数
 * <AUTHOR>
 * @since 2025/5/14
 */
@Data
public class BusinessJudgeParam {

    @Schema(description = "商机ID", type = "Integer", example = "1")
    private Integer businessId;

    @Schema(description = "是否释放", type = "boolean", example = "true")
    private boolean release;

    @Schema(description = "延期天数", type = "Integer", example = "1")
    private Integer delayDays;

}
