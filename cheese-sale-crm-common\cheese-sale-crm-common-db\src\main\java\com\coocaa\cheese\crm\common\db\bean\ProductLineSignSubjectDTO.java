
package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品线签约主体关系表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
public class ProductLineSignSubjectDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 产品线ID
     */
    private Long productLineId;

    /**
     * 签约主体ID
     */
    private Integer signSubjectId;

    /**
     * 统一社会信用代码(加密存储)
     */
    private String code;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}