@startuml
title 外部系统冻结转扣费流程

actor "外部系统(售卖平台)" as ExternalSystem
participant "外部接口\n/api/external-project-fund/*" as ExternalApiController
participant "资金账户管理" as AccountFundController
participant "资金账户Service" as FundService
database "机构资金变动记录表\ninstitution_fund_change_record" as ChangeRecordDB
database "刊例费报价表\nlisting_fee_quote" as ListingFeeDB
database "机构资金账户表\ninstitution_fund_account" as FundAccountDB

ExternalSystem -> ExternalApiController: POST /api/external-project-fund/freeze-to-deduct
note right
请求参数：
- 方案ID
- 方案详情
- 方案名称
- 客户名称
- 实际投放数据
end note

ExternalApiController -> FundService: 调用冻结转扣费服务

FundService -> ChangeRecordDB: 查询机构资金变动记录
note right
查询条件：
- 方案ID
- 变动原因=刊例费
- 变动类型=冻结中
- 创建方式=系统
end note
ChangeRecordDB --> FundService: 返回资金变动记录

FundService -> ListingFeeDB: 获取关联的刊例费报价
note right
获取冻结时的报价信息：
- 发布城市
- 点位单价
- 适用折扣
end note
ListingFeeDB --> FundService: 返回刊例费报价数据

FundService -> FundService: 分城市计算实际刊例费
note right
根据方案的实际投放数据：
1. 分城市计算折前总价
2. 分城市计算折后总价
3. 计算规则同销售工作台
end note

loop 对每个城市
    FundService -> FundService: 计算实际发生金额
    note right
    实际发生 = min{
      某城市新计算的折后总价,
      同一《机构最近变动记录》下【创建时机】=冻结中
      且相同城市对应的【实际发生】
    }
    end note

    FundService -> ListingFeeDB: 创建新的刊例费报价记录
    note right
    创建新记录：
    - 发布城市
    - 点位单价
    - 适用折扣
    - 折前总价(新计算)
    - 折后总价(新计算)
    - 实际发生金额
    - 创建时机=转扣费
    - 关联当前资金变动记录ID
    end note
end

FundService -> FundService: 计算实际消耗合计
note right
实际消耗合计 = ∑《资金变动记录ID》关联的
【创建时机】=转扣费的【实际发生】
end note

FundService -> ChangeRecordDB: 更新资金变动记录
note right
更新内容：
- 变动金额 = min(实际消耗合计, 原【变动金额】)
- 变动类型 = 减
end note

FundService -> FundAccountDB: 更新账户余额和冻结金额
note right
1. 减少冻结金额(total_frozen)
2. 减少账户余额(balance)
3. 增加累计消费金额(total_consumption)
end note

FundService --> ExternalApiController: 返回扣费结果
ExternalApiController --> ExternalSystem: 返回扣费成功结果

@enduml