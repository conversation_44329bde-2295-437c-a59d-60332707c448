package com.coocaa.cheese.crm.service;

import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.audit.service.AbstractApproveService;
import com.coocaa.cheese.crm.bean.InnerApproveTaskQueryParam;
import com.coocaa.cheese.crm.bean.InnerApproveTemplateParam;
import com.coocaa.cheese.crm.bean.PkChallengeRecordParam;
import com.coocaa.cheese.crm.common.db.bean.InnerApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.SignSubjectApproveDTO;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.PkChallengeRecordEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IPkChallengeRecordService;
import com.coocaa.cheese.crm.common.tools.config.api.InnerApproveConfig;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.cheese.crm.common.tools.enums.ApproveFieldTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.ChallengeExpectationEnum;
import com.coocaa.cheese.crm.common.tools.enums.ExecuteStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.convert.InnerApproveConvert;
import com.coocaa.cheese.crm.convert.PkChallengeRecordConvert;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import com.coocaa.cheese.crm.rpc.bean.SameDepartmentQueryParam;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveApplyVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveTaskVO;
import com.coocaa.cheese.crm.vo.InnerApprovePkChallengeDetailVO;
import com.coocaa.cheese.crm.vo.InnerApprovePkChallengePageVO;
import com.coocaa.cheese.crm.vo.PkChallengeRecordDetailVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * pk挑战记录服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Slf4j
@Service
@Primary
public class PkChallengeRecordService extends AbstractApproveService<InnerApprovePkChallengeDetailVO, PkChallengeRecordParam, InnerApprovePkChallengePageVO, InnerApproveTaskQueryParam> {

    @Resource
    private ProductLineService productLineServiceManager;
    @Resource
    private IAdvertisingSubjectService iAdvertisingSubjectService;
    @Resource
    private IPkChallengeRecordService iPkChallengeRecordService;
    @Resource
    private IBusinessService iBusinessService;
    @Resource
    private FeignAuthorityRpc feignAuthorityRpc;

    public PkChallengeRecordService(InnerApproveConfig innerApproveConfig,
                                    InnerApproveService approveService, IInnerApproveService innerApproveService) {
        super(innerApproveConfig, approveService, innerApproveService);
    }

    @Override
    protected StationTargetEnum getStationTarget() {
        return StationTargetEnum.PK_CHALLENGE;
    }

    @Override
    protected Integer getInnerApproveConfig() {
        return innerApproveConfig.getPk();
    }

    @Override
    protected InnerApprovePkChallengeDetailVO getInnerApproveDetail(Long id, Long bizId) {
        //查询投放主体关联品牌
        InnerApproveDetailDTO dto = iAdvertisingSubjectService.queryInnerApproveDetail(bizId.intValue());
        log.info("发起pk申请查询投放主体id:{}, dto:{}", id, JSONUtil.toJsonStr(dto));
        InnerApprovePkChallengeDetailVO vo = PkChallengeRecordConvert.INSTANCE.dtoToVo(dto);
        vo.setProductLineNames(productLineServiceManager.getProductLineBySubjectId(bizId.intValue()));
        if (null != id) {
            // 获取pk挑战详情
            PkChallengeRecordEntity entity = iPkChallengeRecordService.lambdaQuery()
                    .eq(PkChallengeRecordEntity::getId, id)
                    .last("limit 1")
                    .one();
            if (Objects.isNull(entity)) {
                throw new BusinessException("pk挑战记录不存在!");
            }
            PkChallengeRecordDetailVO pkChallengeRecordDetailVO = PkChallengeRecordConvert.INSTANCE.entityToVO(entity);
            vo.setPkChallengeRecordDetailVO(pkChallengeRecordDetailVO);
            BusinessEntity businessEntity = iBusinessService.getById(entity.getBusinessId());
            if (Objects.isNull(businessEntity)) {
                throw new BusinessException("商机不存在!");
            }
            vo.setProgress(businessEntity.getProgress());
            vo.setOwnerNameAndDeptName(businessEntity.getOwnerName() + Constants.SLASH + businessEntity.getDepartmentName());
        }
        return vo;
    }

    @Override
    protected Long getInnerApproveDetailBizId(Long id) {
        return iPkChallengeRecordService.getById(id).getAdvertisingSubjectId().longValue();
    }

    @Override
    protected Long saveRecord(PkChallengeRecordParam param) {
        PkChallengeRecordEntity entity = PkChallengeRecordConvert.INSTANCE.paramToEntity(param);
        CachedUser cachedUser = UserThreadLocal.getUser();
        entity.setChallengerDepartment(cachedUser.getDepartmentId());
        entity.setDepartmentName(cachedUser.getDepartmentName());
        entity.setCreatorName(cachedUser.getName());
        boolean save = iPkChallengeRecordService.save(entity);
        if (!save) {
            throw new BusinessException("pk申请记录表创建失败!");
        }
        return entity.getId();
    }

    @Override
    protected List<InnerApproveTemplateParam> buildApproveList(PkChallengeRecordParam p) {
        List<InnerApproveTemplateParam> approveParams = new ArrayList<>();
        // 获取商机归属人
        BusinessEntity businessEntity = iBusinessService.getById(p.getBusinessId());
        if (Objects.isNull(businessEntity)) {
            throw new BusinessException("商机不存在!");
        }
        SameDepartmentQueryParam param = new SameDepartmentQueryParam();
        param.setUserIdFirst(businessEntity.getOwnerId());
        param.setUserIdSecond(UserThreadLocal.getUserId());
        try {
            log.info("查询归属人是否同部门参数:{}", JSONUtil.toJsonStr(param));
            ResultTemplate<Boolean> result = feignAuthorityRpc.sameDepartment(param);
            log.info("查询归属人是否同部门结果:{}", JSONUtil.toJsonStr(result));
            approveParams.add(new InnerApproveTemplateParam("sameDepartment",
                    ApproveFieldTypeEnum.CHARACTER.getCode(), Boolean.toString(result.getData() != null && result.getData())));
        } catch (Exception e) {
            throw new BusinessException("查询归属人是否同部门失败!");
        }
        approveParams.add(new InnerApproveTemplateParam("creator", ApproveFieldTypeEnum.NUMBER.getCode(), UserThreadLocal.getUserId().toString()));
        return approveParams;
    }

    @Override
    protected List<InnerApprovePkChallengePageVO> setApproveTaskData(List<InnerApproveTaskVO> rowList,
                                                                     PageRequestVO<InnerApproveTaskQueryParam> e,
                                                                     List<InnerApproveEntity> innerApproveEntities) {
        Map<String, InnerApproveEntity> examineApproveEntityMap = innerApproveEntities.stream()
                .collect(Collectors.toMap(InnerApproveEntity::getInstanceCode, entity -> entity));
        //查询业务数据
        List<Integer> subjectIds = new ArrayList<>();
        Map<Long, SignSubjectApproveDTO> subjectMap = getSignSubjectApprove(innerApproveEntities, subjectIds);

        // 批量查询所有productLineNames
        Map<Integer, String> allProductLineNames = productLineServiceManager.getProductLineBySubjectId(subjectIds);
        return rowList.stream()
                .map(row -> {
                    InnerApprovePkChallengePageVO vo = InnerApproveConvert.INSTANCE.toPkPageVo(row);
                    vo.setDepartmentId(row.getDepartId());
                    return getInnerApprovePkChallengePageVO(examineApproveEntityMap, subjectMap, vo, row.getInstanceCode(), allProductLineNames);
                })
                .toList();
    }

    /**
     * 根据 InnerApproveEntities 获取 SignSubjectApproveDTO 的 Map
     *
     * @param innerApproveEntities 审批实体列表
     * @return bizId -> SignSubjectApproveDTO 的映射
     */
    private Map<Long, SignSubjectApproveDTO> getSignSubjectApprove(List<InnerApproveEntity> innerApproveEntities, List<Integer> subjectIds) {

        Map<Long, SignSubjectApproveDTO> bizIdToSubjectMap = new HashMap<>();
        // Step 1: 提取 bizId 列表并构建 bizId -> PkChallengeRecordEntity.id 的映射
        List<Long> bizIds = innerApproveEntities.stream()
                .map(InnerApproveEntity::getBizId)
                .distinct()
                .collect(Collectors.toList());

        // Step 2: 根据 bizId 查询 PkChallengeRecordEntity 并建立 bizId -> id 映射
        List<PkChallengeRecordEntity> pkChallengeRecordEntityList = iPkChallengeRecordService.lambdaQuery()
                .select(PkChallengeRecordEntity::getId, PkChallengeRecordEntity::getAdvertisingSubjectId)
                .in(PkChallengeRecordEntity::getId, bizIds)
                .list();

        if (pkChallengeRecordEntityList.isEmpty()) {
            return Collections.emptyMap();
        }

        List<Integer> advertisingSubjectIds = pkChallengeRecordEntityList.stream()
                .map(PkChallengeRecordEntity::getAdvertisingSubjectId)
                .collect(Collectors.toList());
        subjectIds.addAll(advertisingSubjectIds);

        // Step 3: 根据 advertisingSubjectIds 查询 SignSubjectApproveDTO 并建立映射
        Map<Integer, SignSubjectApproveDTO> subjectApproveMap = iAdvertisingSubjectService.innerSubjectEntity(advertisingSubjectIds).stream()
                .collect(Collectors.toMap(SignSubjectApproveDTO::getId, Function.identity()));

        if (subjectApproveMap.isEmpty()) {
            return Collections.emptyMap();
        }

        // Step 4: 建立 bizId -> SignSubjectApproveDTO 的映射
        for (PkChallengeRecordEntity pkChallengeRecord : pkChallengeRecordEntityList) {
            if (subjectApproveMap.containsKey(pkChallengeRecord.getAdvertisingSubjectId())) {
                bizIdToSubjectMap.put(pkChallengeRecord.getId(), subjectApproveMap.get(pkChallengeRecord.getAdvertisingSubjectId()));
            }
        }
        return bizIdToSubjectMap;
    }

    private InnerApprovePkChallengePageVO getInnerApprovePkChallengePageVO(Map<String, InnerApproveEntity> examineApproveEntityMap,
                                                                           Map<Long, SignSubjectApproveDTO> subjectMap,
                                                                           InnerApprovePkChallengePageVO vo,
                                                                           String instanceCode,
                                                                           Map<Integer, String> allProductLineNames) {
        InnerApproveEntity approveEntity = examineApproveEntityMap.get(instanceCode);
        if (approveEntity == null) {
            return vo;
        }
        Long bizId = approveEntity.getBizId();
        SignSubjectApproveDTO entity = subjectMap.get(bizId);
        if (entity == null) {
            return vo;
        }
        vo.setId(bizId);
        vo.setBizId(entity.getId());
        vo.setBrandId(entity.getBrandId());
        vo.setCompanyId(entity.getCompanyId());
        vo.setProductLineNames(allProductLineNames.get(entity.getId()));
        return vo;
    }

    @Override
    protected List<InnerApprovePkChallengePageVO> setApproveApplyData(
            List<InnerApproveApplyVO> rowList,
            PageRequestVO<InnerApproveTaskQueryParam> e, List<InnerApproveEntity> innerApproveEntities) {
        //查询业务数据
        List<Integer> subjectIds = new ArrayList<>();
        Map<Long, SignSubjectApproveDTO> subjectMap = getSignSubjectApprove(innerApproveEntities, subjectIds);
        Map<String, InnerApproveEntity> examineApproveEntityMap = innerApproveEntities.stream()
                .collect(Collectors.toMap(InnerApproveEntity::getInstanceCode, entity -> entity));
        // 批量查询所有productLineNames
        Map<Integer, String> allProductLineNames = productLineServiceManager.getProductLineBySubjectId(subjectIds);
        return rowList.stream()
                .map(row -> {
                    InnerApprovePkChallengePageVO vo = InnerApproveConvert.INSTANCE.toPkPageVo(row);
                    vo.setInstanceCreateTime(row.getCreateTime());
                    vo.setDepartmentId(row.getDepartId());
                    return getInnerApprovePkChallengePageVO(examineApproveEntityMap, subjectMap, vo, row.getInstanceCode(), allProductLineNames);
                })
                .toList();
    }

    @Override
    protected void checkInitiate(PkChallengeRecordParam param) {
        if (!ChallengeExpectationEnum.CUSTOMER_TRANSFER.getCode().equals(param.getChallengeExpectation())) {
            throw new BusinessException("发起pk挑战期望值只能是客户转让给我");
        }
    }

    /**
     * 校验是否可以发起pk挑战
     *
     * @param bizId 主体id
     */
    public boolean canPkChallengeCreate(Integer bizId) {
        return !iPkChallengeRecordService.lambdaQuery()
                .eq(PkChallengeRecordEntity::getAdvertisingSubjectId, bizId)
                .eq(PkChallengeRecordEntity::getExecuteStatus, ExecuteStatusEnum.PENDING.getCode())
                .exists();
    }

    @AutoTranslate
    public InnerApprovePkChallengeDetailVO approveDetail(Long id) {
        InnerApproveEntity entity = innerApproveService.lambdaQuery()
                .eq(InnerApproveEntity::getBizId, id)
                .eq(InnerApproveEntity::getType, getStationTarget().getCode())
                .orderByDesc(InnerApproveEntity::getCreateTime)
                .last("LIMIT 1")
                .one();
        if (Objects.isNull(entity)) {
            throw new BusinessException("申请查询审批信息详情未找到审批信息");
        }
        // 查询投放主体关联品牌
        InnerApprovePkChallengeDetailVO dto = getInnerApproveDetail(id, getInnerApproveDetailBizId(id));
        log.info("发起申请查询投放主体id:{}, dto:{}", id, JSONUtil.toJsonStr(dto));
        // 查询审批信息
        InnerApproveInstanceVO innerApproveInstanceVO = approveService.queryDetail(entity.getInstanceCode());
        if (Objects.nonNull(innerApproveInstanceVO)) {
            dto.setApprovalName(innerApproveInstanceVO.getApprovalName());
            dto.setInstanceUserId(innerApproveInstanceVO.getUserId());
            dto.setInstanceCreateTime(innerApproveInstanceVO.getCreateTime());
            dto.setEndTime(innerApproveInstanceVO.getEndTime());
            dto.setApprovalResult(innerApproveInstanceVO.getApprovalResult());
            dto.setDepartmentId(innerApproveInstanceVO.getDepartId());
        }
        return dto;
    }
}