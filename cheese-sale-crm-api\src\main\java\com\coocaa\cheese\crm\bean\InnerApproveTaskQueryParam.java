package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.audit.bean.BaseInnerApproveParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询站内审批任务列表
 * <AUTHOR>
 * @since 2025-06-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "InnerApproveTaskQueryParam", description = "查询站内审批任务列表")
public class InnerApproveTaskQueryParam extends BaseInnerApproveParam {

}
