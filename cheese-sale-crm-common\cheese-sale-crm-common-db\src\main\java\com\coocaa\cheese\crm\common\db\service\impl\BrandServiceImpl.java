package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.bean.BrandQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.mapper.BrandMapper;
import com.coocaa.cheese.crm.common.db.service.IBrandService;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 主品牌信息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BrandServiceImpl extends ServiceImpl<BrandMapper, BrandEntity> implements IBrandService {

    @Override
    public IPage<BrandEntity> pageList(IPage<BrandEntity> page, BrandQueryDTO condition) {
        condition.setExactFlag(BooleFlagEnum.isYes(condition.getExactFlag())
                ? BooleFlagEnum.YES.getCode() : BooleFlagEnum.NO.getCode());

        // 结束日期+1天
        if (Objects.nonNull(condition.getCreateEndDate())) {
            condition.setCreateEndDate(condition.getCreateEndDate().plusDays(1));
        }

        return getBaseMapper().pageList(page, condition);
    }

    @Override
    public IPage<BrandEntity> h5PageList(IPage<BrandEntity> page, BrandQueryDTO condition) {
        condition.setExactFlag(BooleFlagEnum.isYes(condition.getExactFlag())
                ? BooleFlagEnum.YES.getCode() : BooleFlagEnum.NO.getCode());
        return getBaseMapper().h5PageList(page, condition);
    }

}
