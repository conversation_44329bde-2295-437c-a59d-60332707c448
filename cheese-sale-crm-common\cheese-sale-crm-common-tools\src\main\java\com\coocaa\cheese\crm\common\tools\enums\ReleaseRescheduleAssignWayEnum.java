package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售-释放改期商机分配方式(字典0150)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum ReleaseRescheduleAssignWayEnum implements IEnumType<String> {

    BUILD_BY_ONESELF("0150-1", "自建"),
    ON_JOB_CHANGE("0150-2", "在职转移"),
    OUT_JOB_HAND_OVER("0150-3", "离职交接");

    private final String code;
    private final String desc;

    private static final Map<String, ReleaseRescheduleAssignWayEnum> BY_CODE_MAP =
            Arrays.stream(ReleaseRescheduleAssignWayEnum.values())
                    .collect(Collectors.toMap(ReleaseRescheduleAssignWayEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static ReleaseRescheduleAssignWayEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code         代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static ReleaseRescheduleAssignWayEnum parse(String code, ReleaseRescheduleAssignWayEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ReleaseRescheduleAssignWayEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 