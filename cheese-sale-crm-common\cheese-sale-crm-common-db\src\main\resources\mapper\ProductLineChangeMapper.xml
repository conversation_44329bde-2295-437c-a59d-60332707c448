<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.ProductLineChangeMapper">

    <select id="queryInnerProductLineChange" resultType="com.coocaa.cheese.crm.common.db.bean.ProductLineChangePageDTO">
        SELECT
          scplc.id,
          scb.brand_id,
          scas.company_id,
          group_concat(scpl.name) AS productLine
         FROM sale_crm_product_line_change scplc
         LEFT JOIN sale_crm_business scb ON scplc.business_id = scb.id
         LEFT JOIN sale_comm_advertising_subject scas ON scb.advertising_subject_id = scas.id
         LEFT JOIN sale_crm_product_line_sign_subject scplss ON scplss.sign_subject_id = scas.id
               AND scb.advertising_subject_id = scplss.sign_subject_id AND scplss.delete_flag = 0
         LEFT JOIN sale_crm_product_line scpl ON scpl.id = scplss.product_line_id
               AND scpl.brand_id = scas.brand_id AND scpl.delete_flag = 0
        WHERE scplc.id IN
        <foreach collection="bizIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
          AND scplc.delete_flag = 0
        GROUP BY scplc.id, scas.id, scpl.brand_id
    </select>

    <select id="queryInnerApproveDetail" resultType="com.coocaa.cheese.crm.common.db.bean.InnerProductApproveDetailDTO">
        SELECT
          scplc.id,
          scb.brand_id,
          scas.company_id,
          scb.progress,
          scplc.before_product_line,
          scplc.after_product_line,
          scplc.remark
         FROM sale_crm_product_line_change scplc
         LEFT JOIN sale_crm_business scb ON scplc.business_id = scb.id AND scb.delete_flag = '0'
         LEFT JOIN sale_comm_advertising_subject scas ON scb.advertising_subject_id = scas.id AND scas.delete_flag = 0
        WHERE scplc.id = #{id}
          AND scplc.delete_flag = '0'
    </select>

    <select id="queryProductLineBySign" resultType="com.coocaa.cheese.crm.common.db.bean.ProductLineDTO">
        SELECT
          scpl.brand_id,
          scpl.id,
          scpl.name
         FROM sale_crm_product_line scpl, sale_comm_advertising_subject scas
        WHERE scpl.brand_id = scas.brand_id
          AND scas.id = #{id}
          AND scpl.delete_flag = '0'
          AND scas.delete_flag = '0'
    </select>
</mapper>