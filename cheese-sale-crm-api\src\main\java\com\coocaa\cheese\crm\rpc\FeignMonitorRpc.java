package com.coocaa.cheese.crm.rpc;

import com.coocaa.ad.common.config.FeignConfig;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.vo.PlanTransferDeductionPointVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Monitor rpc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-08
 */
@FeignClient(value = "cheese-monitor-api", path = "/api/monitor",
        configuration = FeignConfig.class)
public interface FeignMonitorRpc {
    @GetMapping("/monitor-sale-plan-detail/monitor-point-play-details")
    ResultTemplate<List<PlanTransferDeductionPointVO>> getPlanMonitorPointPlayDetails(@RequestParam(name = "playDate")String playDate,
                                                                                      @RequestParam(name = "planId")Integer planId);
}
