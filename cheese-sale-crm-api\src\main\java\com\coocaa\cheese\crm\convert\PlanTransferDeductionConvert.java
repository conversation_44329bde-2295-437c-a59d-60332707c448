package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.mongo.PointFeeDocument;
import com.coocaa.cheese.crm.vo.PlanTransferDeductionPointVO;
import org.mapstruct.Mapper;
import org.mapstruct.control.DeepClone;

import java.util.List;

/**
 * 方案转扣费对象转换
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-07
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface PlanTransferDeductionConvert {
    List<PointFeeDocument> pointVoToDocument(List<PlanTransferDeductionPointVO> pointList);

}
