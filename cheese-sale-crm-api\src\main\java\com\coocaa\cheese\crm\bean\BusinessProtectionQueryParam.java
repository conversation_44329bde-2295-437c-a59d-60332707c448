package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 商机保护期查询参数
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessProtectionQueryParam", description = "商机保护期查询参数")
public class BusinessProtectionQueryParam {
    @Schema(description = "控制目标类型(字典0076)", type = "String", example = "0076-1")
    private String targetType;

    @Schema(description = "控制对象ID", type = "String", example = "od-xxxx**bbyy")
    private String targetId;
}