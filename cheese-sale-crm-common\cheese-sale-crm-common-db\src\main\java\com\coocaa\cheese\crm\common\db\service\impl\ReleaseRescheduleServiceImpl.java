package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveTaskPageDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseHistoryDTO;
import com.coocaa.cheese.crm.common.db.entity.ReleaseRescheduleEntity;
import com.coocaa.cheese.crm.common.db.mapper.ReleaseRescheduleMapper;
import com.coocaa.cheese.crm.common.db.service.IReleaseRescheduleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 释放改期记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ReleaseRescheduleServiceImpl
        extends ServiceImpl<ReleaseRescheduleMapper, ReleaseRescheduleEntity>
        implements IReleaseRescheduleService {

    @Override
    public InnerReleaseApproveDetailDTO queryInnerApproveDetail(Integer id) {
        return getBaseMapper().queryInnerApproveDetail(id);
    }

    @Override
    public List<InnerReleaseApproveTaskPageDTO> queryInnerBusiness(List<Integer> bizIds) {
        return getBaseMapper().queryInnerBusiness(bizIds);
    }

    @Override
    public List<InnerReleaseHistoryDTO> queryReleaseHistory(Integer id, LocalDateTime createTime, Integer businessId) {
        return getBaseMapper().queryReleaseHistory(id, createTime, businessId);
    }
}