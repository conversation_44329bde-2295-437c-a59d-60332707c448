package com.coocaa.cheese.crm.controller.config;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.bean.BusinessProtectionParam;
import com.coocaa.cheese.crm.bean.BusinessProtectionQueryParam;
import com.coocaa.cheese.crm.service.BusinessProtectionService;
import com.coocaa.cheese.crm.vo.BusinessProtectionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商机保护期
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@RestController
@RequestMapping("/business-protections")
@Tag(name = "商机保护期配置", description = "商机保护期配置")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessProtectionController extends BaseController {
    private final BusinessProtectionService businessProtectionService;

    /**
     * 商机保护期配置列表(分页)
     */
    @Operation(summary = "商机保护期配置列表(分页)")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<BusinessProtectionVO>> pageList(@RequestBody PageRequestVO<BusinessProtectionQueryParam> pageRequest) {
        return ResultTemplate.success(businessProtectionService.pageList(pageRequest));
    }

    /**
     * 商机保护期配置详情
     */
    @Operation(summary = "商机保护期配置详情")
    @Parameter(name = "id", description = "商机保护期配置ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<BusinessProtectionVO> getDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessProtectionService.getDetail(id));
    }

    /**
     * 商机保护期配置创建
     */
    @Operation(summary = "创建商机保护期配置")
    @PostMapping
    public ResultTemplate<Boolean> create(@RequestBody @Validated BusinessProtectionParam param) {
        return ResultTemplate.success(businessProtectionService.createOrUpdate(null, param));
    }

    /**
     * 商机保护期配置修改
     */
    @Operation(summary = "商机保护期配置修改")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> update(@PathVariable("id") Integer id, @RequestBody BusinessProtectionParam param) {
        return ResultTemplate.success(businessProtectionService.createOrUpdate(id, param));
    }

    /**
     * 商机保护期配置删除
     */
    @Operation(summary = "商机保护期配置删除")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> delete(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessProtectionService.delete(id));
    }
}
