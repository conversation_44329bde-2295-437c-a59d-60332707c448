package com.coocaa.cheese.crm.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.common.core.redission.model.DelayedMessage;
import com.coocaa.cheese.crm.common.db.entity.AdvertisingSubjectEntity;
import com.coocaa.cheese.crm.common.db.service.IAdvertisingSubjectService;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.tools.enums.DelayQueueEnum;
import com.coocaa.cheese.crm.handler.message.MessageHandler;
import com.coocaa.cheese.crm.handler.message.MessageHandlerFactory;
import com.coocaa.cheese.crm.service.ConfigService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 延迟队列消费定时任务
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DelayQueueConsumerTask {

    private final RedissonClient redisson;
    private final IAdvertisingSubjectService advertisingSubjectService;
    private final ConfigService configService;

    private static List<DelayQueueEnum> getDelayQueueEnums(String messageType) {
        // 获取需要处理的队列类型
        List<DelayQueueEnum> queueEnums;
        if (StringUtils.isNotBlank(messageType)) {
            // 如果指定了消息类型，只处理对应的队列
            DelayQueueEnum queueEnum = DelayQueueEnum.getByMessageType(messageType);
            if (queueEnum == null) {
                log.warn("未找到指定消息类型的队列: {}", messageType);
                return null;
            }
            queueEnums = List.of(queueEnum);
        } else {
            // 否则处理所有队列
            queueEnums = Arrays.asList(DelayQueueEnum.values());
        }
        return queueEnums;
    }

    @XxlJob("delayQueueConsumerJob")
    public void processQueues() {
        // 获取任务参数
        String messageType = XxlJobHelper.getJobParam();
        processQueues(messageType);
    }

    /**
     * 处理队列消息
     *
     * @param messageType 消息类型，如果为空则处理所有类型
     */
    public void processQueues(String messageType) {
        try {
            // 获取需要处理的队列类型
            List<DelayQueueEnum> queueEnums = getDelayQueueEnums(messageType);
            if (queueEnums == null) {
                return;
            }

            // 遍历队列处理消息
            for (DelayQueueEnum queueEnum : queueEnums) {
                // 获取队列
                RQueue<DelayedMessage> queue = redisson.getQueue(queueEnum.getQueueName());

                // 尝试获取消息
                DelayedMessage message = queue.poll();
                if (message == null) {
                    continue;
                }

                log.info("收到延迟消息, 队列名称: {}, 消息类型: {}, 消息内容: {}",
                        queueEnum.getQueueName(), message.getMessageType(), message.getContent());

                try {
                    // 获取对应的消息处理器并处理消息
                    MessageHandler handler = MessageHandlerFactory.getHandler(message.getMessageType());
                    if (handler != null) {
                        handler.handle(message);
                    } else {
                        log.warn("未找到消息处理器: {}", message.getMessageType());
                    }
                } catch (Exception e) {
                    log.error("处理消息失败: {}", message, e);
                }
            }
        } catch (Exception e) {
            log.error("延迟队列消费定时任务执行异常:{}", e.getMessage(), e);
        }
    }

    /**
     * 签约主体保护期定时补偿任务
     * * @param messageType 消息类型，如果为空则处理所有类型
     */
    @XxlJob("advertisingSubjectPeriodJob")
    public void advertisingSubjectPeriodJob() {
        log.info("开始执行签约主体保护期定时补偿任务");
        try {
            DelayQueueEnum queueEnum = DelayQueueEnum.ADVERTISING_SUBJECT_QUEUE;
            // 获取配置的保护期时间
            TimeUnit timeUnit = TimeUnit.valueOf(configService.getConfig(queueEnum.getTimeUnit()).getValue());
            long delay = Long.parseLong(configService.getConfig(queueEnum.getDelay()).getValue());

            // 将延迟时间转换为秒
            long delayInSeconds = timeUnit.toSeconds(delay);

            // 计算阈值时间（当前时间减去保护期时间）
            LocalDateTime threshold = LocalDateTime.now().minusSeconds(delayInSeconds);

            // 查询所有需要更新的签约主体
            // 条件：生效时间 <= threshold && 保护期标记为是(0)
            LambdaQueryWrapper<AdvertisingSubjectEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.le(AdvertisingSubjectEntity::getEffectiveTime, threshold)
                    .eq(AdvertisingSubjectEntity::getProtectionPeriodFlag, BooleFlagEnum.YES.getCode())
                    .eq(AdvertisingSubjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode());

            List<AdvertisingSubjectEntity> subjects = advertisingSubjectService.list(queryWrapper);
            log.info("找到 {} 个需要更新保护期状态的签约主体", subjects.size());

            List<Integer> subjectId = subjects.stream().map(AdvertisingSubjectEntity::getId).toList();
            if (!subjectId.isEmpty()) {
                // 批量更新保护期标记
                advertisingSubjectService.lambdaUpdate()
                        .set(AdvertisingSubjectEntity::getProtectionPeriodFlag, BooleFlagEnum.NO.getCode())
                        .in(AdvertisingSubjectEntity::getId, subjectId).update();
            }
            log.info("签约主体保护期定时补偿任务执行完成");
        } catch (Exception e) {
            log.error("执行签约主体保护期定时补偿任务失败", e);
        }
    }
} 