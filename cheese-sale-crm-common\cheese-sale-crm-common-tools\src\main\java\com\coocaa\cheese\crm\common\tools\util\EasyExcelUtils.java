package com.coocaa.cheese.crm.common.tools.util;

import cn.hutool.core.lang.UUID;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.coocaa.cheese.crm.common.tools.config.cos.CosConfig;
import com.coocaa.cheese.crm.common.tools.cos.ObjectUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class EasyExcelUtils {
    private static CosConfig cosConfig;

    @Autowired
    public void setCosConfig(CosConfig cosConfig) {
        EasyExcelUtils.cosConfig = cosConfig;
    }


    /**
     * 生成Excel文件的字节数组
     *
     * @param sheetName sheet名
     * @param dataList  数据列表
     * @param fieldList 要导出的字段列表，为空时导出所有字段
     * @param <T>       泛型参数，代表实体类型
     * @return Excel文件的字节数组
     * @throws IOException IO异常
     */
    public static <T> byte[] createExcel(String sheetName, List<T> dataList, List<String> fieldList) throws IOException {
        if (CollectionUtils.isEmpty(dataList)) {
            throw new IllegalArgumentException("数据列表不能为空");
        }

        // 如果fieldList为空，获取所有字段
        if (CollectionUtils.isEmpty(fieldList)) {
            fieldList = getAllFields(dataList.get(0).getClass());
        }

        // 排除fieldList中的空字符串或NULL
        fieldList.removeAll(Collections.singleton(""));
        fieldList.removeAll(Collections.singleton(null));

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 获取字段映射
            Map<String, String> fieldMapping = getFieldMapping(dataList.get(0).getClass(), fieldList);

            // 转换数据
            List<List<String>> rows = convertToRows(dataList, fieldList);

            // 设置表头样式
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontName("宋体");
            headWriteFont.setFontHeightInPoints((short) 11);
            // 加粗
            headWriteFont.setBold(true);
            headWriteCellStyle.setWriteFont(headWriteFont);

            // 设置内容样式
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            // 内容字体样式
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setFontName("宋体");
            contentWriteFont.setFontHeightInPoints((short) 11);
            contentWriteCellStyle.setWriteFont(contentWriteFont);

            HorizontalCellStyleStrategy styleStrategy =
                    new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

            // 写入Excel
            EasyExcel.write(outputStream)
                    .head(convertToHead(fieldList, fieldMapping))
                    .sheet(sheetName)
                    .registerWriteHandler(styleStrategy)
                    .useDefaultStyle(false)
                    .doWrite(rows);

            return outputStream.toByteArray();
        }
    }

    /**
     * 生成Excel文件的字节数组，排除指定字段
     *
     * @param sheetName     sheet名
     * @param dataList      数据列表
     * @param excludeFields 要排除的字段列表
     * @param <T>           泛型参数，代表实体类型
     * @return Excel文件的字节数组
     * @throws IOException IO异常
     */
    public static <T> byte[] createExcelWithExclude(String sheetName, List<T> dataList, List<String> excludeFields) throws IOException {
        if (CollectionUtils.isEmpty(dataList)) {
            throw new IllegalArgumentException("数据列表不能为空");
        }

        List<String> allFields = getAllFields(dataList.get(0).getClass());
        if (!CollectionUtils.isEmpty(excludeFields)) {
            allFields.removeAll(excludeFields);
        }

        return createExcel(sheetName, dataList, allFields);
    }

    /**
     * 获取类的所有字段名
     *
     * @param clazz 类
     * @return 字段名列表
     */
    private static List<String> getAllFields(Class<?> clazz) {
        List<String> fieldList = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            // 排除static、final修饰的字段以及带有@ExcelIgnore注解的字段
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers())
                    || java.lang.reflect.Modifier.isFinal(field.getModifiers())
                    || field.isAnnotationPresent(ExcelIgnore.class)) {
                continue;
            }
            fieldList.add(field.getName());
        }
        return fieldList;
    }

    public static <T> String createExcelToCos(String sheetName, List<T> dataList, List<String> fieldList, String filename, String feature) throws IOException {
        if (StringUtils.isEmpty(filename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        // 检测feature是否为空,为空则设置为"default"
        if (StringUtils.isEmpty(feature)) {
            feature = "default";
        }
        byte[] bytes = createExcel(sheetName, dataList, fieldList);
        // 将字节数组转换为文件，存入系统临时目录，兼容windows与linux, 文件名采用随机字符串
        String tempFileName = UUID.randomUUID().toString();
        File file = new File(System.getProperty("java.io.tmpdir"), tempFileName);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(bytes);
        }
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, filename), file);
        // 返回文件url
        return ObjectUtils.getAccessUrl(feature, filename);
    }

    /**
     * 获取字段映射关系
     *
     * @param clazz     类
     * @param fieldList 字段列表
     * @return 字段映射关系
     */
    private static Map<String, String> getFieldMapping(Class<?> clazz, List<String> fieldList) {
        Map<String, String> mapping = new HashMap<>();
        for (String fieldName : fieldList) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
                String columnName = annotation != null && annotation.value().length > 0
                        ? annotation.value()[0]
                        : fieldName;
                mapping.put(fieldName, columnName);
            } catch (NoSuchFieldException e) {
                mapping.put(fieldName, fieldName);
            }
        }
        return mapping;
    }

    /**
     * 将字段列表转换为表头
     *
     * @param fieldList    字段列表
     * @param fieldMapping 字段映射关系
     * @return 表头列表
     */
    private static List<List<String>> convertToHead(List<String> fieldList, Map<String, String> fieldMapping) {
        List<List<String>> head = new ArrayList<>();
        for (String field : fieldList) {
            List<String> column = new ArrayList<>();
            column.add(fieldMapping.getOrDefault(field, field));
            head.add(column);
        }
        return head;
    }

    /**
     * 将数据转换为行数据
     *
     * @param dataList  数据列表
     * @param fieldList 字段列表
     * @param <T>       泛型参数，代表实体类型
     * @return 行数据列表
     */
    private static <T> List<List<String>> convertToRows(List<T> dataList, List<String> fieldList) {
        List<List<String>> rows = new ArrayList<>();

        for (T data : dataList) {
            List<String> row = new ArrayList<>();
            Class<?> clazz = data.getClass();

            for (String fieldName : fieldList) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Object value = field.get(data);

                    if (value != null) {
                        DateTimeFormat dateFormat = field.getAnnotation(DateTimeFormat.class);
                        if (dateFormat != null) {
                            if (value instanceof java.util.Date) {
                                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(dateFormat.value());
                                row.add(sdf.format((java.util.Date) value));
                            } else if (value instanceof LocalDateTime) {
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat.value());
                                row.add(((LocalDateTime) value).format(formatter));
                            } else {
                                row.add(String.valueOf(value));
                            }
                        } else {
                            row.add(String.valueOf(value));
                        }
                    } else {
                        row.add("");
                    }
                } catch (Exception e) {
                    row.add("");
                }
            }

            rows.add(row);
        }

        return rows;
    }
}
