package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.SubjectExamineParam;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveOpinionTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.convert.InnerApproveConvert;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveNodeVO;
import com.coocaa.cheese.crm.rpc.vo.InnerInstanceTaskVO;
import com.coocaa.cheese.crm.vo.InnerExamineApproveVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 通用审批服务类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CommonApproveService {

    private static final int APPROVE_ZERO = 0;
    private static final int APPROVE_ONE = 1;
    protected final InnerApproveService approveService;
    protected final IInnerApproveService innerApproveService;


    @AutoTranslate
    public List<InnerExamineApproveVO> queryApproveList(Long id, String stationTarget) {
        // 该接口调用审批节点查询都会用到这个接口，所以无法关联用户查询
        String instanceCode = Optional.ofNullable(innerApproveService.queryInstanceCodeById(id, stationTarget))
                .orElseThrow(() -> new BusinessException("查询审批节点失败"));
        // 根据审批code查询审批中心节点list
        List<InnerApproveNodeVO> nodes = approveService.queryNodes(instanceCode);
        if (CollectionUtil.isEmpty(nodes)) {
            return Collections.emptyList();
        }
        // 返回数据
        return nodes.stream()
                .filter(i -> i.getApprovalFlag() == APPROVE_ZERO || i.getApprovalFlag() == APPROVE_ONE)
                .map(e -> {
                    InnerExamineApproveVO innerExamineApproveVO = InnerApproveConvert.INSTANCE.toVo(e);
                    innerExamineApproveVO.setBeginTime(e.getStartTime());
                    innerExamineApproveVO.setApprovalTime(e.getEndTime());
                    return innerExamineApproveVO;
                }).sorted(Comparator.comparing(InnerExamineApproveVO::getApprovalFlag)).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean approval(Long id, SubjectExamineParam param) {

        StationTargetEnum stationTarget = StationTargetEnum.parse(param.getStationTarget());
        // 根据id查询审批code
        InnerApproveEntity entity = innerApproveService.lambdaQuery()
                .eq(InnerApproveEntity::getBizId, id)
                .eq(InnerApproveEntity::getType, param.getStationTarget())
                .orderByDesc(InnerApproveEntity::getCreateTime)
                .last("LIMIT 1")
                .one();
        log.info("申请审批时查询审批id:{}, param:{}, entity:{}", id, JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(entity));
        if (Objects.isNull(entity)) {
            throw new BusinessException("申请审批未找到审批信息");
        }
        if (Objects.equals(param.getApproveType(), InnerApproveOpinionTypeEnum.AGREE.getCode())) {
            return approveService.agree(id, entity.getInstanceCode(), param.getComment(), param.getApplyDelayDate(), stationTarget);
        } else if (Objects.equals(param.getApproveType(), InnerApproveOpinionTypeEnum.REJECT.getCode())) {
            return approveService.reject(id, entity.getInstanceCode(), param.getComment(), stationTarget);
        }

        return Boolean.FALSE;
    }

    public Long queryApproveTaskUrl(String instanceCode, String stationTarget) {

        //获取登录人信息
        Integer currentUserId = UserThreadLocal.getUserId();
        InnerInstanceTaskVO innerInstanceTaskVO = approveService.queryTask(instanceCode);
        log.info("通知审批人获取审批任务信息currentUserId:{}, vo:{}", currentUserId, JSONUtil.toJsonStr(innerInstanceTaskVO));
        if (Objects.isNull(innerInstanceTaskVO) || Objects.isNull(innerInstanceTaskVO.getUserId())) {
            throw new BusinessException("链接失效或无权限");
        }
        if (Objects.equals(innerInstanceTaskVO.getUserId(), currentUserId)) {
            return getBizId(instanceCode, stationTarget);
        } else {
            throw new BusinessException("链接失效或无权限");
        }
    }

    @AutoTranslate
    public Long queryApproveApplyUrl(String instanceCode, String stationTarget) {

        //获取登录人信息
        Integer currentUserId = UserThreadLocal.getUserId();
        InnerApproveInstanceVO innerInstanceTaskVO = approveService.queryDetail(instanceCode);
        log.info("通知提交人获取审批申请信息currentUserId:{}, vo:{}", currentUserId, JSONUtil.toJsonStr(innerInstanceTaskVO));
        if (Objects.isNull(innerInstanceTaskVO) || Objects.isNull(innerInstanceTaskVO.getUserId())) {
            throw new BusinessException("链接失效或无权限");
        }
        if (Objects.equals(innerInstanceTaskVO.getUserId(), currentUserId)) {
            return getBizId(instanceCode, stationTarget);
        } else {
            throw new BusinessException("链接失效或无权限");
        }
    }

    public Long queryApproveCcUrl(String instanceCode, String stationTarget) {

        //获取登录人信息
        Integer currentUserId = UserThreadLocal.getUserId();
        InnerApproveInstanceVO innerInstanceTaskVO = approveService.queryDetail(instanceCode);
        log.info("通知抄送人获取审批申请信息currentUserId:{}, vo:{}", currentUserId, JSONUtil.toJsonStr(innerInstanceTaskVO));
        if (Objects.isNull(innerInstanceTaskVO) || Objects.isNull(innerInstanceTaskVO.getCcReviewer())) {
            throw new BusinessException("链接失效或无权限");
        }
        if (Objects.equals(innerInstanceTaskVO.getCcReviewer(), currentUserId)) {
            return getBizId(instanceCode, stationTarget);
        } else {
            throw new BusinessException("链接失效或无权限");
        }
    }

    private Long getBizId(String instanceCode, String stationTarget) {
        return innerApproveService.lambdaQuery()
                .select(InnerApproveEntity::getBizId)
                .eq(InnerApproveEntity::getInstanceCode, instanceCode)
                .eq(InnerApproveEntity::getType, stationTarget)
                .orderByDesc(InnerApproveEntity::getCreateTime)
                .last("LIMIT 1")
                .one()
                .getBizId();
    }
}
