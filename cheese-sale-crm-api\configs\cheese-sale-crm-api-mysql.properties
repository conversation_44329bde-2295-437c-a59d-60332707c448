######################### MySQL\u914D\u7F6E #########################
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name=com.p6spy.engine.spy.P6SpyDriver
spring.datasource.url=*********************************************************************************************************************************

# \u7528\u6237\u540D\u5BC6\u7801
spring.datasource.username=cheese_cms
spring.datasource.password=utsdd109

# \u5BA2\u6237\u7AEF\u7B49\u5F85\u8FDE\u63A5\u6C60\u8FDE\u63A5\u7684\u6700\u5927\u6BEB\u79D2\u6570
spring.datasource.hikari.connection-timeout=20000
# \u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5\u6570
spring.datasource.hikari.minimum-idle=10
# \u6700\u5927\u8FDE\u63A5\u6C60\u5927\u5C0F
spring.datasource.hikari.maximum-pool-size=100
# \u8FDE\u63A5\u6C60\u4E2D\u7A7A\u95F2\u7684\u6700\u957F\u65F6\u95F4\u6BEB\u79D2
spring.datasource.hikari.idle-timeout=30000
# \u6C60\u4E2D\u8FDE\u63A5\u5173\u95ED\u540E\u7684\u6700\u957F\u751F\u547D\u5468\u671F\u6BEB\u79D2
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.connection-test-query=SELECT 1
