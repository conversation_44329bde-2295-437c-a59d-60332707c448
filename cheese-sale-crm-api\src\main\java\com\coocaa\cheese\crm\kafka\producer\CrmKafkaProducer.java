package com.coocaa.cheese.crm.kafka.producer;

import com.coocaa.ad.common.core.kafka.KafkaProducer;
import com.coocaa.cheese.crm.kafka.constant.KafkaConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Kafka 生产者
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-05
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CrmKafkaProducer {
    private final KafkaProducer kafkaProducer;

    /**
     * 主体变更消息发送
     *
     * @param obj
     */
    public void sendAdvertisingSubjectChange(Object obj) {
        kafkaProducer.send(obj, KafkaConstants.TOPIC_ADVERTISING_SUBJECT_CHANGE);
    }

    /**
     * 归属人变更消息发送
     *
     * @param obj
     */
    public void sendBusinessOwnerChange(Object obj) {
        kafkaProducer.send(obj, KafkaConstants.TOPIC_BUSINESS_OWNER_CHANGE);
    }
}
    
