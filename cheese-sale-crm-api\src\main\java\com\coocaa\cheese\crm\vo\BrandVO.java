package com.coocaa.cheese.crm.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.common.serializer.DesensitizeSerializer;
import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 主品牌信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BrandVO", description = "主品牌信息VO")
public class BrandVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "品牌名称", type = "String", example = "可口可乐")
    private String name;

    @Schema(description = "母品牌", type = "String", example = "FOOD")
    @TransField(type = TransTypes.BRAND)
    private Integer parentId;
    private String parentName;

    @Schema(description = "所属行业", type = "String", example = "FOOD")
    @TransField(type = TransTypes.INDUSTRY)
    private String industryCode;
    private String industryName;

    @Schema(description = "所属公司ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;

    @Schema(description = "统一社会信用代码", type = "String", example = "91330000XXX")
    @JSONField(serializeUsing = DesensitizeSerializer.class)
    private String creditCode;

    @Schema(description = "品牌Logo", type = "String", example = "http://example.com/logo.png")
    private String logoUrl;

    @Schema(description = "品牌标签", type = "String", example = "Benz, BMW")
    private String tagNames;

    @Schema(description = "品牌标签列表", type = "List", example = "Benz, BMW")
    private List<BrandTagVO> tags;

    @Schema(description = "生效状态(1:生效,0:不生效)", type = "Integer", example = "1")
    private Integer effectiveStatus;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "生效时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime effectiveTime;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "操作人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer operator;

    @Schema(description = "操作人", type = "Integer", example = "1")
    private String operatorName;

    @Schema(description = "创建人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer creator;
    private String creatorName;

    @Schema(description = "产品线名称列表")
    private List<String> productLineNameList;
} 