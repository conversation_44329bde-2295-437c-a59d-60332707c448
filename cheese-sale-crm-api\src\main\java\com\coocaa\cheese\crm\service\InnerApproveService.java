package com.coocaa.cheese.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.InnerApproveTemplateParam;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.OperateLogEntity;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IOperateLogService;
import com.coocaa.cheese.crm.common.tools.enums.ApproveFieldTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveOpinionTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.listener.event.InnerApproveEvent;
import com.coocaa.cheese.crm.rpc.FeignInnerApprovalRpc;
import com.coocaa.cheese.crm.rpc.bean.ApprovalInitiateParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveApplyPageQueryParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveOpinionParam;
import com.coocaa.cheese.crm.rpc.bean.InnerApproveTaskPageQueryParam;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveNodeVO;
import com.coocaa.cheese.crm.rpc.vo.InnerInstanceTaskVO;
import com.coocaa.cheese.crm.rpc.vo.InnerTaskOperateVO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/30
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InnerApproveService {

    private static final int APPROVE_ONE = 1;

    private final FeignInnerApprovalRpc feignInnerApprovalRpc;
    private final IInnerApproveService innerApproveService;
    private final IOperateLogService operateLogService;
    private final ApplicationEventPublisher eventPublisher;

    /*
     * <AUTHOR>
     * @Description 用于发起站内审批，此接口不做任何业务判断，调用即认为可发起站内审批请求
     * id: 业务id ruleNo: 审批中心规则编号 formStr: 审批表单数据
     * @Date 2025/5/6
     * @Param [id, ruleNo]
     * @return java.lang.Boolean
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean initiateApproval(@NotNull Long id, @NotNull StationTargetEnum targetEnum,
                                    @NotNull Integer ruleCode, @NotEmpty List<InnerApproveTemplateParam> params) {

        //上线临时补充
        params = supplementData(params);
        //调用站内发起审批接口
        ApprovalInitiateParam approvalInitiateParam = new ApprovalInitiateParam();
        approvalInitiateParam.setRuleCode(ruleCode);
        approvalInitiateParam.setFrom(JSONUtil.toJsonStr(params));
        approvalInitiateParam.setDepartId(UserThreadLocal.getDeptId());
        log.info("发起站内审批参数:{}", JSONUtil.toJsonStr(approvalInitiateParam));
        ResultTemplate<String> stringResultTemplate = feignInnerApprovalRpc.initiateApproval(approvalInitiateParam);
        log.info("发起站内审批结果:{}", JSONUtil.toJsonStr(stringResultTemplate));
        if (!stringResultTemplate.getSuccess() || Strings.isBlank(stringResultTemplate.getData())) {
            throw new BusinessException(stringResultTemplate.getMsg());
        }
        //保存业务Id和审批单号关联关系
        Boolean approveSaved = saveInnerApprove(id, UserThreadLocal.getDeptId(), stringResultTemplate.getData(), targetEnum);
        //保存操作日志
        Boolean operateLogSaved = saveOperateLog(id, InnerApproveTypeEnum.INITIATE_APPROVE, targetEnum);
        if (!approveSaved || !operateLogSaved) {
            throw new BusinessException("发起站内审批保存失败");
        }

        return Boolean.TRUE;
    }

    private List<InnerApproveTemplateParam> supplementData(List<InnerApproveTemplateParam> params) {

        List<InnerApproveTemplateParam> list = new ArrayList<>(params);
        list.add(InnerApproveTemplateParam.builder()
                .key("userType")
                .value(UserThreadLocal.getUser().getType().getCode().toString())
                .type(ApproveFieldTypeEnum.NUMBER.getCode())
                .build());
        return list;
    }

    /*
     * <AUTHOR>
     * @Description 审批同意
     * @Date 2025/5/23
     * @Param [id, instanceCode, comment, targetEnum]
     * @return java.lang.Boolean
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean agree(@NotNull Long id, @NotBlank String instanceCode, String comment, LocalDate applyDelayDate, @NotNull StationTargetEnum targetEnum) {

        //调用站内审批接口
        InnerApproveOpinionParam param = new InnerApproveOpinionParam();
        param.setInstanceCode(instanceCode);
        param.setComment(comment);
        log.info("通过审批参数:{}", JSONUtil.toJsonStr(param));
        ResultTemplate<InnerTaskOperateVO> stringResultTemplate = feignInnerApprovalRpc.agreeApproval(param);
        log.info("通过审批结果:{}", JSONUtil.toJsonStr(stringResultTemplate));
        if (!stringResultTemplate.getSuccess() || Objects.isNull(stringResultTemplate.getData())) {
            throw new BusinessException(stringResultTemplate.getMsg());
        }
        InnerTaskOperateVO innerTaskOperateVO = stringResultTemplate.getData();
        //申请延期至需要特殊处理
        innerTaskOperateVO.setApplyDelayDate(applyDelayDate);
        //审批操作后续操作
        notifyOnApproval(id, targetEnum, InnerApproveOpinionTypeEnum.AGREE, innerTaskOperateVO);

        return Boolean.TRUE;
    }

    /*
     * <AUTHOR>
     * @Description 审批拒绝
     * @Date 2025/5/23
     * @Param [id, instanceCode, comment, targetEnum]
     * @return java.lang.Boolean
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean reject(@NotNull Long id, @NotBlank String instanceCode, String comment, @NotNull StationTargetEnum targetEnum) {

        //调用站内审批接口
        InnerApproveOpinionParam param = new InnerApproveOpinionParam();
        param.setInstanceCode(instanceCode);
        param.setComment(comment);
        log.info("拒绝审批参数:{}", JSONUtil.toJsonStr(param));
        ResultTemplate<InnerTaskOperateVO> stringResultTemplate = feignInnerApprovalRpc.rejectApproval(param);
        log.info("拒绝审批结果:{}", JSONUtil.toJsonStr(stringResultTemplate));
        if (!stringResultTemplate.getSuccess() || Objects.isNull(stringResultTemplate.getData())) {
            throw new BusinessException(stringResultTemplate.getMsg());
        }
        //审批操作后续操作
        notifyOnApproval(id, targetEnum, InnerApproveOpinionTypeEnum.REJECT, stringResultTemplate.getData());

        return Boolean.TRUE;
    }

    /*
     * <AUTHOR>
     * @Description 查询审批节点
     * @Date 2025/5/23
     * @Param [instanceCode]
     * @return java.util.List<com.coocaa.cheese.crm.rpc.vo.InnerApproveNodeVO>
     **/
    public List<InnerApproveNodeVO> queryNodes(String instanceCode) {

        ResultTemplate<List<InnerApproveNodeVO>> resultTemplate = feignInnerApprovalRpc.queryNodes(instanceCode);
        log.info("查询审批节点结果instanceCode:{} data:{}", instanceCode, JSONUtil.toJsonStr(resultTemplate));
        if (!resultTemplate.getSuccess() || Objects.isNull(resultTemplate.getData())) {
            return Collections.emptyList();
        }
        return resultTemplate.getData();
    }

    /*
     * <AUTHOR>
     * @Description 查询审批任务列表
     * @Date 2025/5/23
     * @Param [param, currentPage, pageSize]
     * @return com.coocaa.cheese.crm.common.tools.result.PageResponseVO
     **/
    public PageResponseVO<?> queryTaskPage(@NotNull InnerApproveTaskPageQueryParam param, @NotNull Long currentPage, @NotNull Integer pageSize) {

        PageRequestVO<InnerApproveTaskPageQueryParam> page = new PageRequestVO<>();
        page.setCurrentPage(currentPage);
        page.setPageSize(pageSize);
        page.setQuery(param);
        log.info("查询审批任务列表参数:{}", JSONUtil.toJsonStr(page));
        ResultTemplate<PageResponseVO<?>> resultTemplate = feignInnerApprovalRpc.queryTaskPage(page);
        log.info("查询审批任务列表结果:{}", JSONUtil.toJsonStr(resultTemplate));
        if (!resultTemplate.getSuccess() || Objects.isNull(resultTemplate.getData()) || CollectionUtil.isEmpty(resultTemplate.getData().getRows())) {
            return new PageResponseVO<>();
        }
        return resultTemplate.getData();
    }

    /*
     * <AUTHOR>
     * @Description 查询审批申请列表
     * @Date 2025/5/23
     * @Param [param, currentPage, pageSize]
     * @return com.coocaa.cheese.crm.common.tools.result.PageResponseVO
     **/
    public PageResponseVO<?> queryApplyPage(@NotNull InnerApproveApplyPageQueryParam param, @NotNull Long currentPage, @NotNull Integer pageSize) {

        PageRequestVO<InnerApproveApplyPageQueryParam> page = new PageRequestVO<>();
        page.setCurrentPage(currentPage);
        page.setPageSize(pageSize);
        page.setQuery(param);
        log.info("查询审批申请列表参数:{}", JSONUtil.toJsonStr(page));
        ResultTemplate<PageResponseVO<?>> resultTemplate = feignInnerApprovalRpc.queryApplyPage(page);
        log.info("查询审批申请列表结果:{}", JSONUtil.toJsonStr(resultTemplate));
        if (!resultTemplate.getSuccess() || Objects.isNull(resultTemplate.getData()) || CollectionUtil.isEmpty(resultTemplate.getData().getRows())) {
            return new PageResponseVO<>();
        }
        return resultTemplate.getData();
    }

    /*
     * <AUTHOR>
     * @Description 查询审批实例待完成的任务
     * @Date 2025/5/23
     * @Param [instanceCode]
     * @return com.coocaa.cheese.crm.rpc.vo.InnerInstanceTaskVO
     **/
    public InnerInstanceTaskVO queryTask(String instanceCode) {

        ResultTemplate<InnerInstanceTaskVO> instanceTask = feignInnerApprovalRpc.queryTask(instanceCode);
        log.info("查询审批任务结果instanceCode:{}, data:{}", instanceCode, JSONUtil.toJsonStr(instanceTask));
        if (!instanceTask.getSuccess() || Objects.isNull(instanceTask.getData())) {
            return null;
        }
        return instanceTask.getData();
    }

    /*
     * <AUTHOR>
     * @Description 查询审批单详情
     * @Date 2025/5/23
     * @Param [instanceCode]
     * @return com.coocaa.cheese.crm.rpc.vo.InnerApproveInstanceVO
     **/
    public InnerApproveInstanceVO queryDetail(String instanceCode) {

        ResultTemplate<InnerApproveInstanceVO> detail = feignInnerApprovalRpc.queryDetail(instanceCode);
        log.info("查询审批详情结果instanceCode:{}, data:{}", instanceCode, JSONUtil.toJsonStr(detail));
        if (!detail.getSuccess() || Objects.isNull(detail.getData())) {
            return null;
        }
        return detail.getData();
    }

    private Boolean saveInnerApprove(Long id, String departmentId, String instanceCode, StationTargetEnum stationTargetEnum) {

        InnerApproveEntity entity = new InnerApproveEntity();
        entity.setBizId(id);
        entity.setInstanceCode(instanceCode);
        entity.setType(stationTargetEnum.getCode());
        entity.setDepartmentId(departmentId);
        entity.setResult(InnerApproveOpinionTypeEnum.PENDING.getCode());
        return innerApproveService.save(entity);
    }

    private Boolean saveOperateLog(Long id, InnerApproveTypeEnum operateTypeEnum, StationTargetEnum targetEnum) {

        OperateLogEntity operateLogEntity = new OperateLogEntity();
        operateLogEntity.setType(APPROVE_ONE);
        operateLogEntity.setSubType(targetEnum.getCode());
        operateLogEntity.setBizId(id);
        operateLogEntity.setOperateType(operateTypeEnum.getCode());
        operateLogEntity.setOperateTime(LocalDateTime.now());
        return operateLogService.save(operateLogEntity);
    }

    private void notifyOnApproval(Long id, StationTargetEnum targetEnum, InnerApproveOpinionTypeEnum approveType, InnerTaskOperateVO innerTaskOperateVO) {

        //发送事件
        eventPublisher.publishEvent(
                new InnerApproveEvent(
                        this,
                        id,
                        targetEnum,
                        approveType,
                        innerTaskOperateVO
                )
        );
        //保存操作日志
        Boolean operateLogSaved = saveOperateLog(id, Objects.equals(approveType, InnerApproveOpinionTypeEnum.AGREE)
                ? InnerApproveTypeEnum.APPROVE_PASS : InnerApproveTypeEnum.APPROVE_REJECT, targetEnum);
        if (!operateLogSaved) {
            throw new BusinessException("站内审批保存失败");
        }
    }
}
