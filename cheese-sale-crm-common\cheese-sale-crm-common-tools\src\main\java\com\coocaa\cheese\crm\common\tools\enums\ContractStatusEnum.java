package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 合同状态枚举(字典0079)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum ContractStatusEnum implements IEnumType<String> {
    ACTIVE("0079-1", "待提交"),
    EXAMINE("0079-2", "审核中"),
    TREAT_INITIATE_CONTRACT("0079-3", "待发起合同"),
    TREAT_OUR_STAMP("0079-4", "待我方盖章"),
    TREAT_THEIR_STAMP("0079-5", "待对方盖章"),
    IN_PROGRESS("0079-6", "执行中"),
    ALREADY_SIGN("0079-7", "已签约(待执行)"),
    ALREADY_BECOME_DUE("0079-8", "已到期"),
    ALREADY_REJECT("0079-9", "已驳回"),
    ALREADY_CANCEL("0079-10", "已作废");

    private final String code;
    private final String desc;

    private static final Map<String, ContractStatusEnum> BY_CODE_MAP =
            Arrays.stream(ContractStatusEnum.values())
                    .collect(Collectors.toMap(ContractStatusEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static ContractStatusEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static ContractStatusEnum parse(String code, ContractStatusEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ContractStatusEnum::getDesc).orElse(StringUtils.EMPTY);
    }

    public static boolean canCreateEvidence(String code) {
        // 待发起合同/待我方盖章/待对方盖章/已签约(待执行)/执行中  才能创建定版单
        if (StringUtils.isBlank(code)) {
            return false;
        }
        return TREAT_INITIATE_CONTRACT.getCode().equals(code) || TREAT_OUR_STAMP.getCode().equals(code)
                || TREAT_THEIR_STAMP.getCode().equals(code) || IN_PROGRESS.getCode().equals(code)
                || ALREADY_SIGN.getCode().equals(code);
    }
} 