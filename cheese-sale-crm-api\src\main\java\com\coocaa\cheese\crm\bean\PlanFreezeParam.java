package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02
 */
@Data
@Schema(description = "方案转销售入参")
public class PlanFreezeParam {

    @Schema(description = "机构账户ID", type = "Integer", example = "1")
    @NotNull(message = "机构账户ID不能为空")
    private Integer institutionId;

    @NotNull(message = "计费方式不能为空")
    @Schema(description = "计费方式字典(按天、按周)", type = "0087-1")
    private String billingType;


    @Schema(description = "投放周期", type = "Integer", example = "投放周期")
    private Integer launchCycle;

    @NotNull(message = "方案id不能为空")
    @Schema(description = "方案id", type = "Integer", example = "8")
    private Integer planId;

    @NotBlank(message = "方案名称不能为空")
    @Schema(description = "方案名称", type = "String", example = "方案1")
    private String planName;

    @NotBlank(message = "客户名称不能为空")
    @Schema(description = "客户名称", type = "String", example = "客户名称")
    private String customerName;

    @NotNull(message = "开始日期不能为空")
    @Schema(description = "开始日期", type = "LocalDate", example = "2025-04-02")
    private LocalDate startDate;

    @NotNull(message = "结束日期不能为空")
    @Schema(description = "结束日期", type = "LocalDate", example = "2025-04-02")
    private LocalDate endDate;
}
