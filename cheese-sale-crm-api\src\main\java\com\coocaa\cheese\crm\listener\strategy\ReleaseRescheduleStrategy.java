package com.coocaa.cheese.crm.listener.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.InnerApproveEntity;
import com.coocaa.cheese.crm.common.db.entity.ReleaseRescheduleEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.db.service.IInnerApproveService;
import com.coocaa.cheese.crm.common.db.service.IReleaseRescheduleService;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveOpinionTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.InnerApproveStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.ReleaseFailReasonEnum;
import com.coocaa.cheese.crm.common.tools.enums.ReleaseRescheduleExecuteStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.listener.event.InnerApproveEvent;
import com.coocaa.cheese.crm.listener.strategy.approval.ApprovalStrategy;
import com.coocaa.cheese.crm.listener.strategy.approval.StationTargetAware;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/5/7
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ReleaseRescheduleStrategy implements ApprovalStrategy, StationTargetAware {

    private static final int APPROVE_ZERO = 0;

    private final IBusinessService businessService;
    private final IInnerApproveService innerApproveService;
    private final IReleaseRescheduleService releaseRescheduleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(InnerApproveEvent event) {

        log.info("释放改期审批通知信息:{}", JSONUtil.toJsonStr(event));
        Integer rescheduleId = event.getId().intValue();
        if (Objects.isNull(rescheduleId)) {
            throw new BusinessException("释放改期记录id为空");
        }
        //延期申请审批同意增加维护期望延期至日期
        updateApplyDelayDate(rescheduleId, event);

        ReleaseRescheduleEntity rescheduleEntity = Optional.ofNullable(releaseRescheduleService.lambdaQuery()
                .eq(ReleaseRescheduleEntity::getId, rescheduleId)
                .eq(ReleaseRescheduleEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one()).orElseThrow(() -> new BusinessException("释放改期记录不存在!"));
        log.info("延迟申请审批通知释放改期信息查询:{}", JSONUtil.toJsonStr(rescheduleEntity));
        if (InnerApproveOpinionTypeEnum.AGREE.equals(event.getOpinionTypeEnum())
                && (StringUtils.isNotBlank(event.getInnerTaskOperateVO().getApprovalStatus())
                && InnerApproveStatusEnum.ALREADY_FINISH.getCode().equals(event.getInnerTaskOperateVO().getApprovalStatus()))) {
            // 审批通过
            agree(rescheduleEntity);
        } else if (InnerApproveOpinionTypeEnum.REJECT.equals(event.getOpinionTypeEnum())) {
            // 审批不通过
            reject(rescheduleEntity);
        }
    }

    public void agree(ReleaseRescheduleEntity rescheduleEntity) {

        //查询商机信息
        BusinessEntity businessEntity = Optional.ofNullable(businessService.lambdaQuery()
                .eq(BusinessEntity::getId, rescheduleEntity.getBusinessId())
                .one()).orElseThrow(() -> new BusinessException("延迟申请审批通知商机不存在!"));
        log.info("延迟申请审批通知商机查询:{}", JSONUtil.toJsonStr(businessEntity));
        //获取失败理由
        String failReason = getFailReason(rescheduleEntity, businessEntity);
        //修改商机信息
        if (Strings.isBlank(failReason)) {
            boolean updateBusiness;
            if (rescheduleEntity.getApplyDelayDate().isAfter(LocalDate.now())) {
                updateBusiness = businessService.lambdaUpdate()
                        .eq(BusinessEntity::getId, rescheduleEntity.getBusinessId())
                        .set(BusinessEntity::getStatus, BusinessStatusEnum.ACTIVE.getCode())
                        .set(BusinessEntity::getAdvertisingReleaseFlag, APPROVE_ZERO)
                        .set(BusinessEntity::getAdvertisingReleaseDate, rescheduleEntity.getApplyDelayDate())
                        .update();
            } else {
                updateBusiness = businessService.lambdaUpdate()
                        .eq(BusinessEntity::getId, rescheduleEntity.getBusinessId())
                        .set(BusinessEntity::getAdvertisingReleaseDate, rescheduleEntity.getApplyDelayDate())
                        .update();
            }
            boolean updated = releaseRescheduleService.lambdaUpdate()
                    .eq(ReleaseRescheduleEntity::getId, rescheduleEntity.getId())
                    .set(ReleaseRescheduleEntity::getExecuteStatus, ReleaseRescheduleExecuteStatusEnum.ALREADY_EXECUTE.getCode())
                    .set(ReleaseRescheduleEntity::getStatusChangeTime, LocalDateTime.now())
                    .update();
            if (!updateBusiness || !updated) {
                throw new BusinessException("延迟申请审批通知更新商机失败!");
            }
        } else {
            //更新释放改期执行状态
            boolean updated = releaseRescheduleService.lambdaUpdate()
                    .eq(ReleaseRescheduleEntity::getId, rescheduleEntity.getId())
                    .set(ReleaseRescheduleEntity::getExecuteStatus, ReleaseRescheduleExecuteStatusEnum.EXECUTE_FAIL.getCode())
                    .set(ReleaseRescheduleEntity::getFailReason, failReason)
                    .set(ReleaseRescheduleEntity::getStatusChangeTime, LocalDateTime.now())
                    .update();
            if (!updated) {
                throw new BusinessException("延迟申请审批通知更新释放改期记录失败!");
            }
        }
        //修改站内审批业务关联表
        updateInnerApprove(rescheduleEntity.getId(), InnerApproveOpinionTypeEnum.AGREE);
    }

    private String getFailReason(ReleaseRescheduleEntity rescheduleEntity, BusinessEntity businessEntity) {

        //若审批提交记录的【申请人】=当前商机实时的【归属人】
        // & 当前商机的签约主体不存在其他保护中的商机（商机ID与当前不同）
        // &【期望延期至】＞【主体释放日期】，则《释放延期申请记录》【执行状态】由“待定”改为“已执行”
        if (!rescheduleEntity.getCreator().equals(businessEntity.getOwnerId())) {
            return ReleaseFailReasonEnum.BUSINESS_OWNER_CHANGE.getCode();
        }
        if (Objects.nonNull(businessEntity.getAdvertisingSubjectId())) {
            List<BusinessEntity> list = businessService.lambdaQuery()
                    .eq(BusinessEntity::getAdvertisingSubjectId, businessEntity.getAdvertisingSubjectId())
                    .eq(BusinessEntity::getAdvertisingReleaseFlag, APPROVE_ZERO)
                    .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .ne(BusinessEntity::getId, rescheduleEntity.getBusinessId())
                    .list();
            if (CollectionUtil.isNotEmpty(list)) {
                return ReleaseFailReasonEnum.SIGN_SUBJECT_FOLLOW.getCode();
            }
        }
        if (!rescheduleEntity.getApplyDelayDate().isAfter(businessEntity.getAdvertisingReleaseDate())) {
            return ReleaseFailReasonEnum.RELEASE_DATE_GREATER_THAN_DELAY_DATE.getCode();
        }
        return Strings.EMPTY;
    }

    public void reject(ReleaseRescheduleEntity rescheduleEntity) {

        releaseRescheduleService.lambdaUpdate()
                .eq(ReleaseRescheduleEntity::getId, rescheduleEntity.getId())
                .set(ReleaseRescheduleEntity::getExecuteStatus, ReleaseRescheduleExecuteStatusEnum.ALREADY_CANCEL.getCode())
                .update();

        //修改站内审批业务关联表
        updateInnerApprove(rescheduleEntity.getId(), InnerApproveOpinionTypeEnum.REJECT);
    }

    public void updateApplyDelayDate(Integer id, InnerApproveEvent event) {

        if (Objects.equals(InnerApproveOpinionTypeEnum.AGREE.getCode(), event.getOpinionTypeEnum().getCode())
                && Objects.nonNull(event.getInnerTaskOperateVO().getApplyDelayDate())) {
            releaseRescheduleService.lambdaUpdate()
                    .set(ReleaseRescheduleEntity::getApplyDelayDate, event.getInnerTaskOperateVO().getApplyDelayDate())
                    .eq(ReleaseRescheduleEntity::getId, id)
                    .update();
        }
    }

    public void updateInnerApprove(Integer id, InnerApproveOpinionTypeEnum innerApproveOpinionTypeEnum) {

        innerApproveService.lambdaUpdate()
                .set(InnerApproveEntity::getResult, innerApproveOpinionTypeEnum.getCode())
                .set(InnerApproveEntity::getCloseTime, LocalDateTime.now())
                .eq(InnerApproveEntity::getBizId, id.longValue())
                .update();
    }

    @Override
    public StationTargetEnum getTargetEnum() {
        return StationTargetEnum.BUSINESS_DELAY;
    }
}
