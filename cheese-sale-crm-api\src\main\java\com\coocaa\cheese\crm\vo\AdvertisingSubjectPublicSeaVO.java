package com.coocaa.cheese.crm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 签约主体公海VO
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
@Accessors(chain = true)
@Schema(name = "AdvertisingSubjectPublicSeaVO", description = "签约主体公海VO")
public class AdvertisingSubjectPublicSeaVO {

    @Schema(description = "签约主体ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "品牌ID", type = "Integer", example = "1")
    private Integer brandId;
    private String brandName;

    @Schema(description = "所属公司ID", type = "Integer", example = "1")
    private Integer companyId;
    private String companyName;

    @Schema(description = "产品线", type = "String", example = "1")
    private String productLineNames;

}
