package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 主品牌信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BrandVO", description = "主品牌信息VO")
public class BrandH5VO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "品牌名称", type = "String", example = "可口可乐")
    private String name;

    @Schema(description = "所属行业", type = "String", example = "FOOD")
    @TransField(type = TransTypes.BRAND)
    private Integer parentId;
    private String  parentName;

    @Schema(description = "所属行业", type = "String", example = "FOOD")
    @TransField(type = TransTypes.INDUSTRY)
    private String industryCode;
    private String industryName;

    @Schema(description = "所属公司ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;
} 