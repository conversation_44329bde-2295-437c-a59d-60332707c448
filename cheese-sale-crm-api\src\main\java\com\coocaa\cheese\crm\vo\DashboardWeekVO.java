package com.coocaa.cheese.crm.vo;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 周信息VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-09
 */
@Data
public class DashboardWeekVO {

    @Schema(description = "开始日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate endDate;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "周数")
    private Integer weekNumber;

    @Schema(description = "审批通过总数")
    private Integer approvedCount;
}
