package com.coocaa.cheese.crm.task;

import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessChangeStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessProgressEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessStatusEnum;
import com.coocaa.cheese.crm.service.BusinessStatusChangeLogService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 即将结束保护的定时任务
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessReleaseReminder {

    private final IBusinessService businessService;
    private final BusinessStatusChangeLogService businessStatusChangeLogService;

    @XxlJob("businessWillReleaseJob")
    public void executeWillRelease() {
        // 获取所有即将释放的商机数量
        LocalDate now = LocalDate.now();
        LocalDate tenDaysLater = now.plusDays(3);
        Long count = businessService.lambdaQuery()
                .eq(BusinessEntity::getStatus, BusinessStatusEnum.ACTIVE.getCode())
                .gt(BusinessEntity::getAdvertisingReleaseDate, now)
                .le(BusinessEntity::getAdvertisingReleaseDate, tenDaysLater)
                .count();
        // TODO 暂时没有处理飞书通知，先注释掉
//        if (count > 0) {
//            // 发送飞书通知
//            String message = String.format("有%d条商机的签约主体，将在3天内过独占保护期，请至销售工作台查看！点击打开", count);
//            feishuService.sendNotification(message);
//        }
    }

    @XxlJob("businessReleaseJob")
    public void executeRelease() {
        LocalDate now = LocalDate.now();
        
        // 1. 查询所有到期的商机（释放日期<=当前日期 且 未释放的）
        List<BusinessEntity> expiredBusinesses = businessService.lambdaQuery()
                .eq(BusinessEntity::getStatus, BusinessStatusEnum.ACTIVE.getCode())
                .eq(BusinessEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .le(BusinessEntity::getAdvertisingReleaseDate, now)
                .eq(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.NO.getCode())
                .list();
        
        if (expiredBusinesses.isEmpty()) {
            return;
        }
        
        // 2. 根据进度分组处理
        // 初步接洽 -> 直接释放
        List<Integer> toReleaseIds = expiredBusinesses.stream()
                .filter(business -> BusinessProgressEnum.INITIAL.getCode().equals(business.getProgress()))
                .map(BusinessEntity::getId)
                .toList();
        
        // 高意向/商务推进/成交 -> 休眠中
        List<BusinessEntity> toDormantBusinesses = expiredBusinesses.stream()
                .filter(business -> !BusinessProgressEnum.INITIAL.getCode().equals(business.getProgress()))
                .toList();
        
        // 3. 更新状态
        // 直接释放的商机
        if (!toReleaseIds.isEmpty()) {
            businessService.lambdaUpdate()
                    .set(BusinessEntity::getAdvertisingReleaseFlag, BooleFlagEnum.YES.getCode())
                    .set(BusinessEntity::getUpdateTime, now)
                    .set(BusinessEntity::getOperator, 0)
                    .in(BusinessEntity::getId, toReleaseIds)
                    .update();
        }
        
        // 需要休眠的商机
        if (!toDormantBusinesses.isEmpty()) {
            List<Integer> dormantIds = toDormantBusinesses.stream()
                    .map(BusinessEntity::getId)
                    .toList();
            
            businessService.lambdaUpdate()
                    .set(BusinessEntity::getStatus, BusinessStatusEnum.DORMANT.getCode())
                    .set(BusinessEntity::getUpdateTime, now)
                    .set(BusinessEntity::getOperator, 0)
                    .in(BusinessEntity::getId, dormantIds)
                    .update();
            
            // 4. 保存状态变更记录
            for (BusinessEntity businessEntity : toDormantBusinesses) {
                businessStatusChangeLogService.saveBusinessStatusChange(
                        BusinessChangeStatusEnum.STATUS,
                        businessEntity,
                        BusinessStatusEnum.DORMANT.getCode(),
                        "商机状态变更为休眠中"
                );
            }
        }
    }
}
