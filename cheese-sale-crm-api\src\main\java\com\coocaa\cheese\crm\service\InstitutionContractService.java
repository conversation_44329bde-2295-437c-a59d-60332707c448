package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.InstitutionContractParam;
import com.coocaa.cheese.crm.bean.InstitutionContractQueryParam;
import com.coocaa.cheese.crm.common.db.entity.InstitutionContractEntity;
import com.coocaa.cheese.crm.common.db.service.IInstitutionContractService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.convert.InstitutionContractConvert;
import com.coocaa.cheese.crm.vo.InstitutionContractVO;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * 机构合同服务
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionContractService {
    private static final long CONSTANT_ZERO_L = 0L;
    private final IInstitutionContractService institutionContractService;

    /**
     * 分页查询机构合同列表
     *
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    @AutoTranslate
    public PageResponseVO<InstitutionContractVO> pageListInstitutionContract(PageRequestVO<InstitutionContractQueryParam> pageRequest) {
        if (Objects.isNull(pageRequest.getQuery().getInstitutionId())) {
            throw new BusinessException("机构账户ID不能为空");
        }
        // 构建分页对象
        Page<InstitutionContractEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));

        // 构建查询条件
        InstitutionContractQueryParam query = pageRequest.getQuery();
        IPage<InstitutionContractEntity> pageResult = institutionContractService.lambdaQuery()
                .eq(Objects.nonNull(query.getInstitutionId()), InstitutionContractEntity::getInstitutionId, query.getInstitutionId())
                .like(StringUtils.isNotBlank(query.getContractCode()), InstitutionContractEntity::getContractCode, query.getContractCode())
                .eq(Objects.nonNull(query.getEffectiveFlag()), InstitutionContractEntity::getEffectiveFlag, query.getEffectiveFlag())
                .eq(InstitutionContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(InstitutionContractEntity::getUpdateTime)
                .page(page);

        // 转换为VO
        return InstitutionContractConvert.INSTANCE.toPageResponse(pageResult);
    }

    /**
     * 查询合同详情
     *
     * @param id 合同ID
     * @return 合同详情
     */
    @AutoTranslate
    public InstitutionContractVO detail(Integer id) {
        // 查询机构合同列表
        InstitutionContractEntity contract = institutionContractService.lambdaQuery()
                .eq(InstitutionContractEntity::getId, id)
                .eq(InstitutionContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();

        // 转换为VO
        return InstitutionContractConvert.INSTANCE.toVo(contract);
    }

    /**
     * 上传机构合同
     *
     * @param param 合同参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadInstitutionContract(InstitutionContractParam param) {

        InstitutionContractEntity existContract = institutionContractService.lambdaQuery()
                .eq(InstitutionContractEntity::getContractCode, param.getContractCode())
                .eq(InstitutionContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        if (Objects.nonNull(existContract) && !Objects.equals(param.getInstitutionId(), existContract.getInstitutionId())) {
            throw new BusinessException("合同编号已存在于其他机构，请更换合同编号");
        }
        // 转换为实体
        InstitutionContractEntity entity = InstitutionContractConvert.INSTANCE.toEntity(param);
        if (Objects.isNull(existContract)) {
            //新增
            //查询是否该机构下有合同了，没有，第一条默认生效
            entity.setEffectiveFlag(effectiveContract(param.getInstitutionId()));
            entity.setDeleteFlag(BooleFlagEnum.NO.getCode());

            // 保存合同
            return institutionContractService.save(entity);
        } else {
            //修改
            return institutionContractService.lambdaUpdate()
                    .set(InstitutionContractEntity::getSignDate, param.getSignDate())
                    .set(InstitutionContractEntity::getFileUrl, param.getFileUrl())
                    .set(InstitutionContractEntity::getDescription, param.getDescription())
                    .set(InstitutionContractEntity::getUpdateTime, LocalDateTime.now())
                    .eq(InstitutionContractEntity::getInstitutionId, param.getInstitutionId())
                    .eq(InstitutionContractEntity::getContractCode, param.getContractCode())
                    .update();
        }
    }

    /**
     * 设置当前生效合同
     *
     * @param id 合同ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean setEffectiveContract(Integer id) {
        // 查询合同
        InstitutionContractEntity contract = institutionContractService.getById(id);
        if (contract == null) {
            throw new BusinessException("合同不存在");
        }

        // 将该机构的所有合同设置为非生效
        institutionContractService.lambdaUpdate()
                .set(InstitutionContractEntity::getEffectiveFlag, BooleFlagEnum.NO.getCode())
                .eq(InstitutionContractEntity::getInstitutionId, contract.getInstitutionId())
                .update();

        // 将当前合同设置为生效
        contract.setEffectiveFlag(BooleFlagEnum.YES.getCode());
        return institutionContractService.updateById(contract);
    }

    /**
     * 获取机构当前生效合同编号
     *
     * @param institutionId 机构账户ID
     * @return 合同编号
     */
    public String getEffectiveContractCode(Integer institutionId) {
        InstitutionContractEntity contract = institutionContractService.lambdaQuery()
                .eq(InstitutionContractEntity::getInstitutionId, institutionId)
                .eq(InstitutionContractEntity::getEffectiveFlag, BooleFlagEnum.YES.getCode())
                .eq(InstitutionContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .last("limit 1")
                .one();
        
        return contract != null ? contract.getContractCode() : "";
    }

    private Integer effectiveContract(@NotNull(message = "机构账户ID不能为空") Integer institutionId) {

        return institutionContractService.lambdaQuery()
                .eq(InstitutionContractEntity::getInstitutionId, institutionId)
                .eq(InstitutionContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .count() == CONSTANT_ZERO_L ? BooleFlagEnum.YES.getCode() : BooleFlagEnum.NO.getCode();
    }
} 