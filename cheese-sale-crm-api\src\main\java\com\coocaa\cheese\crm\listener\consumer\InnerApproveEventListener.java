package com.coocaa.cheese.crm.listener.consumer;

import cn.hutool.json.JSONUtil;
import com.coocaa.cheese.crm.listener.strategy.approval.ApprovalStrategy;
import com.coocaa.cheese.crm.listener.strategy.approval.ApprovalStrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.coocaa.cheese.crm.listener.event.InnerApproveEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InnerApproveEventListener {

    private final ApprovalStrategyFactory strategyFactory;

    /*
     * <AUTHOR>
     * @Description 站内审批监听
     * @Date 2025/5/23
     * @Param [event]
     * @return void
     **/
    @EventListener
    public void handleEvent(InnerApproveEvent event) {
        log.info("InnerApproveEventListener.handleEvent: {}", JSONUtil.toJsonStr(event));
        ApprovalStrategy strategy = strategyFactory.getStrategy(event.getTargetEnum());
        strategy.execute(event);
    }
}
