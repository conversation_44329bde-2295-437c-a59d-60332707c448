<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.cheese.crm.common.db.mapper.InstitutionFundChangeRecordMapper">
    <!-- 按条件查询资金变动记录列表 -->
    <select id="pageList" resultType="com.coocaa.cheese.crm.common.db.entity.InstitutionFundChangeRecordEntity">
        SELECT ifcr.id, ifcr.fund_account_id, ifcr.change_amount, ifcr.change_type, ifcr.change_reason,
               ifcr.change_remark, ifcr.voucher_url, ifcr.plan_id, ifcr.creation_method, ifcr.change_time,
               ifcr.creator, ifcr.create_time, ifcr.operator, ifcr.update_time
        FROM sale_crm_institution_fund_change_record ifcr
        <include refid="joinSql"/>
        <include refid="whereSql"/>
        ORDER BY ifcr.change_time DESC
    </select>

    <!-- 根据条件统计资金变动记录数量 -->
    <select id="pageList_COUNT" resultType="java.lang.Integer">
        SELECT count(1)
        FROM sale_crm_institution_fund_change_record ifcr
        <include refid="joinSql"/>
        <include refid="whereSql"/>
    </select>

    <!-- 资金变动记录列表查询的公共部分提取出来 -->
    <sql id="joinSql">
        <if test="condition.institutionId != null">
            LEFT JOIN sale_crm_institution_fund_account ifa ON ifcr.fund_account_id = ifa.id
        </if>
    </sql>

    <sql id="whereSql">
        <where>
            AND ifcr.delete_flag = 0
            <if test="condition.fundAccountId != null">
                AND ifcr.fund_account_id = #{condition.fundAccountId}
            </if>
            <if test="condition.changeType != null and condition.changeType != ''">
                AND ifcr.change_type = #{condition.changeType}
            </if>
            <if test="condition.changeReason != null and condition.changeReason != ''">
                AND ifcr.change_reason = #{condition.changeReason}
            </if>
            <if test="condition.planId != null">
                AND ifcr.plan_id = #{condition.planId}
            </if>
            <if test="condition.creationMethod != null">
                AND ifcr.creation_method = #{condition.creationMethod}
            </if>
            <if test="condition.startTime != null">
                AND ifcr.change_time >= #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                AND ifcr.change_time &lt;= #{condition.endTime}
            </if>
            <if test="condition.institutionId != null">
                AND ifa.institution_id = #{condition.institutionId}
            </if>
        </where>
    </sql>
</mapper> 