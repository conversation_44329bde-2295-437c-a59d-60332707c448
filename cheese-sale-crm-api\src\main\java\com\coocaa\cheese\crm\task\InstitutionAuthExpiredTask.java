package com.coocaa.cheese.crm.task;

import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.db.entity.InstitutionAccountEntity;
import com.coocaa.cheese.crm.common.db.service.IInstitutionAccountService;
import com.coocaa.cheese.crm.common.tools.enums.AccountDisableReasonEnum;
import com.coocaa.cheese.crm.common.tools.enums.AccountStatusEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * 机构授权过期状态定时任务
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionAuthExpiredTask {
    private final IInstitutionAccountService institutionAccountService;

    /**
     * 每天凌晨执行
     */
    @XxlJob("updateAuthExpiredStatusJob")
    public void updateAuthExpiredStatus() {
        log.info("开始执行机构授权过期状态更新任务");
        LocalDate today = LocalDate.now();

        try {
            // 查询所有未停用但授权截止日期已过期的机构账户
            List<InstitutionAccountEntity> expiredAccounts = institutionAccountService.lambdaQuery()
                    // 未停用
                    .eq(InstitutionAccountEntity::getAccountStatus, AccountStatusEnum.NORMAL.getCode())
                    // 授权截止日期已过期
                    .lt(InstitutionAccountEntity::getAuthEndDate, today)
                    .eq(InstitutionAccountEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .list();

            log.info("找到{}个需要更新为停用状态的机构账户", expiredAccounts.size());

            // 批量更新为停用状态，并设置停用原因为"授权到期"
            for (InstitutionAccountEntity account : expiredAccounts) {
                // 设置为停用
                account.setAccountStatus(AccountStatusEnum.DISABLED.getCode());
                // 设置停用原因为"授权到期"
                account.setDisableReason(AccountDisableReasonEnum.AUTHORIZATION_EXPIRED.getCode());
                institutionAccountService.updateById(account);
            }
            log.info("机构授权过期状态更新任务执行完成");
        } catch (Exception e) {
            log.error("机构授权过期状态更新任务执行异常", e);
            throw e;
        }
    }
} 