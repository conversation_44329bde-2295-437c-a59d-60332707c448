package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 品牌管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
public class BrandParam {

    @Schema(description = "母品牌", type = "Integer", example = "123")
    private Integer parentId;

    @NotBlank(message = "品牌名称不能为空")
    @Size(max = 20, message = "品牌名称不能超过{max}个字符")
    @Schema(description = "品牌名称", type = "String", example = "梅赛德斯")
    private String name;

    @NotBlank(message = "行业不能为空")
    @Schema(description = "二级行业编码", type = "String", example = "001-02")
    private String industryCode;

    @Schema(description = "持有公司ID", type = "Integer", example = "123")
    private Integer companyId;

    @Schema(description = "品牌Logo", type = "String", example = "http://example.com/logo.png")
    private String logoUrl;

    @Schema(description = "品牌标签", type = "List<String>", example = "奔驰")
    private List<String> tags;

    @Schema(description = "生效状态(1:生效,0:不生效)", type = "Integer", example = "1")
    private Integer effectiveStatus;
}
