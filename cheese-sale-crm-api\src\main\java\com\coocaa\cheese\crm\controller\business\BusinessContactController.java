package com.coocaa.cheese.crm.controller.business;

import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.BusinessContactParam;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.BusinessContactService;
import com.coocaa.cheese.crm.vo.BusinessContactVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商机联系人管理
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@RestController
@RequestMapping("/business-contacts")
@Tag(name = "商机联系人管理", description = "商机联系人管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessContactController extends BaseController {
    private final BusinessContactService businessContactService;

    /**
     * 联系人创建
     */
    @Operation(summary = "创建联系人")
    @PostMapping
    public ResultTemplate<Boolean> createContact(@RequestBody @Validated BusinessContactParam contact) {
        return ResultTemplate.success(businessContactService.createOrUpdateContact(null, contact));
    }

    /**
     * 联系人修改
     */
    @Operation(summary = "联系人修改")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> updateContact(@PathVariable("id") Integer id, @RequestBody BusinessContactParam contact) {
        return ResultTemplate.success(businessContactService.createOrUpdateContact(id, contact));
    }

    /**
     * 联系人删除
     */
    @Operation(summary = "联系人删除")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> deleteContact(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessContactService.deleteContact(id));
    }

    /**
     * 联系人详情
     */
    @Operation(summary = "联系人详情")
    @GetMapping("/{id}")
    public ResultTemplate<BusinessContactVO> contactDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(businessContactService.getContactDetail(id));
    }

    /**
     * 获取商机下的所有未删除联系人
     */
    @Operation(summary = "获取商机下的所有未删除联系人")
    @GetMapping("/list")
    public ResultTemplate<List<BusinessContactVO>> listContactsByBusinessId(@RequestParam(name = "businessId") Integer businessId) {
        return ResultTemplate.success(businessContactService.getContactsByBusinessId(businessId));
    }
} 