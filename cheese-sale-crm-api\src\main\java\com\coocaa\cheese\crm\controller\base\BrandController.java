package com.coocaa.cheese.crm.controller.base;

import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.bean.BrandParam;
import com.coocaa.cheese.crm.bean.BrandQueryH5Param;
import com.coocaa.cheese.crm.bean.BrandQueryParam;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.common.tools.util.StringUtils;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.service.BrandService;
import com.coocaa.cheese.crm.vo.BrandH5VO;
import com.coocaa.cheese.crm.vo.BrandVO;
import com.coocaa.cheese.crm.vo.ProductLineDetailVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 品牌管理
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Slf4j
@RestController
@RequestMapping("/brands")
@Tag(name = "品牌管理", description = "品牌管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BrandController extends BaseController {
    private final BrandService brandService;

    /**
     * 品牌下拉列表
     */
    @Operation(summary = "品牌下拉列表")
    @GetMapping("/dropdown")
    public ResultTemplate<List<CodeNameVO>> listBrands(@RequestParam(name = "name", required = false) String name) {
        return ResultTemplate.success(brandService.listBrands(name, false));
    }

    /**
     * 品牌精确查询
     */
    @Operation(summary = "品牌精确查询")
    @GetMapping("/exact")
    public ResultTemplate<CodeNameVO> getBrandByName(@RequestParam(name = "name") String name) {
        if (StringUtils.isBlank(name)) {
            return ResultTemplate.success(null);
        }

        List<CodeNameVO> codeNames = brandService.listBrands(name, true);
        if (CollectionUtils.isEmpty(codeNames)) {
            return ResultTemplate.success(null);
        }

        return ResultTemplate.success(codeNames.get(0));
    }

    /**
     * 根据ID批量查询列表
     */
    @Operation(summary = "根据ID批量查询列表")
    @PostMapping("/list/ids")
    public ResultTemplate<List<CodeNameVO>> listByIds(@RequestBody List<Integer> ids) {
        return ResultTemplate.success(brandService.listByIds(ids));
    }

    /**
     * 品牌列表(分页)
     */
    @Operation(summary = "品牌列表(分页)")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVO<BrandVO>> pageList(@RequestBody PageRequestVO<BrandQueryParam> pageRequest) {
        return ResultTemplate.success(brandService.pageList(pageRequest));
    }


    /**
     * H5品牌列表(分页)
     */
    @Operation(summary = "H5品牌列表(分页)")
    @PostMapping("/h5/page")
    public ResultTemplate<PageResponseVO<BrandH5VO>> h5PageList(@RequestBody PageRequestVO<BrandQueryH5Param> pageRequest) {
        return ResultTemplate.success(brandService.h5PageList(pageRequest));
    }

    /**
     * 品牌详情
     */
    @Operation(summary = "品牌详情")
    @Parameter(name = "id", description = "品牌ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<BrandVO> getDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(brandService.getDetail(id));
    }

    /**
     * 品牌创建
     */
    @Operation(summary = "创建品牌")
    @PostMapping
    public ResultTemplate<Boolean> create(@RequestBody @Validated BrandParam param) {
        return ResultTemplate.success(brandService.createOrUpdate(null, param));
    }

    /**
     * 品牌修改
     */
    @Operation(summary = "品牌修改")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> update(@PathVariable("id") Integer id, @RequestBody BrandParam param) {
        return ResultTemplate.success(brandService.createOrUpdate(id, param));
    }

    /**
     * 品牌删除
     */
    @Operation(summary = "品牌删除")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> delete(@PathVariable("id") Integer id) {
        return ResultTemplate.success(brandService.delete(id));
    }


    /**
     * 查询品牌关联的产品线信息
     */
    @Operation(summary = "查询品牌关联的产品线信息")
    @GetMapping("/{brandId}/product-lines")
    public ResultTemplate<ProductLineDetailVO> getProductLinesByBrandId(@PathVariable("brandId") Integer brandId) {
        return ResultTemplate.success(brandService.getProductLinesByBrandId(brandId));
    }
}
