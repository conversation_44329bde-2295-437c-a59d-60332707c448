package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 	销售-站内审批结果(字典0138)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum InnerApproveResultEnum implements IEnumType<String> {

    AGREE("0138-1", "同意"),
    REJECT("0138-2", "拒绝");

    private final String code;
    private final String desc;

    private static final Map<String, InnerApproveResultEnum> BY_CODE_MAP =
            Arrays.stream(InnerApproveResultEnum.values())
                    .collect(Collectors.toMap(InnerApproveResultEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static InnerApproveResultEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static InnerApproveResultEnum parse(String code, InnerApproveResultEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(InnerApproveResultEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 