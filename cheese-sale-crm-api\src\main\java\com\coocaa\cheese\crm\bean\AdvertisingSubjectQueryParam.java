package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 签约主体创建参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-19
 */
@Data
public class AdvertisingSubjectQueryParam {
    @Schema(description = "签约主体类型(字典0068)", type = "String", example = "0068-6")
    private String type;

    @Schema(description = "品牌名称", type = "String", example = "喜宝")
    private String brandName;

    @Schema(description = "所属公司名称", type = "String", example = "深圳金拱门食品有限公司")
    private String companyName;

    @Schema(description = "是否TOP客户 [0:否, 1:是]", type = "Integer", example = "0")
    private Integer topFlag;

    @Schema(description = "生效状态(1:生效,0:不生效)", type = "Integer", example = "1")
    private Integer effectiveStatus;

    @Schema(description = "是否公海保护期 [0:否, 1:是]", type = "Integer", example = "0")
    private Integer protectionPeriodFlag;

    @Schema(description = "创建人", type = "Integer", example = "0")
    private Integer creator;

    @Schema(description = "部门", type = "Integer", example = "0")
    private String departmentId;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "创建开始日期", type = "String", example = "2025-01-01")
    private LocalDate createStartDate;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "创建结束日期", type = "String", example = "2025-12-31")
    private LocalDate createEndDate;

    @Schema(description = "搜索关键词(品牌名称、签约主体名称)", type = "String", example = "可口可乐")
    private String keyword;
}
