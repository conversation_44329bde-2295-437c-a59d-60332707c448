package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商机信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessHisVO", description = "商机信息VO")
public class BusinessHisVO {

    @Schema(description = "归属人ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer ownerId;
    private String ownerName;

    @Schema(description = "管理部门ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "商机进度(字典0073)", type = "String", example = "0073-1")
    @TransField(type = TransTypes.DICT)
    private String progress;
    private String progressName;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "主体释放日期")
    private LocalDate advertisingReleaseDate;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "分配时间")
    private LocalDateTime assignTime;

    @Schema(description = "跟进天数")
    private Integer followDays;

} 