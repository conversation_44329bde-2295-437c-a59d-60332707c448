package com.coocaa.cheese.crm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 折扣区间视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "折扣区间视图对象")
public class DiscountCostVO {
    @Schema(description = "区间ID")
    private Integer id;

    @Schema(description = "规则ID")
    private Integer ruleId;

    @Schema(description = "当前累计消耗金额")
    private BigDecimal cost;

    @Schema(description = "区间标记 [0:开, 1:闭, 2: 正无穷]")
    private Integer closedFlag;

    @Schema(description = "折扣系数")
    private BigDecimal discount;

    @Schema(description = "增加累计消耗金额")
    private BigDecimal costStep;

    @Schema(description = "折扣系数降低")
    private BigDecimal discountDecrease;

    @Schema(description = "不超过的系数")
    private BigDecimal discountLimit;
} 