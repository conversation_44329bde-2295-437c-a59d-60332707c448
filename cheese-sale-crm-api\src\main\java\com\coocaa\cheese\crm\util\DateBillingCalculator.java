package com.coocaa.cheese.crm.util;

import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.cheese.crm.common.tools.enums.EvidenceBillingTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * 日期计算工具类
 *
 * <AUTHOR>
 * @since 2025/4/7
 */
public class DateBillingCalculator {
    /**
     * 计算计费单位数量（天数或周数）
     *
     * @param startDate   开始日期（非空）
     * @param endDate     结束日期（非空，不能早于开始日期）
     * @param billingType 计费类型（DAY-按天，WEEK-按周）
     * @return 计算得到的天数或周数
     * @throws BusinessException 如果参数无效或计费类型不支持
     */
    public static long calculateBillingUnits(LocalDate startDate, LocalDate endDate, String billingType) {
        // 参数校验
        validateParameters(startDate, endDate, billingType);
        // 根据计费类型选择计算方法
        if (EvidenceBillingTypeEnum.DAY.getCode().equals(billingType)) {
            // 按天计算
            return calculateDays(startDate, endDate);
        } else if (EvidenceBillingTypeEnum.WEEK.getCode().equals(billingType)) {
            // 按周计算（以周六为分界点）
            return calculateWeeksWithSaturdayBoundary(startDate, endDate);
        } else {
            throw new BusinessException("不支持的计费类型: " + billingType);
        }
    }

    /**
     * 参数校验
     *
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param billingType 计费类型
     * @throws BusinessException 如果任何参数为空或结束日期早于开始日期
     */
    private static void validateParameters(LocalDate startDate, LocalDate endDate, String billingType) {
        if (startDate == null || endDate == null || StringUtils.isBlank(billingType)) {
            throw new BusinessException("参数不能为空");
        }
        if (endDate.isBefore(startDate)) {
            throw new BusinessException("结束日期不能早于开始日期");
        }
    }

    /**
     * 计算两个日期之间的天数（包含首尾两天）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 两个日期之间的总天数（包含开始和结束当天）
     */
    private static long calculateDays(LocalDate startDate, LocalDate endDate) {
        // +1 包含首尾两天
        return ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }

    /**
     * 按周计算（以周六为每周分界点）
     * 规则：
     * 1. 每周从周六开始计算
     * 2. 任何跨越周六的时间段都算作新的一周
     * 3. 返回的周数至少为1
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 计算得到的周数
     */
    private static long calculateWeeksWithSaturdayBoundary(LocalDate startDate, LocalDate endDate) {
        // 计算总天数（包含首尾）
        long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        // 如果开始和结束是同一天，直接返回1周
        if (days == 1) {
            return 1;
        }
        // 找到开始日期后的第一个周六（包含当天）
        LocalDate firstSaturday = findNextSaturday(startDate);
        // 计算跨越的周六次数（即周数）
        long saturdayCrossings = calculateSaturdayCrossings(firstSaturday, endDate);
        if (startDate.getDayOfWeek() != DayOfWeek.SATURDAY) {
            saturdayCrossings += 1;
        }
        return saturdayCrossings;
    }

    /**
     * 找到给定日期后的下一个周六（包含当天）
     *
     * @param date 起始日期
     * @return 下一个周六的日期（如果当天是周六则返回当天）
     */
    private static LocalDate findNextSaturday(LocalDate date) {
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        if (dayOfWeek == DayOfWeek.SATURDAY) {
            return date;
        }
        // 特殊处理一下开始时间是周天的情况
        if (dayOfWeek == DayOfWeek.SUNDAY) {
            return date.plusDays(6);
        }
        // 计算到下一个周六的天数差
        return date.plusDays(DayOfWeek.SATURDAY.getValue() - dayOfWeek.getValue());
    }

    /**
     * 计算从第一个周六到结束日期之间跨越的周六次数
     * 规则：
     * 1. 如果结束日期是周六，则至少算作跨越1次
     * 2. 每完整跨越一周（7天）则计数+1
     * 3. 计算方法：
     *    - 计算总天数 = 结束日期 - 第一个周六 + 1
     *    - 基础周数 = 总天数 / 7
     *    - 剩余天数 = 总天数 % 7
     *    - 如果剩余天数>0或结束日期是周六，则周数+1
     *
     * @param firstSaturday 第一个周六日期（必须为周六）
     * @param endDate       结束日期（必须大于等于第一个周六）
     * @return 跨越的周六总次数（最小为1）
     */
    private static long calculateSaturdayCrossings(LocalDate firstSaturday, LocalDate endDate) {
        // 计算总天数（包含首尾）
        long totalDays = ChronoUnit.DAYS.between(firstSaturday, endDate) + 1;

        // 基础周数（每7天算一周）
        long baseWeeks = totalDays / 7;

        // 剩余天数
        long remainingDays = totalDays % 7;

        // 如果结束日期是周六或者有剩余天数，则加一周
        if (endDate.getDayOfWeek() == DayOfWeek.SATURDAY || remainingDays > 0) {
            baseWeeks++;
        }

        return baseWeeks;
    }
}
