package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 机构账户列表查询参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构账户列表查询参数")
public class InstitutionAccountListParam {

    @Schema(description = "是否停用（true-停用，false-启用，null-全部）")
    private Boolean disabled;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "机构账户ID列表")
    private List<Integer> accountIds;
} 