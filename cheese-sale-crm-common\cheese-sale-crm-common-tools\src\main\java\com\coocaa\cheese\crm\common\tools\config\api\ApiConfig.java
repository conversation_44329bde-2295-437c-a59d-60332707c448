package com.coocaa.cheese.crm.common.tools.config.api;

import com.coocaa.cheese.crm.common.tools.config.BaseCosConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @className: ApiConfig
 * @description: NACOS API配置
 * @author: jx
 * @date: 2025/3/10 10:07
 */
@Configuration
public class ApiConfig extends BaseCosConfig {

    /**
     * 每天早上8:30 发送消息通知短信模板id
     */
    @Value("${message.template.id:2480600}")
    public String templateId;

    /**
     * 发送飞书消息的群组id
     */
    @Value("${feishu.message.chat.id:oc_14391d079d6d332bb23795755a9b1170}")
    public String messageChatId;

    /**
     * 读取服务名
     */
    @Value("${spring.application.name}")
    public String appName;
}
