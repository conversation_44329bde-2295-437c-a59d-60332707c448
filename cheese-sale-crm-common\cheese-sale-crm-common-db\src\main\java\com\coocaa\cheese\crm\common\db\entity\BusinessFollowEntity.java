package com.coocaa.cheese.crm.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商机跟进任务表
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@TableName("sale_crm_business_follow")
public class BusinessFollowEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商机ID
     */
    private Integer businessId;

    /**
     * 跟进时间
     */
    private LocalDateTime followTime;

    /**
     * 跟进主题 [首次建联, 需求挖掘, 方案确认, 商务推动, 履约服务, 日常维护]
     */
    private String followSubject;

    /**
     * 是否有效沟通 [0:否, 1:是]
     */
    private Integer effectiveCommunicationFlag;

    /**
     * 沟通进展
     */
    private String communicationProgress;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
