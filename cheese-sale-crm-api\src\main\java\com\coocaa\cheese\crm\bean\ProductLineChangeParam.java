
package com.coocaa.cheese.crm.bean;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品线变更记录表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Schema(name = "ProductLineChangeParam", description = "产品线变更记录表参数")
public class ProductLineChangeParam {

    @NotNull(message = "主键ID不能为空")
    @Schema(description = "主键ID")
    private Long id;

    @NotNull(message = "商机ID不能为空")
    @Schema(description = "商机ID")
    private Integer businessId;

    @NotNull(message = "商机进度(字典0073)不能为空")
    @Schema(description = "商机进度(字典0073)", maxLength = 10)
    private String progress;

    @NotNull(message = "变更前产品线不能为空")
    @Schema(description = "变更前产品线", maxLength = 255)
    private String beforeProductLine;

    @NotNull(message = "变更后产品线不能为空")
    @Schema(description = "变更后产品线", maxLength = 255)
    private String afterProductLine;

    @Schema(description = "情况说明", maxLength = 100)
    private String remark;

    @NotNull(message = "执行状态(字典0163)不能为空")
    @Schema(description = "执行状态(字典0163)", maxLength = 10)
    private String executeStatus;

    @NotNull(message = "状态变更时间不能为空")
    @Schema(description = "状态变更时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime statusChangeTime;

    @NotNull(message = "删除标记  [0:否, 1:是]不能为空")
    @Schema(description = "删除标记  [0:否, 1:是]", maxLength = 1)
    private Integer deleteFlag;

    @NotNull(message = "创建人不能为空")
    @Schema(description = "创建人")
    private Integer creator;

    @NotNull(message = "创建时间不能为空")
    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @NotNull(message = "操作人不能为空")
    @Schema(description = "操作人")
    private Integer operator;

    @NotNull(message = "更新时间不能为空")
    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;
}