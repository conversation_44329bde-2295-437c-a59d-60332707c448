
package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公海打捞授权表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
public class PublicSeaAuthDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商机ID
     */
    private Integer businessId;

    /**
     * 情况说明
     */
    private String remark;

    /**
     * 执行状态(字典0151)
     */
    private String executeStatus;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Boolean deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}