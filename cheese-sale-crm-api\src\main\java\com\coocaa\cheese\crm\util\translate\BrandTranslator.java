package com.coocaa.cheese.crm.util.translate;

import com.coocaa.ad.translate.Translator;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.ad.translate.util.TransUtils;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;
import com.coocaa.cheese.crm.common.db.service.IBrandService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 企业相关数据翻译
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BrandTranslator implements Translator<Integer> {
    private final IBrandService brandService;

    @Override
    public String getTransType() {
        return TransTypes.BRAND;
    }

    @Override
    public Class<Integer> getDataType() {
        return Integer.class;
    }

    @Override
    public Map<Integer, String> getMapping(Collection<Integer> sourceValues) {
        return TransUtils.toNumValMap(sourceValues, ids -> brandService.lambdaQuery()
                .select(BrandEntity::getId, BrandEntity::getName)
                .in(BrandEntity::getId, ids)
                .list().stream()
                .collect(Collectors.toMap(BrandEntity::getId, BrandEntity::getName, (o, n) -> n)));
    }
}
