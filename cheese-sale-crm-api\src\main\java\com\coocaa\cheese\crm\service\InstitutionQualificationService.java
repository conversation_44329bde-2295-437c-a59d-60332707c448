package com.coocaa.cheese.crm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.common.result.PageRequestVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.InstitutionQualificationParam;
import com.coocaa.cheese.crm.bean.InstitutionQualificationQueryParam;
import com.coocaa.cheese.crm.common.db.entity.InstitutionQualificationEntity;
import com.coocaa.cheese.crm.common.db.service.IInstitutionQualificationService;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.convert.InstitutionQualificationConvert;
import com.coocaa.cheese.crm.vo.InstitutionQualificationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 机构资质服务
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionQualificationService {
    private final IInstitutionQualificationService institutionQualificationService;

    /**
     * 分页查询机构资质列表
     *
     * @param pageRequest 分页查询参数
     * @return 分页结果
     */
    @AutoTranslate
    public PageResponseVO<InstitutionQualificationVO> pageListInstitutionQualification(PageRequestVO<InstitutionQualificationQueryParam> pageRequest) {
        // 构建分页对象
        Page<InstitutionQualificationEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(Constants.DEFAULT_PAGE_SIZE));

        // 构建查询条件
        InstitutionQualificationQueryParam query = pageRequest.getQuery();
        IPage<InstitutionQualificationEntity> pageResult = institutionQualificationService.lambdaQuery()
                .eq(Objects.nonNull(query.getInstitutionId()), InstitutionQualificationEntity::getInstitutionId, query.getInstitutionId())
                .eq(InstitutionQualificationEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(InstitutionQualificationEntity::getCreateTime)
                .page(page);

        // 转换为VO
        return InstitutionQualificationConvert.INSTANCE.toPageResponse(pageResult);
    }

    /**
     * 获取机构资质列表
     *
     * @param institutionId 机构账户ID
     * @return 机构资质列表
     */
    @AutoTranslate
    public List<InstitutionQualificationVO> listInstitutionQualification(Integer institutionId) {
        // 查询机构资质列表
        List<InstitutionQualificationEntity> qualificationList = institutionQualificationService.lambdaQuery()
                .eq(InstitutionQualificationEntity::getInstitutionId, institutionId)
                .eq(InstitutionQualificationEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(InstitutionQualificationEntity::getCreateTime)
                .list();

        // 转换为VO
        return InstitutionQualificationConvert.INSTANCE.entityToVO(qualificationList);
    }

    /**
     * 上传机构资质
     *
     * @param param 资质参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadInstitutionQualification(InstitutionQualificationParam param) {
        // 检查资质名称是否重复
        checkQualificationName(param.getQualificationName(), param.getInstitutionId());

        // 转换为实体
        InstitutionQualificationEntity entity = InstitutionQualificationConvert.INSTANCE.toEntity(param);
        entity.setDeleteFlag(BooleFlagEnum.NO.getCode());

        // 保存资质
        return institutionQualificationService.save(entity);
    }

    /**
     * 检查资质名称是否重复
     */
    private void checkQualificationName(String qualificationName, Integer institutionId) {
        // 同一机构下资质名称重复，覆盖更新
        InstitutionQualificationEntity existQualification = institutionQualificationService.lambdaQuery()
                .eq(InstitutionQualificationEntity::getQualificationName, qualificationName)
                .eq(InstitutionQualificationEntity::getInstitutionId, institutionId)
                .eq(InstitutionQualificationEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();

        if (existQualification != null) {
            // 删除旧资质
            institutionQualificationService.removeById(existQualification.getId());
        }
    }
} 