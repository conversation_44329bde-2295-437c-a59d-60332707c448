package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 机构资金冻结处理参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构资金冻结处理参数")
public class InstitutionFundFreezeParam {

    @Schema(description = "资金账户ID", type = "Integer", example = "1")
    private Integer fundAccountId;

    @Schema(description = "资金账户编码", type = "String", example = "1")
    @NotNull(message = "资金账户编码不能为空")
    private String fundAccountCode;

    @Schema(description = "处理类型：1-解冻, 2-转扣费", type = "Integer", example = "1")
    @NotNull(message = "处理类型不能为空")
    private Integer processType;

    @Schema(description = "扣费金额，处理类型为转扣费时必填", type = "BigDecimal", example = "500.00")
    @DecimalMin(value = "0.01", message = "扣费金额不能小于0.01")
    @DecimalMax(value = "9999999.99", message = "扣费金额不能大于9999999.99")
    private BigDecimal deductAmount;

    @Schema(description = "备注说明", type = "String", example = "解冻未使用资金")
    private String description;

    @Schema(description = "变动调整凭据URL", type = "String", example = "https://www.baidu.com/img/bd_logo1.png")
    private String adjustmentVoucherUrl;
} 