package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.bean.BusinessDelayDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerProductLineDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseBusinessDetailDTO;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.mapper.BusinessMapper;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 商机服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-19
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessServiceImpl extends ServiceImpl<BusinessMapper, BusinessEntity> implements IBusinessService {

    @Override
    public InnerReleaseBusinessDetailDTO queryBusinessDetailForInner(Integer id) {
        return getBaseMapper().queryBusinessDetailForInner(id);
    }

    @Override
    public InnerProductLineDetailDTO queryDetailForProductLine(Integer id) {
        return getBaseMapper().queryDetailForProductLine(id);
    }

    @Override
    public List<BusinessDelayDTO> queryBusinessToBeReleased(LocalDate threeDaysLater) {
        return getBaseMapper().queryBusinessToBeReleased(threeDaysLater);
    }
}