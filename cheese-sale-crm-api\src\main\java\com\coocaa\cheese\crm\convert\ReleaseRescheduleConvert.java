package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.ReleaseRescheduleInitiateParam;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseBusinessDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseHistoryDTO;
import com.coocaa.cheese.crm.common.db.entity.ReleaseRescheduleEntity;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveApplyVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveTaskVO;
import com.coocaa.cheese.crm.vo.InnerApproveReleaseApplyPageVO;
import com.coocaa.cheese.crm.vo.InnerReleaseApproveDetailVO;
import com.coocaa.cheese.crm.vo.InnerReleaseApproveTaskPageVO;
import com.coocaa.cheese.crm.vo.InnerReleaseHistoryVO;
import com.coocaa.cheese.crm.vo.ReleaseRescheduleInitiateVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 释放改期记录表 信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-5-13
 */
@Mapper
public interface ReleaseRescheduleConvert extends PageableConvert<ReleaseRescheduleEntity, ReleaseRescheduleInitiateVO> {
    ReleaseRescheduleConvert INSTANCE = Mappers.getMapper(ReleaseRescheduleConvert.class);

    /**
     * dto 转 VO
     */
    ReleaseRescheduleInitiateVO toVo(InnerReleaseBusinessDetailDTO dto);

    /**
     * param 转 entity
     */
    ReleaseRescheduleEntity toEntity(ReleaseRescheduleInitiateParam param);

    /**
     * vo 转 vo
     */
    InnerReleaseApproveTaskPageVO toVo(InnerApproveTaskVO vo);

    /**
     * vo 转 vo
     */
    InnerApproveReleaseApplyPageVO toVo(InnerApproveApplyVO vo);

    /**
     * dto 转 vo
     */
    InnerReleaseApproveDetailVO toVo(InnerReleaseApproveDetailDTO dto);

    /**
     * dto 转 vo
     */
    InnerReleaseHistoryVO toVo(InnerReleaseHistoryDTO dto);
}