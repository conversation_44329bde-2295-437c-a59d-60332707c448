package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售-站内审批状态(字典0141)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum InnerApproveStatusEnum implements IEnumType<String> {

    PENDING_APPROVE("0141-1", "待审批"),
    APPROVING("0141-2", "审批中"),
    ALREADY_CANCEL("0141-3", "已取消"),
    ALREADY_FINISH("0141-4", "已完成");

    private final String code;
    private final String desc;

    private static final Map<String, InnerApproveStatusEnum> BY_CODE_MAP =
            Arrays.stream(InnerApproveStatusEnum.values())
                    .collect(Collectors.toMap(InnerApproveStatusEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static InnerApproveStatusEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static InnerApproveStatusEnum parse(String code, InnerApproveStatusEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(InnerApproveStatusEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 