package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 签约主体VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "AdvertisingSubjectVO", description = "签约主体VO")
public class AdvertisingSubjectVO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "投放主体类型(字典0068)", type = "String", example = "0068-6")
    @TransField(type = TransTypes.DICT)
    private String type;
    private String typeName;

    @Schema(description = "品牌ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "所属公司ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;

    @Schema(description = "是否TOP客户 [0:否, 1:是]", type = "Integer", example = "0")
    private Integer topFlag;

    @Schema(description = "证明材料", type = "String", example = "http://example.com/evidence.pdf")
    private String evidenceUrl;

    @Schema(description = "备注说明", type = "String", example = "重点客户签约主体")
    private String description;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "操作人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer operator;

    @Schema(description = "操作人", type = "Integer", example = "1")
    private String operatorName;

    @Schema(description = "创建人", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer creator;
    private String creatorName;

    @Schema(description = "生效状态(1:生效,0:不生效)", type = "Integer", example = "1")
    private Integer effectiveStatus;

    @Schema(description = "生效时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime effectiveTime;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "是否公海保护期 [0:否, 1:是]", type = "Integer", example = "0")
    private Integer protectionPeriodFlag;

    @Schema(description = "产品线", type = "String", example = "1")
    private List<String> productLineList;

}