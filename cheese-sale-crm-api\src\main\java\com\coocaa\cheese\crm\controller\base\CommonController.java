package com.coocaa.cheese.crm.controller.base;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.util.StringUtils;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.cheese.crm.common.tools.bean.CodeNameVO;
import com.coocaa.cheese.crm.controller.BaseController;
import com.coocaa.cheese.crm.rpc.FeignAuthorityRpc;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 公共数据
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-02-22
 */
@Slf4j
@RestController
@RequestMapping("/common")
@Tag(name = "公共数据", description = "公共数据")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CommonController extends BaseController {
    private final FeignAuthorityRpc feignAuthorityRpc;

    /**
     * 获取用户列表
     */
    @Operation(summary = "获取用户列表")
    @GetMapping("/users")
    public ResultTemplate<List<CodeNameVO>> getUsers(
            @RequestParam(name = "keyword", required = false) String keyword,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size) {
        List<CodeNameVO> cities = Optional.ofNullable(feignAuthorityRpc.listDataUser(keyword, size))
                .map(ResultTemplate::getData)
                .orElse(Collections.emptyList());
        return ResultTemplate.success(cities);
    }

    /**
     * 获取启用的城市列表
     */
    @Operation(summary = "启用的城市列表")
    @GetMapping("/cities/available")
    public ResultTemplate<List<CodeNameVO>> getAvailableCities() {
        List<CodeNameVO> cities = Optional.ofNullable(feignAuthorityRpc.getAvailableCities())
                .map(ResultTemplate::getData)
                .orElse(Collections.emptyList());
        return ResultTemplate.success(cities);
    }

    /**
     * 根据城市ID查询详情
     */
    @Operation(summary = "城市详情列表")
    @GetMapping("/cities")
    public ResultTemplate<List<CodeNameVO>> getCities(@RequestParam(name = "ids") List<Integer> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return ResultTemplate.success(Collections.emptyList());
        }

        // 获取城市详情
        List<CodeNameVO> cities = Optional.ofNullable(feignAuthorityRpc.listCityByIds(ids))
                .map(ResultTemplate::getData)
                .orElse(Collections.emptyList());
        return ResultTemplate.success(cities);
    }


    /**
     * 根据字典编码查询详情
     */
    @Operation(summary = "字典详情列表")
    @GetMapping("/dicts")
    public ResultTemplate<List<CodeNameVO>> getDicts(@RequestParam(name = "codes") List<String> codes) {
        if (CollectionUtil.isEmpty(codes)) {
            return ResultTemplate.success(Collections.emptyList());
        }

        // 获取字典详情
        List<CodeNameVO> cities = Optional.ofNullable(feignAuthorityRpc.listDictByCodes(codes))
                .map(ResultTemplate::getData)
                .orElse(Collections.emptyList());
        return ResultTemplate.success(cities);
    }


    /**
     * 根据字典父编码查询列表
     */
    @Operation(summary = "字典详情列表")
    @GetMapping("/dicts/parent/{code}")
    public ResultTemplate<List<CodeNameVO>> getDictsByParent(@PathVariable(name = "code") String parentCode) {
        if (StringUtils.isBlank(parentCode)) {
            return ResultTemplate.success(Collections.emptyList());
        }

        // 获取字典详情
        List<CodeNameVO> cities = Optional.ofNullable(feignAuthorityRpc.listDictByParent(parentCode))
                .map(ResultTemplate::getData)
                .orElse(Collections.emptyList());
        return ResultTemplate.success(cities);
    }

    /**
     * 行业列表
     */
    @Operation(summary = "行业详情列表")
    @GetMapping("/industries")
    public ResultTemplate<List<CodeNameVO>> getIndustries(@RequestParam(name = "codes", required = false) List<String> codes) {
        ResultTemplate<List<CodeNameVO>> feignResults = CollectionUtil.isEmpty(codes)
                ? feignAuthorityRpc.listSecondLevelIndustries()
                : feignAuthorityRpc.listIndustryByCodes(codes);

        // 获取行业信息
        List<CodeNameVO> cities = Optional.ofNullable(feignResults)
                .map(ResultTemplate::getData)
                .orElse(Collections.emptyList());
        return ResultTemplate.success(cities);
    }
}
