package com.coocaa.cheese.crm.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseApproveTaskPageDTO;
import com.coocaa.cheese.crm.common.db.bean.InnerReleaseHistoryDTO;
import com.coocaa.cheese.crm.common.db.entity.ReleaseRescheduleEntity;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 释放改期记录表接口
 *
 * <AUTHOR>
 * @since 2025-5-13
 */
public interface ReleaseRescheduleMapper extends BaseMapper<ReleaseRescheduleEntity> {

    /**
     * 延期申请查询详情
     */
    InnerReleaseApproveDetailDTO queryInnerApproveDetail(Integer id);

    /**
     * 申请延期查询商机信息
     */
    List<InnerReleaseApproveTaskPageDTO> queryInnerBusiness(List<Integer> bizIds);


    /**
     * 延期申请查询记录
     */
    List<InnerReleaseHistoryDTO> queryReleaseHistory(@Param("id") Integer id,
                                                     @Param("createTime") LocalDateTime createTime,
                                                     @Param("businessId") Integer businessId);
}