package com.coocaa.cheese.crm.service;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.common.exception.CommonException;
import com.coocaa.cheese.crm.common.db.entity.ConfigEntity;
import com.coocaa.cheese.crm.common.db.service.IConfigService;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.tools.util.CacheKeyUtils;
import com.coocaa.cheese.crm.convert.ConfigConvert;
import com.coocaa.cheese.crm.vo.ConfigVO;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售服务配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-22
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ConfigService {
    private static final String CONFIG_CACHE_KEY = CacheKeyUtils.getConfigKey();
    private final IConfigService configService;
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 查询配置值
     */
    public String getConfigValue(String code) {
        return Optional.ofNullable(getConfigEntity(code)).map(ConfigEntity::getValue).orElse(null);
    }

    /**
     * 查询配置
     */
    public ConfigVO getConfig(String code) {
        return Optional.ofNullable(getConfigEntity(code)).map(ConfigConvert.INSTANCE::toVo).orElse(null);
    }

    /**
     * 根据父编码查询配置
     */
    public List<ConfigVO> listConfigByParent(String parentCode) {
        if (StringUtils.isBlank(parentCode)) {
            return Collections.emptyList();
        }
        return configService.lambdaQuery()
                .eq(ConfigEntity::getParentCode, parentCode)
                .eq(ConfigEntity::getStatus, BooleFlagEnum.YES.getCode())
                .list().stream()
                .map(ConfigConvert.INSTANCE::toVo).toList();
    }

    /**
     * 创建配置
     */
    public boolean createConfig(ConfigVO config) {
        // 检查配置是否重复
        if (configService.lambdaQuery()
                .eq(ConfigEntity::getCode, config.getCode())
                .eq(StringUtils.isNotBlank(config.getParentCode()), ConfigEntity::getParentCode, config.getParentCode())
                .exists()) {
            throw new CommonException("配置已存在");
        }

        try {
            ConfigEntity entity = ConfigConvert.INSTANCE.toEntity(config);
            entity.setStatus(BooleFlagEnum.YES.getCode());
            return configService.save(entity);
        } catch (Exception ex) {
            log.error("保存配置失败", ex);
            throw new CommonException("保存配置失败");
        }
    }


    /**
     * 修改配置
     */
    public boolean updateConfig(Integer configId, ConfigVO config) {
        // 检查配置是否重复
        if (configService.lambdaQuery()
                .eq(ConfigEntity::getCode, config.getCode())
                .eq(StringUtils.isNotBlank(config.getParentCode()), ConfigEntity::getParentCode, config.getParentCode())
                .ne(Objects.nonNull(configId), ConfigEntity::getId, configId)
                .exists()) {
            throw new CommonException("配置已存在");
        }

        // 查询已存在的配置信息
        ConfigEntity existed = Optional.ofNullable(configService.getById(configId))
                .orElseThrow(() -> new CommonException("配置不存在"));

        // 保存填充配置信息
        ConfigEntity entity = ConfigConvert.INSTANCE.toEntity(config);
        entity.setId(existed.getId());
        entity.setStatus(BooleFlagEnum.YES.getCode());

        // 保存数据 & 更新缓存
        if (configService.updateById(entity)) {
            updateCache(entity);
            return true;
        }

        return false;
    }

    /**
     * 创建或修改配置
     */
    public boolean createOrUpdate(ConfigVO config) {
        // 查询已存在的配置
        ConfigEntity existed = getConfigEntity(config.getCode());

        ConfigEntity newConfig = ConfigConvert.INSTANCE.toEntity(config);
        boolean result;
        if (Objects.isNull(existed)) {
            newConfig.setStatus(BooleFlagEnum.YES.getCode());
            result = configService.save(newConfig);
        } else {
            newConfig.setId(existed.getId());
            result = configService.updateById(newConfig);
        }

        // 更新缓存
        if (result) {
            updateCache(newConfig);
        }

        return result;
    }

    /**
     * 创建或修改配置
     */
    public boolean createOrUpdateByCode(String code, String value) {
        // 查询已存在的配置
        ConfigEntity existed = getConfigEntity(code);

        boolean result;
        ConfigEntity newConfig;
        if (Objects.isNull(existed)) {
            newConfig = new ConfigEntity();
            newConfig.setCode(code);
            newConfig.setValue(value);
            newConfig.setStatus(BooleFlagEnum.YES.getCode());
            result = configService.save(newConfig);
        } else {
            newConfig = new ConfigEntity();
            newConfig.setId(existed.getId());
            newConfig.setCode(code);
            newConfig.setValue(value);
            result = configService.updateById(newConfig);
        }

        // 更新缓存
        if (result) {
            updateCache(newConfig);
        }

        return result;
    }

    /**
     * 刷新缓存
     */
    @PostConstruct
    public boolean refreshCache() {
        log.info("重新缓存所有配置信息");
        // 查出所有配置
        List<ConfigEntity> configs = configService.lambdaQuery()
                .select(ConfigEntity::getId, ConfigEntity::getName, ConfigEntity::getCode, ConfigEntity::getValue,
                        ConfigEntity::getExt1, ConfigEntity::getExt2, ConfigEntity::getExt3)
                .eq(ConfigEntity::getStatus, BooleFlagEnum.YES.getCode()).list();
        stringRedisTemplate.delete(CONFIG_CACHE_KEY);

        // 重新缓存所有配置
        if (CollectionUtils.isNotEmpty(configs)) {
            Map<String, String> configMap = configs.stream()
                    .filter(config -> StringUtils.isNotBlank(config.getCode()))
                    .collect(Collectors.toMap(config -> getKey(config.getCode()), JSON::toJSONString));
            stringRedisTemplate.opsForHash().putAll(CONFIG_CACHE_KEY, configMap);
            log.info("重新缓存所有[{}]条配置成功", configs.size());
        }
        return true;
    }


    /**
     * 根据编码查配置
     */
    private ConfigEntity getConfigEntity(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        // 优先查缓存
        String json = stringRedisTemplate.<String, String>opsForHash().get(CONFIG_CACHE_KEY, getKey(code));
        ConfigEntity config = null;
        if (StringUtils.isNotBlank(json)) {
            config = JSON.parseObject(json, ConfigEntity.class);
        }

        // 再查数据库
        if (Objects.isNull(config)) {
            config = configService.lambdaQuery()
                    .select(ConfigEntity::getId, ConfigEntity::getName, ConfigEntity::getCode, ConfigEntity::getValue,
                            ConfigEntity::getExt1, ConfigEntity::getExt2, ConfigEntity::getExt3)
                    .eq(ConfigEntity::getCode, code)
                    .last("LIMIT 1")
                    .one();

            // 更新缓存
            if (Objects.nonNull(config)) {
                updateCache(config);
            }
        }

        return config;
    }

    /**
     * 更新缓存
     */
    private boolean updateCache(ConfigEntity config) {
        if (Objects.isNull(config) || StringUtils.isBlank(config.getCode())) {
            return false;
        }

        // 更新缓存
        stringRedisTemplate.opsForHash().put(CONFIG_CACHE_KEY, getKey(config.getCode()), JSON.toJSONString(config));
        return true;
    }

    /**
     * 生成缓存Key
     */
    private String getKey(String code) {
        return StringUtils.isBlank(code) ? "null" : code.trim().toLowerCase();
    }
}
