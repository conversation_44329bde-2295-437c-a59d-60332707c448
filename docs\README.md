## 工具类使用
### 数据加解密
```java
// 【自动解密】前端 -> 后端，敏感信息需要加密传输
@JSONField(deserializeUsing = EncryptDeserializer.class)
@Schema(description = "联系电话", type = "String", example = "加密的字符串")  
private String contactPhone;

// 【自动加密】后端 -> 前端，敏感信息需要加密传输
@JSONField(serializeUsing = DesensitizeSerializer.class)
@Schema(description = "联系电话", type = "String", example = "加密的字符串")
private String contactPhone;

// 【自动加密】 使用AES加密后存入数据库
@Data  
@TableName(value = "venue_contract", autoResultMap = true)  
public class ContractEntity implements Serializable {
   /**  
    * 联系电话  
    */  
   @TableField(typeHandler = EncryptHandler.class)  
   private String contactPhone;  
  
   /**  
    * 联系邮箱  
    */  
   @TableField(typeHandler = EncryptHandler.class)  
   private String contactEmail;
}

// 注意这时有个大坑
// 使用以下方式更新数据字段时，typeHandler = EncryptHandler.class 将不会生效
boolean result = workOrderService.lambdaUpdate()
        .set(WorkOrderEntity::getWorkerName, workerParam.getWorkerName())
        .set(WorkOrderEntity::getWorkerMobile, workerParam.getWorkerMobile())
        .eq(WorkOrderEntity::getId, entity.getId())
        .update();

// 需要修改成以下方式去更新数据
WorkOrderEntity updateEntity = new WorkOrderEntity();
updateEntity.setId(entity.getId());
updateEntity.setWorkerName(workerParam.getWorkerName());
updateEntity.setWorkerMobile(workerParam.getWorkerMobile());
boolean result = workOrderService.updateById(updateEntity);


```

### 字典翻译
```java
// 注入包
private final ConverterFactory converterFactory;

// 翻译有些特定信息  
converterFactory.convert(Collection<?> list);

// 翻译指定字段
@Schema(description = "申请单状态(字典0025)", type = "Int", example = "0")  
@Convert(type = ConvertType.DICT, targetFieldName = "applyStatusName")  
private String applyStatus;  
private String applyStatusName;

// 可实现自定义翻译
// 参考 com.coocaa.ad.cheese.workorder.util.converter.DictConverter 实现
```
