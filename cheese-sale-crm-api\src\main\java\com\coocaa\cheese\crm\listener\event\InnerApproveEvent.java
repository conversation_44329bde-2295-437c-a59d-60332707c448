package com.coocaa.cheese.crm.listener.event;

import com.coocaa.cheese.crm.common.tools.enums.InnerApproveOpinionTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.StationTargetEnum;
import com.coocaa.cheese.crm.rpc.vo.InnerTaskOperateVO;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/6
 */
public class InnerApproveEvent extends ApplicationEvent {

    private final Long id;
    private final StationTargetEnum targetEnum;
    private final InnerApproveOpinionTypeEnum opinionTypeEnum;
    private final InnerTaskOperateVO innerTaskOperateVO;

    public InnerApproveEvent(Object source, Long id, StationTargetEnum targetEnum,
                             InnerApproveOpinionTypeEnum opinionTypeEnum, InnerTaskOperateVO innerTaskOperateVO) {
        super(source);
        this.id = id;
        this.targetEnum = targetEnum;
        this.opinionTypeEnum = opinionTypeEnum;
        this.innerTaskOperateVO = innerTaskOperateVO;
    }

    public Long getId() {
        return id;
    }

    public StationTargetEnum getTargetEnum() {
        return targetEnum;
    }

    public InnerApproveOpinionTypeEnum getOpinionTypeEnum() {
        return opinionTypeEnum;
    }

    public InnerTaskOperateVO getInnerTaskOperateVO() {
        return innerTaskOperateVO;
    }
}
