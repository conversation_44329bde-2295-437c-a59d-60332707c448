package com.coocaa.cheese.crm.common.db.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.common.util.AesUtils;
import com.coocaa.cheese.crm.common.db.bean.BankQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.BankEntity;
import com.coocaa.cheese.crm.common.db.mapper.BankMapper;
import com.coocaa.cheese.crm.common.db.service.IBankService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 银行帐户 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-25
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BankServiceImpl extends ServiceImpl<BankMapper, BankEntity> implements IBankService {

    @Override
    public IPage<BankEntity> pageList(IPage<BankEntity> page, BankQueryDTO condition) {
        if (StringUtils.isNotBlank(condition.getAccount())) {
            condition.setAccount(AesUtils.encryptHex(condition.getAccount().trim()));
        }
        return getBaseMapper().pageList(page, condition);
    }
}
