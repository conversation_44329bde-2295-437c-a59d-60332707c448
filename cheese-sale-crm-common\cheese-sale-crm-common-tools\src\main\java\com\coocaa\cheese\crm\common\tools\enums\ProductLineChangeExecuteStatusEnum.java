package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 	销售-产品线变更执行状态(字典0163)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum ProductLineChangeExecuteStatusEnum implements IEnumType<String> {

    PENDING("0163-1", "待定"),
    ALREADY_EXECUTE("0163-2", "已执行"),
    ALREADY_CANCEL("0163-3", "已取消"),
    EXECUTE_FAIL("0163-4", "执行失败");

    private final String code;
    private final String desc;

    private static final Map<String, ProductLineChangeExecuteStatusEnum> BY_CODE_MAP =
            Arrays.stream(ProductLineChangeExecuteStatusEnum.values())
                    .collect(Collectors.toMap(ProductLineChangeExecuteStatusEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static ProductLineChangeExecuteStatusEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static ProductLineChangeExecuteStatusEnum parse(String code, ProductLineChangeExecuteStatusEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(ProductLineChangeExecuteStatusEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 