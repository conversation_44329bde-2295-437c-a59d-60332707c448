package com.coocaa.cheese.crm.rpc.vo;

import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/7
 */
@Data
public class InnerInstanceTaskVO {

    @Schema(description = "审批人用户ID")
    private Integer userId;

    @Schema(description = "任务ID")
    private Integer taskId;

    @Schema(description = "审批任务名称")
    private String approvalName;

    @Schema(description = "审批实例创建时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime instanceCreateTime;

    @Schema(description = "审批实例创建人")
    private Integer instanceUserId;
}
