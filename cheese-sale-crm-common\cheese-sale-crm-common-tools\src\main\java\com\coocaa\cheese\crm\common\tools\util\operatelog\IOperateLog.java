package com.coocaa.cheese.crm.common.tools.util.operatelog;

import java.util.Date;

/**
 * 操作日志接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-04
 */
public interface IOperateLog {
    /**
     * 获取系统编码-销售工作台CRM
     * @return 系统编码
     */
    default String getSystemCode() {
        return "0006-6";
    }

    /**
     * 获取功能名称
     * @return 功能名称
     */
    String getFunctionName();

    /**
     * 获取实体编码
     * @return 实体编码
     */
    String getEntityCode();

    /**
     * 获取实体ID
     * @return 实体ID
     */
    Integer getEntityId();

    /**
     * 获取操作内容
     * @return 操作内容
     */
    String getContent();

    /**
     * 获取操作人
     * @return 操作人
     */
    Integer getOperator();

    /**
     * 操作时间
     */
    default Date getCreateTime() {
        return new Date();
    }
}
