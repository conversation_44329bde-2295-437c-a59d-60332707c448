package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.TransferRecordEntity;
import com.coocaa.cheese.crm.common.db.mapper.TransferRecordMapper;
import com.coocaa.cheese.crm.common.db.service.ITransferRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 转移记录服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class TransferRecordServiceImpl
        extends ServiceImpl<TransferRecordMapper, TransferRecordEntity>
        implements ITransferRecordService {
} 