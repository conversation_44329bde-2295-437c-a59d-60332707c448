package com.coocaa.cheese.crm.util.translate;

import com.coocaa.ad.translate.Translator;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.ad.translate.util.TransUtils;
import com.coocaa.cheese.crm.common.db.entity.ChannelEntity;
import com.coocaa.cheese.crm.common.db.service.IChannelService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 渠道相关数据翻译
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-20
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ChannelTranslator implements Translator<Integer> {
    private final IChannelService channelService;

    @Override
    public String getTransType() {
        return TransTypes.CHANNEL;
    }

    @Override
    public Class<Integer> getDataType() {
        return Integer.class;
    }

    @Override
    public Map<Integer, String> getMapping(Collection<Integer> sourceValues) {
        return TransUtils.toNumValMap(sourceValues, ids -> channelService.lambdaQuery()
                .select(ChannelEntity::getId, ChannelEntity::getName)
                .in(ChannelEntity::getId, ids)
                .list().stream()
                .collect(Collectors.toMap(ChannelEntity::getId, ChannelEntity::getName, (o, n) -> n)));
    }
}
