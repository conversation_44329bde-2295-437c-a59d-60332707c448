package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 签约主体状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AdvertisingReleaseStatusEnum implements IEnumType<String> {

    UNBOUND("未绑定", "未绑定签约主体"),
    FOREVER("永久", "永久保护"),
    RELEASED("已释放", "已释放状态"),
    PROTECTING("保护中", "保护期内");

    private final String code;
    private final String desc;

    private static final Map<String, AdvertisingReleaseStatusEnum> BY_CODE_MAP =
            Arrays.stream(AdvertisingReleaseStatusEnum.values())
                    .collect(Collectors.toMap(AdvertisingReleaseStatusEnum::getCode, item -> item));

    /**
     * 将代码转成枚举
     */
    public static AdvertisingReleaseStatusEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static AdvertisingReleaseStatusEnum parse(String code, AdvertisingReleaseStatusEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(AdvertisingReleaseStatusEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}