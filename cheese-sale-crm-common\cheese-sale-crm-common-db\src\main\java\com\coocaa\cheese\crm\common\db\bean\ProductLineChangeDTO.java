
package com.coocaa.cheese.crm.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品线变更记录表
 *
 * <AUTHOR>
 * @since 2025-6-17
 */
@Data
@Accessors(chain = true)
public class ProductLineChangeDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商机ID
     */
    private Integer businessId;

    /**
     * 商机进度(字典0073)
     */
    private String progress;

    /**
     * 变更前产品线
     */
    private String beforeProductLine;

    /**
     * 变更后产品线
     */
    private String afterProductLine;

    /**
     * 情况说明
     */
    private String remark;

    /**
     * 执行状态(字典0163)
     */
    private String executeStatus;

    /**
     * 状态变更时间
     */
    private LocalDateTime statusChangeTime;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Boolean deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}