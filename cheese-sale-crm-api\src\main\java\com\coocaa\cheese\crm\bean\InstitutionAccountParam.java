package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 机构账户参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构账户参数")
public class InstitutionAccountParam {

    @Schema(description = "公司ID", type = "Integer", example = "1")
    @NotNull(message = "公司ID不能为空")
    private Integer companyId;

    @Schema(description = "上级机构ID", type = "Integer", example = "2")
    private Integer parentId;

    @Schema(description = "授权开始日期", type = "LocalDate", example = "2025-01-01")
    private LocalDate authStartDate;

    @Schema(description = "授权截止日期", type = "LocalDate", example = "2025-12-31")
    private LocalDate authEndDate;

    @Schema(description = "备注", type = "String", example = "重要客户")
    private String description;

    @Schema(description = "所有者ID", type = "Integer", example = "101")
    @NotNull(message = "所有者ID不能为空")
    private Integer ownerId;

    @Schema(description = "所有者部门ID", type = "String", example = "dept_123")
    @NotNull(message = "所有者部门ID不能为空")
    private String ownerDepartmentId;

    @Schema(description = "账户状态(字典0109)", type = "String", example = "0109-1")
    private String accountStatus;

    @Schema(description = "停用原因(字典0110)", type = "String", example = "0110-1")
    private String disableReason;

    @Schema(description = "计价方式(字典0119)", type = "String", example = "0119-1")
    private String pricingMethod;

    @Schema(description = "调剂比例", type = "Integer", example = "10")
    @Min(value = 0, message = "调剂比例不能小于0")
    @Max(value = 10, message = "调剂比例不能大于10")
    private Integer adjustmentRatio;
} 