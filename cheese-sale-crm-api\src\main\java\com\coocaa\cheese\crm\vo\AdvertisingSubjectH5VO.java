package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 签约主体H5VO
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Data
@Accessors(chain = true)
@Schema(name = "AdvertisingSubjectH5VO", description = "签约主体H5VO")
public class AdvertisingSubjectH5VO {
    @Schema(description = "主键ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "投放公司Id", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY, target = "companyName")
    private Integer companyId;

    @Schema(description = "签约主体名称", type = "String", example = "XX主体")
    private String companyName;

    @Schema(description = "品牌ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "是否禁用(不可选)", type = "Boolean", example = "false")
    private Boolean disabled;

    @Schema(description = "禁用原因", type = "String", example = "48小时保护期内")
    private String disabledReason;

    @Schema(description = "是否公海保护期 [0:否, 1:是]", type = "Integer", example = "1")
    private Integer protectionPeriodFlag;

    @Schema(description = "创建人", type = "Integer", example = "创建人")
    private Integer creator;

    @Schema(description = "生效时间")
    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    private LocalDateTime effectiveTime;

    @Schema(description = "产品线名称列表", type = "String")
    private String productLineNames;

    @Schema(description = "主体按钮对象")
    @TransField
    private SubjectButtonVO subjectButtonVO;
}