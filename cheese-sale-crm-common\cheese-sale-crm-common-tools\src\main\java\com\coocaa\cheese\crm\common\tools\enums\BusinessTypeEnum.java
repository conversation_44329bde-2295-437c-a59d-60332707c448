package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 业务类型枚举(1:合同 2:定版单)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum implements IEnumType<Integer> {
    CONTRACT(1, "合同"),
    EVIDENCE(2, "定版单");

    private static final Map<Integer, BusinessTypeEnum> BY_CODE_MAP =
            Arrays.stream(BusinessTypeEnum.values())
                    .collect(Collectors.toMap(BusinessTypeEnum::getCode, item -> item));
    private final Integer code;
    private final String desc;

    /**
     * 将代码转成枚举
     */
    public static BusinessTypeEnum parse(Integer code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static BusinessTypeEnum parse(Integer code, BusinessTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(Integer code) {
        return Optional.ofNullable(parse(code)).map(BusinessTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 