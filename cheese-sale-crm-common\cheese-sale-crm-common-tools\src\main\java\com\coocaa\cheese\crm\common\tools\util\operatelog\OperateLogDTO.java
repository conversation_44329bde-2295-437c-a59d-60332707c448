package com.coocaa.cheese.crm.common.tools.util.operatelog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-04
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class OperateLogDTO implements IOperateLog {
    /**
     * 功能名称
     */
    private String functionName;

    /**
     * 实体名称 (数据字典中配置)
     */
    private String entityCode;

    /**
     * 实体ID
     */
    private Integer entityId;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 操作人
     */
    private Integer operator;
}
