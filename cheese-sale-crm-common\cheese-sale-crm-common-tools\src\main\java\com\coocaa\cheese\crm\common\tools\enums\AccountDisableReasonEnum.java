package com.coocaa.cheese.crm.common.tools.enums;

import com.coocaa.ad.common.enums.IEnumType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售-账户停用原因枚举(字典0110)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-28
 */
@Getter
@AllArgsConstructor
public enum AccountDisableReasonEnum implements IEnumType<String> {
    AUTHORIZATION_EXPIRED("0110-1", "授权到期"),
    COOPERATION_TERMINATED("0110-2", "合作终止"),
    PARTNER_BREACH("0110-3", "合作方违约"),
    OTHER_REASON("0110-4", "其它原因");

    private static final Map<String, AccountDisableReasonEnum> BY_CODE_MAP =
            Arrays.stream(AccountDisableReasonEnum.values())
                    .collect(Collectors.toMap(AccountDisableReasonEnum::getCode, item -> item));
    private final String code;
    private final String desc;

    /**
     * 将代码转成枚举
     */
    public static AccountDisableReasonEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code         代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static AccountDisableReasonEnum parse(String code, AccountDisableReasonEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(AccountDisableReasonEnum::getDesc).orElse(StringUtils.EMPTY);
    }
} 