package com.coocaa.cheese.crm.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 机构合同查询参数
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构合同查询参数")
public class InstitutionContractQueryParam {
    @Schema(description = "机构账户ID", type = "Integer", example = "1")
    private Integer institutionId;

    @Schema(description = "合同编号", type = "String", example = "CT20250101001")
    private String contractCode;

    @Schema(description = "是否生效", type = "Integer", example = "1")
    private Integer effectiveFlag;
} 