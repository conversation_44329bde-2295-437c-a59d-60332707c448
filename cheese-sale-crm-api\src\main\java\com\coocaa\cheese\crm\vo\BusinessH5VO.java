package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商机信息VO
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Accessors(chain = true)
@Schema(name = "BusinessH5VO", description = "商机信息VO")
public class BusinessH5VO {
    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "渠道ID")
    @TransField(type = TransTypes.CHANNEL)
    private Integer channelId;

    @Schema(description = "渠道名称", type = "String", example = "直营渠道")
    private String channelName;

    @Schema(description = "品牌ID")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;

    @Schema(description = "品牌名称", type = "String", example = "创维")
    private String brandName;

    @Schema(description = "归属人ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer ownerId;
    private String ownerName;

    @Schema(description = "管理部门ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "是否TOP客户 [0:否, 1:是]", type = "Integer", example = "1")
    private Integer topFlag;

    @Schema(description = "签约主体公司ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;

    @Schema(description = "签约主体名称-关联查询的公司名称", type = "String", example = "创维集团")
    private String companyName;

    @Schema(description = "商机编码", example = "BIZ001")
    private String code;

    @Schema(description = "商机进度(字典0073)", type = "String", example = "0073-1")
    @TransField(type = TransTypes.DICT)
    private String progress;
    private String progressName;

    @Schema(description = "商机状态(字典0074)", type = "String", example = "0074-1")
    @TransField(type = TransTypes.DICT)
    private String status;
    private String statusName;

    @Schema(description = "签约主体ID")
    private Integer advertisingSubjectId;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "主体绑定日期")
    private LocalDate advertisingBindDate;

    @JsonFormat(pattern = Constants.DATE_FORMAT)
    @Schema(description = "主体释放日期")
    private LocalDate advertisingReleaseDate;

    @Schema(description = "商机绑定状态 [0:未绑定, 1:已绑定]", type = "Integer", example = "1")
    private Integer advertisingBindFlag;

    @Schema(description = "商机释放状态 [0:保护中, 1:已释放]", type = "Integer", example = "0")
    private Integer advertisingReleaseFlag;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "分配时间")
    private LocalDateTime assignTime;

    @Schema(description = "备注说明", maxLength = 50)
    private String description;

    @Schema(description = "产品线", type = "String", example = "1")
    private String productLineNames;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "创建时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime createTime;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "操作人")
    @TransField(type = TransTypes.USER)
    private Integer operator;

    @Schema(description = "操作人姓名", type = "String", example = "张三")
    private String operatorName;
} 