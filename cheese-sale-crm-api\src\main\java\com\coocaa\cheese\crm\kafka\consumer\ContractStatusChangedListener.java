package com.coocaa.cheese.crm.kafka.consumer;

import com.alibaba.fastjson2.JSONObject;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.cheese.crm.common.tools.enums.BusinessTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.ContractStatusEnum;
import com.coocaa.cheese.crm.common.tools.enums.ContractTypeEnum;
import com.coocaa.cheese.crm.common.tools.enums.EvidenceStatusEnum;
import com.coocaa.cheese.crm.kafka.constant.KafkaConstants;
import com.coocaa.cheese.crm.service.BusinessFollowService;
import com.coocaa.cheese.crm.vo.ContractStatusChangeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 合同状态变更监听器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-05
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractStatusChangedListener {
    private final IBusinessService businessService;
    private final BusinessFollowService businessFollowService;

    /**
     * 合同状态变更，修改商机状态
     * 商机关联的《合同》状态 [待发起合同（0079-3）]时，若【商机进度】=初始或跟进中，则维护至 [合同推进]
     * 类型≠零元赠播的《合同》状态 [已签约（待执行）]时，若【商机进度】=初始或跟进中或合同推进，则维护至 [已签约]
     */
    @KafkaListener(topics = KafkaConstants.TOPIC_CONTRACT_STATUS_CHANGE,
            id = KafkaConstants.ID_CONTRACT_STATUS_CHANGE,
            containerFactory = KafkaConstants.KAFKA_LISTENER_CONTAINER_FACTORY_BATCH)
    public void getMessage(List<ConsumerRecord<?, String>> objList, Acknowledgment ack) {
        log.info("Topic:{},消费了{}条消息", KafkaConstants.TOPIC_CONTRACT_STATUS_CHANGE, objList.size());
        try {
            objList.forEach(obj -> {
                Optional<String> message = Optional.ofNullable(obj.value());
                if (message.isEmpty()) {
                    // 消息不存在的情况
                    log.error("消息格式不正确:{}", obj);
                    return;
                }
                String msg = message.get();
                // 使用转换器解析消息
                ContractStatusChangeVO vo = JSONObject.parseObject(msg, ContractStatusChangeVO.class);
                if (Objects.isNull(vo.getBusinessId()) || StringUtils.isBlank(vo.getStatus())) {
                    log.warn("合同状态变更信息为空, 忽略处理");
                    return;
                }
                log.info("当前消费businessId：{}", vo.getBusinessId());
                if (BusinessTypeEnum.CONTRACT.getCode().equals(vo.getNotifyStatusType())) {
                    // 商机关联的《合同》状态 [待发起合同（0079-3）]时，若【商机进度】=初始或跟进中，则维护至 [合同推进]
                    if (ContractStatusEnum.TREAT_INITIATE_CONTRACT.getCode().equals(vo.getStatus())) {
                        // 更新进度状态
                        businessFollowService.updateBusinessProgress(vo.getBusinessId(), vo.getStatus(), null);
                    }
                    // 类型≠零元赠播的《合同》状态 [已签约（待执行）]时，若【商机进度】=初始或跟进中或合同推进，则维护至 [已签约]
                    else if ((ContractStatusEnum.ALREADY_SIGN.getCode().equals(vo.getStatus()) || ContractStatusEnum.IN_PROGRESS.getCode().equals(vo.getStatus()))
                            && !ContractTypeEnum.ZERO_GIFT_PLAY.getCode().equals(vo.getContractType())) {
                        // 更新进度状态
                        businessFollowService.updateBusinessProgress(vo.getBusinessId(), vo.getStatus(), vo.getContractType());
                    }
                } else if (BusinessTypeEnum.EVIDENCE.getCode().equals(vo.getNotifyStatusType())) {
                    //该类合同下的任一 定版单已客户确认时
                    if (EvidenceStatusEnum.CONFIRMED.getCode().equals(vo.getStatus()) && !ContractTypeEnum.ZERO_GIFT_PLAY.getCode().equals(vo.getContractType())) {
                        // 更新进度状态
                        businessFollowService.updateBusinessProgress(vo.getBusinessId(), vo.getStatus(), vo.getContractType());
                    }
                }
            });
        } catch (Exception e) {
            log.error("消费失败:{}", e.getMessage(), e);
        } finally {
            ack.acknowledge();
        }
    }
}
