package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-11
 */
@Data
public class DashboardCustomerReportDetailVO {

    @Schema(description = "所属公司ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.COMPANY)
    private Integer companyId;
    private String companyName;

    @Schema(description = "用户ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.USER)
    private Integer userId;
    private String userName;

    @Schema(description = "管理部门ID", type = "String", example = "1")
    @TransField(type = TransTypes.DEPARTMENT)
    private String departmentId;
    private String departmentName;

    @Schema(description = "品牌ID", type = "Integer", example = "1")
    @TransField(type = TransTypes.BRAND)
    private Integer brandId;
    private String brandName;

    @Schema(description = "产品线", type = "String", example = "1")
    private String productLine;

    @JsonFormat(pattern = Constants.DATE_TIME_FORMAT)
    @Schema(description = "创建时间", type = "String", example = "2025-02-19 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "审批状态(字典0147)", type = "String", example = "0147-1")
    @TransField(type = TransTypes.DICT)
    private String result;
    private String resultName;

    @Schema(description = "是否创建商机(1是，0否)", type = "Integer", example = "1")
    private Integer isCreateBusiness;

}
