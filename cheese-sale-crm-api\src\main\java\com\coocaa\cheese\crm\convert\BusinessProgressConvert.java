package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.BusinessProgressParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessProgressEntity;
import com.coocaa.cheese.crm.vo.BusinessProgressVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 商机进度转换类
 *
 * <AUTHOR>
 * @since 2025/5/13
 */

@Mapper
public interface BusinessProgressConvert extends PageableConvert<BusinessProgressEntity, BusinessProgressVO> {
    BusinessProgressConvert INSTANCE = Mappers.getMapper(BusinessProgressConvert.class);

    /**
     * 将实体转换成VO
     */
    BusinessProgressVO toVo(BusinessProgressEntity entity);

    /**
     * 将VO转换成实体
     */
    BusinessProgressEntity toEntity(BusinessProgressVO vo);

    /**
     * 将参数转换成实体
     */
    BusinessProgressEntity toEntity(BusinessProgressParam param);
}
