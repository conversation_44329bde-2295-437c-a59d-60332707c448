package com.coocaa.cheese.crm.service;

import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.exception.BusinessException;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.coocaa.cheese.crm.bean.BusinessProgressParam;
import com.coocaa.cheese.crm.common.db.entity.BusinessEntity;
import com.coocaa.cheese.crm.common.db.entity.BusinessProgressEntity;
import com.coocaa.cheese.crm.common.db.service.IBusinessProgressService;
import com.coocaa.cheese.crm.common.db.service.IBusinessService;
import com.coocaa.ad.common.enums.BooleFlagEnum;
import com.coocaa.cheese.crm.common.tools.enums.BusinessProgressEnum;
import com.coocaa.cheese.crm.common.tools.enums.ProgressTypeEnum;
import com.coocaa.cheese.crm.convert.BusinessProgressConvert;
import com.coocaa.cheese.crm.vo.BusinessProgressVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 商机进度服务类
 * <AUTHOR>
 * @version 1.0
 * @since  2025/5/12
 */

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BusinessProgressService {
    private final IBusinessProgressService businessProgressService;
    private final IBusinessService businessService;
    private final BusinessFollowService businessFollowService;

    /**
     * 获取商机进度详情
     * @param id 进度id
     * @return 商机进度详情
     */
    @AutoTranslate
    public BusinessProgressVO getDetail(Integer id) {
        return Optional.ofNullable(businessProgressService.getById(id))
                .map(BusinessProgressConvert.INSTANCE::toVo)
                .orElse(new BusinessProgressVO());
    }

    /**
     * 创建或更新商机进度
     * @param param 进度参数
     * @return 创建或更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOrUpdate(BusinessProgressParam param) {
        // 校验参数是否合法
        checkParam(param);
        // 若预算填写金额＞0，此时当【商机进度】=初步接洽，则维护商机=【高意向】
        if (param.getBudgetAmount() != null && param.getBudgetAmount() > 0) {
            BusinessEntity businessEntity = businessService.getById(param.getBusinessId());
            if (BusinessProgressEnum.INITIAL.getCode().equals(businessEntity.getProgress())) {
                businessEntity.setUpdateTime(LocalDateTime.now());
                businessEntity.setOperator(UserThreadLocal.getUserId());
                businessFollowService.updateBusinessProgressAndProtection(businessEntity, BusinessProgressEnum.FOLLOWING.getCode());
            }
        }
        BusinessProgressEntity entity = BusinessProgressConvert.INSTANCE.toEntity(param);
        return businessProgressService.saveOrUpdate(entity);
    }

    /**
     * 校验参数
     * @param param 进度参数
     */
    private void checkParam(BusinessProgressParam param) {
        if (ProgressTypeEnum.BUDGET.equals(param.getProgressType())) {
            if (param.getBudgetAmount() == null || param.getBudgetAmount() < 0) {
                throw new BusinessException("预算金额必须大于等于0");
            }
            // 若【预算金额】＞0，再次点击编辑允许修改，但此时值不能≤0 （因为有预算会触发商机进度变更）
            if (param.getId() != null) {
                BusinessProgressEntity oldEntity = businessProgressService.getById(param.getId());
                if (oldEntity.getBudgetAmount() > 0 && param.getBudgetAmount() <= 0) {
                    throw new BusinessException("预算金额必须大于0");
                }
            }
        } else {
            if (param.getProgressStatus() == null) {
                throw new BusinessException("进度状态不能为空");
            }
        }
        if (StringUtils.isBlank(param.getDetailDesc()) || param.getDetailDesc().length() > 50) {
            throw new BusinessException("细节描述不能为空且长度不超过50");
        }
    }

    /**
     * 删除客户意向
     * @param id 进度id
     * @return 删除结果
     */
    public boolean delete(Integer id) {
        return businessProgressService.lambdaUpdate()
                .set(BusinessProgressEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(BusinessProgressEntity::getId, id)
                .update();
    }

    /**
     * 根据商机id查询商机进度列表
     * @param id 商机id
     * @return 商机进度列表
     */
    public List<BusinessProgressVO> listByBusinessId(Integer id) {
        List<BusinessProgressEntity> entities = businessProgressService.lambdaQuery()
                .eq(BusinessProgressEntity::getBusinessId, id)
                .eq(BusinessProgressEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();

        return CollectionUtils.isEmpty(entities)
                ? Collections.emptyList()
                : entities.stream()
                .map(BusinessProgressConvert.INSTANCE::toVo)
                .collect(Collectors.toList());
    }
}
