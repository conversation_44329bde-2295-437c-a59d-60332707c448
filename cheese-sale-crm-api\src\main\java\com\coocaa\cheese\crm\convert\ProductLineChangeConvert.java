package com.coocaa.cheese.crm.convert;

import com.coocaa.cheese.crm.bean.ProductLineChangeParam;
import com.coocaa.cheese.crm.bean.ProductLineInitiateParam;
import com.coocaa.cheese.crm.common.db.bean.InnerProductApproveDetailDTO;
import com.coocaa.cheese.crm.common.db.entity.ProductLineChangeEntity;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveApplyVO;
import com.coocaa.cheese.crm.rpc.vo.InnerApproveTaskVO;
import com.coocaa.cheese.crm.vo.InnerApproveProductApplyPageVO;
import com.coocaa.cheese.crm.vo.InnerProductApproveDetailVO;
import com.coocaa.cheese.crm.vo.InnerProductApproveTaskPageVO;
import com.coocaa.cheese.crm.vo.ProductLineChangeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 产品线变更记录表 信息转换
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-6-17
 */
@Mapper
public interface ProductLineChangeConvert extends PageableConvert<ProductLineChangeEntity, ProductLineChangeVO> {
    ProductLineChangeConvert INSTANCE = Mappers.getMapper(ProductLineChangeConvert.class);

    /**
     * Entity 转 VO
     */
    ProductLineChangeVO toVo(ProductLineChangeEntity entity);

    /**
     * VO 转 Entity
     */
    ProductLineChangeEntity toEntity(ProductLineChangeVO vo);

    /**
     * Param 转 Entity
     */
    ProductLineChangeEntity toEntity(ProductLineChangeParam param);

    /**
     * Param 转 Entity
     */
    ProductLineChangeEntity toEntity(ProductLineInitiateParam param);

    /**
     * vo 转 VO
     */
    InnerProductApproveTaskPageVO toVo(InnerApproveTaskVO vo);

    /**
     * dto 转 VO
     */
    InnerProductApproveDetailVO toVo(InnerProductApproveDetailDTO dto);

    /**
     * vo 转 VO
     */
    InnerApproveProductApplyPageVO toVo(InnerApproveApplyVO vo);
}