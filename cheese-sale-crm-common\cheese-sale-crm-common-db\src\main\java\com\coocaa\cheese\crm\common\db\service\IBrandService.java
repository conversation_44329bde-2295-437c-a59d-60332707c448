package com.coocaa.cheese.crm.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.cheese.crm.common.db.bean.BrandQueryDTO;
import com.coocaa.cheese.crm.common.db.entity.BrandEntity;

/**
 * 主品牌信息 服务类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface IBrandService extends IService<BrandEntity> {
    /**
     * 分页查询品牌列表
     *
     * @param page      分页条件
     * @param condition 查询条件
     * @return 品牌数据列表
     */
    IPage<BrandEntity> pageList(IPage<BrandEntity> page, BrandQueryDTO condition);

    /**
     * 分页查询品牌列表
     *
     * @param page      分页条件
     * @param condition 查询条件
     * @return 品牌数据列表
     */
    IPage<BrandEntity> h5PageList(IPage<BrandEntity> page, BrandQueryDTO condition);
}
