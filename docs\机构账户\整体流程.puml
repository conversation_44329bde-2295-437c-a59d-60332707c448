@startuml
title 机构账户管理功能接口调用逻辑

actor "销管内勤" as Admin
actor "BD人员" as BD
actor "外部系统" as ExternalSystem
participant "前端页面" as Frontend
participant "机构账户管理\n/api/crm/institution-account/*" as AccountController
participant "合同管理\n/api/crm/institution-contract/*" as ContractController
participant "资质管理\n/api/crm/institution-qualification/*" as QualificationController
participant "资金账户管理\n/api/crm/institution-fund-account/*" as AccountFundController
participant "折扣管理\n/api/crm/discount-rule/*" as DiscountRuleController
participant "外部接口\n/api/external-project-fund/*" as ExternalApiController
participant "账户定时任务" as AutoCloseInstitutionTask
participant "折扣定时任务" as DiscountRuleEffectiveTask

== 机构账户管理 ==

Admin -> Frontend: 访问机构账户列表页面
Frontend -> AccountController: POST /api/crm/institution-account/list
note right: 查询机构账户列表
AccountController --> Frontend: 返回账户列表数据-返回所有数据合集
Frontend --> Admin: 展示账户列表

Admin -> Frontend: 点击"开户"按钮
Frontend -> Frontend: 显示开户表单
Admin -> Frontend: 填写开户信息
Frontend -> AccountController: POST /api/crm/institution-account
note right: 创建机构账户
AccountController --> Frontend: 返回开户结果
Frontend --> Admin: 显示开户成功提示

Admin -> Frontend: 选择账户并点击"转移"按钮
Frontend -> Frontend: 显示转移表单
Admin -> Frontend: 选择转移目标部门和人员
Frontend -> AccountController: POST /api/crm/institution-account/transfer
note right: 转移机构账户
AccountController --> Frontend: 返回转移结果
Frontend --> Admin: 显示转移成功提示

Admin -> Frontend: 点击账户查看详情
Frontend -> AccountController: GET /api/crm/institution-account/{accountId}
note right: 获取账户详情
AccountController --> Frontend: 返回账户详情
Frontend --> Admin: 展示账户详情

Admin -> Frontend: 修改授权信息
Frontend -> AccountController: put /api/crm/institution-account
note right: 更新授权信息
AccountController --> Frontend: 返回更新结果
Frontend --> Admin: 显示更新成功提示
== 合同管理 ==
Admin -> Frontend: 访问合同管理页面
Frontend -> ContractController: GET /api/institution-contract/{institutionId}/list
note right: 查询合同列表
QualificationController --> Frontend: 返回合同列表
Frontend --> Admin: 展示合同列表

Admin -> Frontend: 点击"上传合同"按钮
Frontend -> Frontend: 显示上传表单
Admin -> Frontend: 填写合同信息并上传文件
Frontend -> ContractController: POST /api/crm/institution-contract
note right: 上传合同
ContractController --> Frontend: 返回上传结果
Frontend --> Admin: 显示上传成功提示

Admin -> Frontend: 点击"设为当前生效"按钮
Frontend -> ContractController: PUT /api/crm/institution-contract/{contractId}/effective
note right: 设置当前生效合同
ContractController --> Frontend: 返回设置结果
Frontend --> Admin: 显示设置成功提示

Admin -> Frontend: 点击查看合同详情
Frontend -> ContractController: GET /api/crm/institution-contract/{contractId}
note right: 获取合同详情
ContractController --> Frontend: 返回合同详情-带附件URL
Frontend --> Admin: 展示合同详情

== 资质管理 ==
Admin -> Frontend: 访问资质管理页面
Frontend -> ContractController: GET /api/institution-qualification/{institutionId}/list
note right: 查询资质列表
QualificationController --> Frontend: 返回资质列表
Frontend --> Admin: 展示资质列表

Admin -> Frontend: 点击"上传资质"按钮
Frontend -> Frontend: 显示上传表单
Admin -> Frontend: 填写资质信息并上传文件
Frontend -> QualificationController: POST /api/crm/institution-qualification
note right: 上传资质
QualificationController --> Frontend: 返回上传结果
Frontend --> Admin: 显示上传成功提示

Admin -> Frontend: 点击查看资质详情
Frontend -> QualificationController: GET /api/crm/institution-qualification/{qualificationId}
note right: 获取资质详情
QualificationController --> Frontend: 返回资质详情-带附件URL
Frontend --> Admin: 展示资质详情

== 资金账户管理 ==
Admin -> Frontend: 访问资金账户管理页面
Frontend -> ContractController: GET /api/institution-fund-account/{institutionId}/list
note right: 查询资金账户列表
QualificationController --> Frontend: 返回资金账户列表
Frontend --> Admin: 展示资金账户列表

Admin -> Frontend: 访问资金账户页面
Frontend -> AccountFundController: GET /api/crm/institution-fund-account/{institutionId}
note right: 获取资金账户详情
AccountFundController --> Frontend: 返回账户详情
Frontend --> Admin: 展示账户详情

Admin -> Frontend: 点击"增加资金"按钮
Frontend -> Frontend: 显示增加资金表单
Admin -> Frontend: 填写增加资金信息
Frontend -> AccountFundController: POST /api/crm/institution-fund-account/increase
note right: 增加资金
AccountFundController --> Frontend: 返回操作结果
Frontend --> Admin: 显示操作成功提示

Admin -> Frontend: 点击"冻结资金"按钮
Frontend -> Frontend: 显示冻结资金表单
Admin -> Frontend: 填写冻结资金信息-人工接口  系统创建的走服务
Frontend -> AccountFundController: POST /api/crm/institution-fund-account/amount-change
note right: 冻结资金
AccountFundController --> Frontend: 返回操作结果
Frontend --> Admin: 显示操作成功提示


Admin -> Frontend: 点击"扣减资金"按钮
Frontend -> Frontend: 显示扣减资金表单
Admin -> Frontend: 填写扣减资金信息
Frontend -> AccountFundController: POST /api/crm/institution-fund-account/amount-change
note right: 扣减资金
AccountFundController --> Frontend: 返回操作结果
Frontend --> Admin: 显示操作成功提示

Admin -> Frontend: 点击"冻结转扣费\解除解冻"按钮
Frontend -> Frontend: 显示冻结转扣费\解除解冻表单
Admin -> Frontend: 填写冻结转扣费\解除解冻信息
Frontend -> AccountFundController: POST /api/crm/institution-fund-account/freeze-process
note right: 冻结转扣费
AccountFundController --> Frontend: 返回操作结果
Frontend --> Admin: 显示操作成功提示

Admin -> Frontend: 查看资金变动记录
Frontend -> AccountFundController: POST /api/crm/institution-fund-account/change-records
note right: 查询资金变动记录
AccountFundController --> Frontend: 返回变动记录
Frontend --> Admin: 展示变动记录

Admin -> Frontend: 查看刊例费报价详情
Frontend -> AccountFundController: GET /api/crm/institution-fund-account/listing-fee/{fundChangeRecordId}
note right: 获取刊例费报价详情
AccountFundController --> Frontend: 返回报价详情
Frontend --> Admin: 展示报价详情

== 折扣管理 ==

Admin -> Frontend: 访问折扣规则列表页面
Frontend -> DiscountRuleController: POST /api/crm/discount-rule/list
note right: 查询折扣规则列表
DiscountRuleController --> Frontend: 返回规则列表
Frontend --> Admin: 展示规则列表

Admin -> Frontend: 点击"创建折扣规则"按钮
Frontend -> Frontend: 显示创建表单
Admin -> Frontend: 填写规则信息
Frontend -> DiscountRuleController: POST /api/crm/discount-rule
note right: 创建折扣规则
DiscountRuleController --> Frontend: 返回创建结果
Frontend --> Admin: 显示创建成功提示

Admin -> Frontend: 点击"编辑折扣规则"按钮
Frontend -> Frontend: 显示编辑表单
Admin -> Frontend: 修改规则信息
Frontend -> DiscountRuleController: PUT /api/crm/discount-rule
note right: 更新折扣规则
DiscountRuleController --> Frontend: 返回更新结果
Frontend --> Admin: 显示更新成功提示

Admin -> Frontend: 点击查看规则详情
Frontend -> DiscountRuleController: GET /api/crm/discount-rule/{ruleId}
note right: 获取规则详情
DiscountRuleController --> Frontend: 返回规则详情
Frontend --> Admin: 展示规则详情

Admin -> Frontend: 点击"删除规则"按钮
Frontend -> DiscountRuleController: DELETE /api/crm/discount-rule/{ruleId}
note right: 删除折扣规则
DiscountRuleController --> Frontend: 返回删除结果
Frontend --> Admin: 显示删除成功提示

Admin -> Frontend: 折扣计算接口
Frontend -> DiscountRuleController: POST /api/crm/discount-rule/calculate-discount
note right: 折扣计算接口
DiscountRuleController --> Frontend: 返回机构折扣默认1.4
Frontend --> Admin: 折扣计算接口

Admin -> Frontend: 折扣试算接口-可以和计算接口合并，但是需要增加判断来获取数据来源
Frontend -> DiscountRuleController: POST /api/crm/discount-rule/trial
note right: 折扣试算
DiscountRuleController --> Frontend: 返回机构折扣默认1.4
Frontend --> Admin: 机构折扣


== 外部系统接口调用 ==

ExternalSystem -> ExternalApiController: POST /api/external-project-fund/freeze
note right: 冻结投放方案资金
ExternalApiController -> AccountFundController: 调用内部冻结接口
AccountFundController -> DiscountRuleController: POST /api/crm/discount-rule/calculate
note right: 计算适用折扣
DiscountRuleController --> AccountFundController: 返回折扣结果
AccountFundController --> ExternalApiController: 返回冻结结果
ExternalApiController --> ExternalSystem: 返回冻结结果

ExternalSystem -> ExternalApiController: POST /api/external-project-fund/unfreeze
note right: 解冻投放方案资金
ExternalApiController -> AccountFundController: 调用内部解冻接口
AccountFundController --> ExternalApiController: 返回解冻结果
ExternalApiController --> ExternalSystem: 返回解冻结果

ExternalSystem -> ExternalApiController: POST /api/external-project-fund/freeze-to-deduct
note right: 投放方案资金冻结转扣费
ExternalApiController -> AccountFundController: 调用内部冻结转扣费接口
AccountFundController --> ExternalApiController: 返回扣费结果
ExternalApiController --> ExternalSystem: 返回扣费结果

== 系统自动关户 ==

AutoCloseInstitutionTask -> AutoCloseInstitutionTask: 每日凌晨触发
AutoCloseInstitutionTask -> AutoCloseInstitutionTask: 执行autoCloseInstitutionJob方法
note right: 系统自动关户
AccountController -> AccountController: 查询授权到期的账户
AccountController -> AccountController: 批量更新账户状态
AccountController --> AutoCloseInstitutionTask: 打印执行日志

== 折扣规则自动生效 ==

DiscountRuleEffectiveTask -> DiscountRuleEffectiveTask: 每日凌晨触发
DiscountRuleEffectiveTask -> DiscountRuleEffectiveTask: 执行discountRuleEffectiveJob方法
note right: 折扣规则自动生效
DiscountRuleController -> DiscountRuleController: 批量更新已到生效时间的策略状态
DiscountRuleController --> DiscountRuleEffectiveTask: 打印执行日志

@enduml