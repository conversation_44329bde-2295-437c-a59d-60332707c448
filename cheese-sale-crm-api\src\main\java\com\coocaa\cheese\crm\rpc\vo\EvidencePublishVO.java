package com.coocaa.cheese.crm.rpc.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
/**
 * 刊例明细返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02
 */
@Data
public class EvidencePublishVO {
    @Schema(description = "id", type = "Integer", example = "1")
    private Integer id;
    @Schema(description = "定版单id", type = "Integer", example = "2")
    private Integer evidenceId;
    /**
     * 城市id
     */
    @Schema(description = "城市id", type = "Integer", example = "城市id")
    @TransField(type = TransTypes.CITY)
    private Integer cityId;

    @Schema(description = "城市名称", type = "String", example = "城市名称")
    private String cityName;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称", type = "String", example = "项目名称")
    private String projectName;

    /**
     * 点位数量
     */
    @Schema(description = "点位数量", type = "Integer", example = "点位数量")
    private Integer pointCount;

    /**
     * 时长字典
     */
    @Schema(description = "时长字典", type = "String", example = "时长字典")
    @TransField(type = TransTypes.DICT)
    private String duration;
    @Schema(description = "时长", type = "String", example = "时长")
    private String durationName;

    /**
     * 频次字典
     */
    @Schema(description = "频次字典", type = "String", example = "频次字典")
    @TransField(type = TransTypes.DICT)
    private String frequency;
    @Schema(description = "频次", type = "String", example = "频次")
    private String frequencyName;

    /**
     * 计费方式字典(按天、按周)
     */
    @Schema(description = "计费方式字典", type = "String", example = "计费方式字典")
    @TransField(type = TransTypes.DICT)
    private String billingType;
    @Schema(description = "计费方式", type = "String", example = "计费方式")
    private String billingTypeName;

    /**
     * 价格
     */
    @Schema(description = "价格", type = "BigDecimal", example = "价格")
    private BigDecimal price;

    /**
     * 投放周期（XX天或XX周）
     */
    @Schema(description = "投放周期", type = "Integer", example = "投放周期")
    private Integer launchCycle;

    /**
     * 折前总价
     */
    @Schema(description = "折前总价", type = "BigDecimal", example = "折前总价")
    private BigDecimal totalPrice;

    /**
     * 方案id
     */
    @Schema(description = "方案id", type = "Integer", example = "方案id")
    private Integer planId;

    /**
     * 方案名称
     */
    @Schema(description = "方案名称", type = "String", example = "方案名称")
    private String planName;

    /**
     * 订单id
     */
    @Schema(description = "订单id", type = "Integer", example = "订单id")
    private Integer orderId;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号", type = "String", example = "订单编号")
    private String orderCode;
}
