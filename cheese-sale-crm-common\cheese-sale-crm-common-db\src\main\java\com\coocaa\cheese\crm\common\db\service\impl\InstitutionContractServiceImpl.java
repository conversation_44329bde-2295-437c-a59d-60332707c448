package com.coocaa.cheese.crm.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.cheese.crm.common.db.entity.InstitutionContractEntity;
import com.coocaa.cheese.crm.common.db.mapper.InstitutionContractMapper;
import com.coocaa.cheese.crm.common.db.service.IInstitutionContractService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 机构合同 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class InstitutionContractServiceImpl
        extends ServiceImpl<InstitutionContractMapper, InstitutionContractEntity>
        implements IInstitutionContractService {
} 