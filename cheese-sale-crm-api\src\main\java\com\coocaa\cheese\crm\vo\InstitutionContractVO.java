package com.coocaa.cheese.crm.vo;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import com.coocaa.cheese.crm.common.tools.constant.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 机构合同视图对象
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
@Schema(description = "机构合同视图对象")
public class InstitutionContractVO {
    @Schema(description = "合同ID")
    private Integer id;

    @Schema(description = "机构账户ID")
    private Integer institutionId;

    @Schema(description = "合同编号")
    private String contractCode;

    @Schema(description = "签约日期")
    @JsonFormat(pattern = Constants.DATE_FORMAT)
    private LocalDate signDate;

    @Schema(description = "合同附件URL")
    private String fileUrl;

    @Schema(description = "当前生效")
    private Integer effectiveFlag;

    @Schema(description = "备注说明")
    private String description;

    @Schema(description = "维护人ID")
    @TransField(type = TransTypes.USER)
    private Integer operator;
    private String operatorName;

    @Schema(description = "上传时间")
    private LocalDateTime updateTime;

} 